.holaSoyTikiSolo {
  margin: 0;
  width: 1038px;
  position: relative;
  display: inline-block;
}
.text {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}
.holaSoyTikiSolo1 {
  width: 1038px;
  position: relative;
  display: inline-block;
}
.text1,
.textParent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
}
.text1 {
  align-items: center;
  justify-content: flex-end;
  font-size: 16px;
}
.textParent {
  align-items: flex-start;
  justify-content: flex-start;
  gap: 10px;
}
.instanceParent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 20px;
}
.serviceContent {
  flex: 1;
  border-radius: 40px;
  justify-content: center;
  padding: 0 10px;
  box-sizing: border-box;
  gap: 30px;
  min-width: 459px;
}
.serviceContentParent {
  display: flex;
  gap: 20px;
  font-size: 24px;
  align-self: stretch;
  flex-direction: row;
  justify-content: flex-start;
  text-align: left;
  font-size: 20px;
  color: #10265f;
}
.bloqueDeTexto,
.bloqueDeTextoWrapper,
.sectionPlanes {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  max-width: 100%;
}
.bloqueDeTexto {
  align-items: center;
  gap: 30px;
}
.bloqueDeTextoWrapper,
.sectionPlanes {
  align-items: flex-start;
}
.sectionPlanes {
  border-radius: 18px;
  justify-content: center;
  padding: 20px 70px 40px;
  box-sizing: border-box;
}
.homepage,
.root {
  overflow: hidden;
  display: flex;
  align-items: center;
  max-width: 100%;
}
.homepage {
  width: 1429px;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  font-size: 36px;
  color: #333;
  font-family: Poppins;
}
.root {
  background-color: #fdfaff;
  flex-direction: row;
  justify-content: center;
}
.condicionesDeUso {
  position: relative;
  letter-spacing: 0.01em;
}
.footerLinks {
  align-self: stretch;
  flex: 1;
  /* overflow-x: auto; */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 26px;
}
.faq,
.footerLinksWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.footerLinksWrapper {
  width: auto;
  height: 69px;
  background-color: #fafafa;
  padding: 10px;
  box-sizing: border-box;
  text-align: center;
  font-size: 20px;
  color: #333;
  font-family: Poppins;
}
.faq {
  max-width: 100%;
  line-height: normal;
  letter-spacing: normal;
}
@media screen and (max-width: 1200px) {
  .serviceContentParent {
    flex-wrap: wrap;
  }
  .sectionPlanes {
    padding-top: 20px;
    padding-bottom: 26px;
    box-sizing: border-box;
  }
}
@media screen and (max-width: 1050px) {
  .holaSoyTikiSolo {
    font-size: 29px;
  }
}
@media screen and (max-width: 750px) {
  .holaSoyTikiSolo1 {
    width: auto;
  }
  .serviceContent {
    min-width: 100%;
  }
  .sectionPlanes {
    gap: 15px;
    padding-left: 35px;
    padding-right: 35px;
    padding-bottom: 20px;
    box-sizing: border-box;
  }
}
@media screen and (max-width: 450px) {
  .sectionPlanes {
    padding-left: 15px;
    padding-right: 15px;
  }
  .holaSoyTikiSolo {
    font-size: 22px;
  }
  .condicionesDeUso {
    font-size: 17px;
  }
  .footerLinks {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  .footerLinksWrapper {
    height: auto;
  }
}
