import React from "react";
import { Snackbar, Alert } from "@mui/material";
import styles from "./customSnackbar.module.css";

interface CustomSnackbarProps {
  open: boolean;
  message: string;
  severity: "success" | "error";
  onClose: () => void;
}

const CustomSnackbar: React.FC<CustomSnackbarProps> = ({
  open,
  message,
  severity,
  onClose,
}) => {
  return (
    <Snackbar
      open={open}
      autoHideDuration={6000} // Duración en milisegundos
      onClose={onClose}
      anchorOrigin={{ vertical: "bottom", horizontal: "right" }} // Posición del Snackbar
    >
      <Alert
        onClose={onClose}
        severity={severity}
        variant="filled"
        className={`${styles.customAlert} ${
          severity === "success" ? styles.success : styles.error
        }`}
      >
        {message}
      </Alert>
    </Snackbar>
  );
};

export default CustomSnackbar;
