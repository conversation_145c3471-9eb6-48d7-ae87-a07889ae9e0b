.holaSoyTikiSolo {
    align-self: stretch;
    position: relative;
    font-weight: 600;
}

.holaSoyTikisolo {
    align-self: stretch;
    position: relative;
    font-size: 16px;
    color: #828282;
}

.text {
    align-self: stretch;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.icons {
    width: 21.3px;
    position: relative;
    height: 21.3px;


}

.cotizar {
    width: 76px;
    position: relative;
    font-size: 16px;
    font-family: Poppins;
    color: #fff;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    flex-shrink: 0;
}

.icons1 {
    width: 21px;
    position: relative;
    max-height: 100%;
    object-fit: cover;
    display: none;
}

.button {
    cursor: pointer;
    border: none;
    padding: 16px 24px;
    background-color: #89c598;
    width: 256px;
    border-radius: 200px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    gap: 8px;
    min-width: 188px;
    text-transform: capitalize;
    color: #ffffff;
    font-weight: 400;
    font-size: 16px;
}

.icons2 {
    width: 21.3px;
    position: relative;
    height: 21.3px;
}

.button1 {
    cursor: pointer;
    border: none;
    padding: 16px 24px;
    background-color: #7abbd4;
    width: 256px;
    border-radius: 200px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    gap: 8px;
    min-width: 188px;
    text-transform: capitalize;
    color: #ffffff;
    font-weight: 400;
    font-size: 16px;
}

.button2 {
    cursor: pointer;
    border: none;
    padding: 16px 24px;
    background-color: #af8cc0;
    width: 256px;
    border-radius: 200px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    gap: 8px;
    min-width: 188px;
    text-transform: capitalize;
    color: #ffffff;
    font-weight: 400;
    font-size: 16px;
}

.image17Icon {
    width: 24.5px;
    position: relative;
    height: 25px;
    object-fit: cover;
}

.whatsapp {
    position: relative;
    font-weight: 600;
}

.buttonsContact {
    width: 256px;
    border-radius: 60px;
    background-color: #acd5b3;
    height: 90px;
    display: none;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 10px;
    box-sizing: border-box;
    gap: 10px;
}

.image18Icon {
    width: 28.5px;
    position: relative;
    height: 25px;
    object-fit: cover;
}

.buttonsContact1 {
    width: 256px;
    border-radius: 60px;
    background-color: #acc4d5;
    height: 90px;
    display: none;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 10px;
    box-sizing: border-box;
    gap: 10px;
}

.image19Icon {
    width: 31.5px;
    position: relative;
    height: 25px;
    object-fit: cover;
}

.buttonsContact2 {
    width: 256px;
    border-radius: 60px;
    background-color: #bfadd5;
    height: 90px;
    display: none;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 10px;
    box-sizing: border-box;
    gap: 10px;
}


.datos {
    align-self: stretch;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
}

.textParent {
    position: relative;
    top: calc(50% - 259.5px);
    border-radius: 18px;
    background-color: #fff;
    max-width: 1248px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 50px;
    box-sizing: border-box;
    gap: 60px;
    text-align: center;
    font-size: 22px;
    color: #333;
    font-family: Poppins;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
}

.sectionVerification {
    display: flex;
    width: 100%;
    padding: 0px 24px;
    justify-content: center;
    align-items: center;
    min-height: 90vh;
}