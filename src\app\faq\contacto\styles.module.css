.contactContainer {
  align-self: stretch;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 30px;
  gap: 30px;
  max-width: 563px;
  width: 100%;
  margin: 0 auto;
}
.contactTitle {
  align-self: stretch;
  position: relative;
  font-weight: 600;
}
.contactSubtext {
  align-self: stretch;
  position: relative;
}
.textSection {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 10px;
}
.contactTitleWrapper {
  width: 401px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.contactDescription {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  font-size: 16px;
  color: #333;
}
.buttonGroup {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  text-align: center;
  font-size: 16px;
}
.contactButtonIcon {
  width: 24px;
  position: relative;
  height: 24px;
  object-fit: cover;
}
.contactButton {
  flex: 1;
  border-radius: 200px;
  background-color: #fff;
  border: 2px solid #10265f;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16px 24px;
  gap: 8px;
  color: #10265f;
  transition: all 0.1s ease;
}
.contactButton:hover {
  border-color: #9878b3;
  color: #9878b3;
  cursor: pointer;
}
.contactButtonIcon {
  filter: invert(5%) sepia(81%) saturate(2331%) hue-rotate(217deg) brightness(94%) contrast(97%);
  transition: filter 0.1s ease;
}
.contactButton:hover .contactButtonIcon {
  filter: invert(50%) sepia(11%) saturate(1365%) hue-rotate(230deg) brightness(93%) contrast(90%);
}
.contactButtonLabel {
  position: relative;
}

/* Media queries para pantallas más pequeñas */
@media screen and (max-width: 768px) {
  .contactContainer {
    padding: 20px;
    gap: 20px;
  }
  .buttonGroup {
    flex-direction: column;
    gap: 15px;
  }
  .contactButton {
    width: 100%;
  }
  .contactTitleWrapper {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .contactContainer {
    padding: 15px;
    gap: 15px;
  }
  .contactTitle {
    font-size: 19px;
  }
  .contactButton {
    padding: 12px 16px;
  }
  .contactDescription {
    font-size: 14px;
  }
}