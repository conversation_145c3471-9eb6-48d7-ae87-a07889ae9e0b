"use client";
import PrivacyContent from "../components/modals/LegalDocuments/PrivacyContent";
import { useLegalDocuments } from "@/hooks/useLegalDocuments";

interface PrivacySection {
  id: string | number;
  tituloDeSeccion?: string;
  contenidoDeSeccion: string;
}

export default function PrivacyPage() {
  const { privacySections = [], loading } = useLegalDocuments(true) as {
    privacySections: PrivacySection[];
    loading: boolean;
  };
  return (
    <main style={{ padding: 24, maxWidth: 900, margin: "0 auto" }}>
      <h1 style={{ fontSize: 32, fontWeight: 700, marginBottom: 24, color: '#10265f' }}>Aviso de Privacidad</h1>
      <PrivacyContent privacySections={privacySections} loading={loading} />
    </main>
  );
}
