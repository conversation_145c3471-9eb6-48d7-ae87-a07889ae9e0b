import { FileUploader, FilterContainer, InputsContainer2, StyledInput } from '@/Styles/General.styles';

import styled from 'styled-components';
import ListadoArticulos from './MakeReport/ListadoArticulos';

const MakeReport = () => {
  return (
    <StyledLayout>
      <div className="containerMain">
        <div className="headerContainer">
          <h1>Platícanos que sucedió</h1>
          <p>Recuerda que La información que nos proporciones debe que ser igual al Acta del Ministerio Publico</p>
        </div>

        <InputsContainer2>
          <FilterContainer name="¿Cuándo ocurrió el incidente?">
            <StyledInput placeholder="Introduce el Nombre del Proyecto" id="projectName" name="name" disabled />
          </FilterContainer>
          <FilterContainer name="¿A qué hora sucedió?">
            <StyledInput placeholder="Introduce el Nombre del Proyecto" id="projectName" name="name" disabled />
          </FilterContainer>
          <FilterContainer name="¿En donde sucedió?">
            <StyledInput placeholder="Introduce el Nombre del Proyecto" id="projectName" name="name" disabled />
          </FilterContainer>
          <FilterContainer name="¿Cuáles fueron las circunstancias?" style={{ gridColumn: 'span 3' }}>
            <StyledInput placeholder="Introduce el Nombre del Proyecto" id="projectName" name="name" disabled />
          </FilterContainer>
          <FilterContainer name="Por favor, sube el Acta de Denuncia al Ministerio Publico" style={{ gridColumn: 'span 3' }}>
            <FileUploader onFileSelect={() => console.log('file here')} accept=".pdf,.jpg" />
          </FilterContainer>
        </InputsContainer2>
        <div className="headerContainer">
          <h1>Inventario de lo perdido</h1>
          <p>Se te pedirá comprobar con un ticket los artículos marcados para verificar la compra</p>
        </div>
        <ListadoArticulos />
      </div>
    </StyledLayout>
  );
};

export default MakeReport;

const StyledLayout = styled.div`
  background-color: #fdfaff;
  width: 100%;
  min-height: 70vh;

  .containerMain {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
    align-items: flex-start;
    justify-content: start;
    max-width: 120rem;
    margin: 0 auto;
    padding: 46px 60px;
    .headerContainer {
      display: flex;
      flex-direction: column;
      width: 100%;
    }
    h1 {
      color: var(--Gris-Obscuro, #333);
      font-family: Poppins;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    h2 {
      color: var(--Gris-Obscuro, #333);

      /* Wiki/Common/Body */
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
`;
