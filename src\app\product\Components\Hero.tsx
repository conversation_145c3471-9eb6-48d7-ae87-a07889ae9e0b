'use client';
import Image from 'next/image';
import React, { FunctionComponent } from 'react';
import styled from 'styled-components';
import { Product } from '../products.types';
import { Check } from '@mui/icons-material';
import { getEquivalentColor } from '@/Functions/getEquivalentColor';
import { renderParagraphsWithIndent } from '@/Functions/renderParagraphsWithIndent';
// import Candado from "@/../public/web/img/candado.svg";

interface HeroProps {
  product: Product;
}
const Hero: FunctionComponent<HeroProps> = ({ product }) => {
  return (
    <Layout>
      <Wrapper className="card-elements">
        <Card>
          <div
            style={{
              borderRadius: '0px 16px 16px 16px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'start',
            }}
          >
            <Title>{product.title.toLowerCase()}</Title>
            <Subtitle style={{ color: getEquivalentColor(product.color) }}>{product.subtitle}</Subtitle>
            <DescriptionContainer>{renderParagraphsWithIndent(product.description)}</DescriptionContainer>
            <CoverageList>
              <CoverageItem>
                <strong>Te cubrimos:</strong>
              </CoverageItem>
              {product.features.map((feature, index) => (
                <CoverageItem key={index} style={{ marginLeft: '10px' }}>
                  <strong>
                    {feature.icon ? <Image key={index} src={feature.icon.url} alt={feature.icon.nombre} width={25} height={25} /> : <Check />}
                    {feature.title}:
                  </strong>
                  {renderParagraphsWithIndent(feature.description)}
                </CoverageItem>
              ))}
            </CoverageList>
          </div>
          <ImageWrapperMobile>
            {product.image && (
              <Image
                src={product.image} // Sustituye este placeholder con la URL real de la imagen
                alt={product.title}
                sizes="100%"
                width={'100'}
                height={'100'}
                style={{ height: '100%', width: 'auto' }}
              />
            )}
          </ImageWrapperMobile>
          {/* <Button style={{ color: product.color }} onClick={scrollToInfo}>
          Leer más
        </Button> */}
        </Card>
        <ImageWrapper>
          <Image
            src={product.image} // Sustituye este placeholder con la URL real de la imagen
            alt={product.title}
            sizes="100%"
            style={{ minWidth: '250px', width: '100%', height: 'auto' }}
            width={'100'}
            height={'100'}
          />
        </ImageWrapper>
      </Wrapper>
    </Layout>
  );
};
export default Hero;

const Layout = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fdfaff;
`;

const Wrapper = styled.section`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  max-width: 1440px;
  padding: 60px 2rem 60px;
  margin: 0 auto;
  gap: 70px;
  background: #fdfaff;
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const Card = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
  flex: 4;
  align-items: center;
  position: relative;

  /* div {
    display: flex;
    flex-direction: column;
    margin-top: 2rem;
    align-items: start;
  } */

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    align-items: center;
  }
`;

const Title = styled.h2`
  font-family: Poppins;
  font-weight: 600;
  font-size: 36px;
  line-height: 54px;
  letter-spacing: 0%;
  color: #10265f;
  text-transform: capitalize;
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const Subtitle = styled.p`
  color: #8c699d;
  font-family: Poppins;
  font-weight: 600;
  font-size: 22px;
  line-height: 33px;
  letter-spacing: 0%;
  line-height: normal;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
`;

const CoverageList = styled.ul`
  text-align: left;
  margin-bottom: 1.5rem;
  list-style-type: none;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const CoverageItem = styled.li`
  color: var(--Text-Black, #000);
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 7px;

  strong {
    color: #10265f;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  p {
    margin-left: 20px;
  }

  @media (max-width: 768px) {
    font-size: 0.85rem;
  }
`;

const DescriptionContainer = styled.div`
  display: flex;
  width: 100%;
  max-width: 625px;
  padding: 0 0 30px;
  flex-direction: column;
  align-items: flex-start;
  p {
    color: #10265f;
    font-family: Poppins;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
  }
`;

// const Button = styled.button`
//   background-color: #fff;
//   border: none;
//   border-radius: 200px;
//   padding: 16px 24px;
//   font-size: 1rem;
//   cursor: pointer;
//   transition: background-color 0.3s ease;
//   width: 188px;

//   @media (max-width: 768px) {
//     font-size: 0.9rem;
//     padding: 0.6rem 1.2rem;
//   }
// `;

const ImageWrapper = styled.div`
  display: flex;
  justify-content: center;
  /* margin-top: 2rem; */
  flex: 2;

  img {
    width: 80%;
    /* max-width: 200px; */
  }
  @media (max-width: 768px) {
    display: none;
  }
`;

const ImageWrapperMobile = styled.div`
  display: block;
  justify-content: center;
  /* margin-top: 2rem; */
  height: 200px;

  img {
    height: auto;
  }
  @media (min-width: 769px) {
    display: none;
  }
`;

// const Info = styled.span`
//   position: absolute;
//   top: -38px;
//   left: 0;
//   width: 118px;
//   height: 38px;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   color: #fff;
//   font-family: Poppins;
//   font-size: 16px;
//   font-style: normal;
//   font-weight: 400;
//   line-height: normal;
//   border-radius: 16px 16px 0px 0px;
//   background: #86649b;
//   padding: 0 15px;
// `;
