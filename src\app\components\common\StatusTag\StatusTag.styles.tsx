import styled from 'styled-components';

export const StatusContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
  flex-direction: column;
  cursor: pointer;
  padding: 0;
  text-decoration: none;
  transition: opacity 0.2s ease-in-out;

  &:hover {
    opacity: 0.8;
  }

  &:focus-visible {
    outline: 2px solid #AF8CC0;
    outline-offset: 4px;
  }
`;

export const StatusText = styled.span`
  color: #10265F;
  text-decoration: none;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  transition: color 0.2s ease-in-out;
  text-decoration: underline;

  @media (max-width: 980px) {
    font-size: 14px;
  }
`;
