/**
 * Representa una pregunta y su respuesta.
 * @property {string} question - Texto de la pregunta.
 * @property {string} answer - Texto de la respuesta.
 * @property {number} [id] - Identificador único de la pregunta (opcional).
 * @property {boolean} [active] - Indica si la pregunta está activa (opcional).
 * @property {string} [category] - Categoría a la que pertenece la pregunta (opcional).
 * @property {number} [order] - Orden de la pregunta dentro de su categoría (opcional).
 * @property {string} [creationDate] - Fecha de creación de la pregunta (opcional).
 * @property {string} [updateDate] - Fecha de la última actualización de la pregunta (opcional).
 * @property {string | null} [deleteDate] - Fecha de eliminación de la pregunta, si aplica (opcional).
 */
export interface FAQItem {
  question: string;
  answer: string;
  id?: number;
  active?: boolean;
  category?: string;
  order?: number;
  creationDate?: string;
  updateDate?: string;
  deleteDate?: string | null;
}

/**
 * Representa la estructura de los datos de preguntas frecuentes organizados por categorías.
 * Cada categoría contiene una lista de preguntas frecuentes.
 * @property {FAQItem[]} generalidades__servicio - Preguntas sobre generalidades del servicio.
 * @property {FAQItem[]} acceso_gestion_de_cuenta - Preguntas sobre acceso y gestión de la cuenta.
 * @property {FAQItem[]} pagos_facturacion - Preguntas sobre pagos y facturación.
 * @property {FAQItem[]} polizas_coberturas - Preguntas sobre pólizas y coberturas.
 * @property {FAQItem[]} siniestros - Preguntas sobre siniestros.
 */
export interface FAQData {
  generalidades__servicio: FAQItem[];
  acceso_gestion_de_cuenta: FAQItem[];
  pagos_facturacion: FAQItem[];
  polizas_coberturas: FAQItem[];
  siniestros: FAQItem[];
}

/**
 * Tipo para las props del componente FAQAccordion.
 * @property {string} title - Título de la categoría.
 * @property {FAQItem[]} data - Lista de preguntas y respuestas asociadas a la categoría.
 */
export interface FAQAccordionProps {
  title: string;
  data: FAQItem[];
}
