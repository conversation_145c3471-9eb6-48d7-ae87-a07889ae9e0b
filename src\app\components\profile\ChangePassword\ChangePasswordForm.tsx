"use client";
import { useFormik } from "formik";
import { Typography } from "@mui/material";
import { useState } from "react";
import * as S from "./changePassword.styles";
import { changePasswordSchema } from "./changePasswordSchema";
import Image from "next/image";
import CustomSwitch from "../../CustomSwitch";
import changePasswordImg from "/public/web/img/mainAssets/changePasswordImg.png";
import { useResetPassword } from "@/hooks/PasswordRecovery/useResetPassword";
import { useRouter } from "next/navigation";
import { useIsLogged } from "@/app/LoginProvider";
import { useSnackbar } from "@/context/SnackbarContext";

export default function ChangePasswordForm() {
  const router = useRouter();
  const { logout } = useIsLogged();
  const { changePassword } = useResetPassword();
  const [loading, setLoading] = useState(false);
  const { showSuccess } = useSnackbar();

  const handleLogOut = () => {
    logout();
    router.push("/");
  };

  const formik = useFormik({
    initialValues: {
      currentPassword: "",
      newPassword: "",
      confirmNewPassword: "",
      logoutAll: false,
    },
    validationSchema: changePasswordSchema,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        await changePassword({
          newPassword: values.newPassword,
          oldPassword: values.currentPassword,
        });
        if (values.logoutAll) {
          handleLogOut();
        } else {
          router.back();
        }
        showSuccess("Cambios guardados.");
      } catch (error) {
        console.error("Error al cambiar contraseña:", error);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <S.Container>
      <S.Title>Cambia tu contraseña</S.Title>

      <S.Content>
        <S.Form onSubmit={formik.handleSubmit}>
          <S.InputGroup>
            <S.InputField
              type="password"
              id="currentPassword"
              {...formik.getFieldProps("currentPassword")}
              $hasError={
                !!(
                  formik.touched.currentPassword &&
                  formik.errors.currentPassword
                )
              }
              $isActive={formik.values.currentPassword !== ""}
            />
            <S.InputLabel
              htmlFor="currentPassword"
              $isActive={formik.values.currentPassword !== ""}
            >
              Ingresa tu contraseña actual
            </S.InputLabel>
            {formik.touched.currentPassword &&
              formik.errors.currentPassword && (
                <S.ErrorText>{formik.errors.currentPassword}</S.ErrorText>
              )}
          </S.InputGroup>

          <S.InputGroup>
            <S.InputField
              type="password"
              id="newPassword"
              {...formik.getFieldProps("newPassword")}
              $hasError={
                !!(formik.touched.newPassword && formik.errors.newPassword)
              }
              $isActive={formik.values.newPassword !== ""}
            />
            <S.InputLabel
              htmlFor="newPassword"
              $isActive={formik.values.newPassword !== ""}
            >
              Nueva contraseña
            </S.InputLabel>
            {formik.touched.newPassword && formik.errors.newPassword && (
              <S.ErrorText>{formik.errors.newPassword}</S.ErrorText>
            )}
          </S.InputGroup>

          <S.InputGroup>
            <S.InputField
              type="password"
              id="confirmNewPassword"
              {...formik.getFieldProps("confirmNewPassword")}
              $hasError={
                !!(
                  formik.touched.confirmNewPassword &&
                  formik.errors.confirmNewPassword
                )
              }
              $isActive={formik.values.confirmNewPassword !== ""}
            />
            <S.InputLabel
              htmlFor="confirmNewPassword"
              $isActive={formik.values.confirmNewPassword !== ""}
            >
              Repite tu nueva contraseña
            </S.InputLabel>
            {formik.touched.confirmNewPassword &&
              formik.errors.confirmNewPassword && (
                <S.ErrorText>{formik.errors.confirmNewPassword}</S.ErrorText>
              )}
          </S.InputGroup>
        </S.Form>
        <S.ImageWrapper>
          <Image
            src={changePasswordImg}
            alt="Ilustración cambio de contraseña"
            width={160}
            height={160}
            style={{ maxWidth: "100%", height: "auto" }}
          />
        </S.ImageWrapper>
      </S.Content>

      <S.ToggleContainer>
        <Typography variant="body2">
          Elige una nueva contraseña con mínimo 8 caracteres, que incluya al
          menos una mayúscula, un número y un carácter especial
        </Typography>
        <Typography variant="body2" sx={{ marginTop: "6px" }}>
          Al escoger esta opción, se cerrará la sesión en el resto de tus
          dispositivos.
        </Typography>
        <CustomSwitch
          checked={formik.values.logoutAll}
          onChange={() =>
            formik.setFieldValue("logoutAll", !formik.values.logoutAll)
          }
        />
      </S.ToggleContainer>

      <S.SubmitButton
        type="button"
        onClick={async () => {
          formik.setTouched({
            currentPassword: true,
            newPassword: true,
            confirmNewPassword: true,
          });

          await formik.validateForm();
          formik.handleSubmit();
        }}
        disabled={!formik.isValid || !formik.dirty || loading}
      >
        {loading ? "Guardando..." : "Guardar"}
      </S.SubmitButton>
    </S.Container>
  );
}
