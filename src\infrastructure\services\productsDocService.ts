import axios from '@/core/axios';

interface PublicDocumentResponse {
  message: string;
  url: string;
}

export class ProductsDocService {
  /**
   * @param productId ID del producto
   * @returns URL (string) del documento
   */
  async getPublicDocumentUrl(productId: string | number): Promise<string> {
    const response = await axios.get<PublicDocumentResponse>(`/v1/products/${productId}/document/public`);
    return response.data.url;
  }
}
