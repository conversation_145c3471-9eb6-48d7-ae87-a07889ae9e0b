'use client';

import React, { useEffect, useState } from 'react';
// import { useRouter } from "next/router";
import {
  AppBar,
  Button,
  Box,
  Menu,
  // MenuItem,
  Drawer,
  List,
  ListItemText,
  ListItemButton,
  MenuItem,
  Modal,
  CircularProgress,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import styles from '../../styles/home.module.css';
import { ChevronDown } from 'lucide-react';
import MenuIcon from '@mui/icons-material/Menu';

import Image from 'next/image';
import Logo from '/public/web/img/wiki_seguros.svg';
import { usePathname, useRouter } from 'next/navigation';
import CloseIcon from '@mui/icons-material/Close';
import { useIsLogged, useLoginModalContext } from '@/app/LoginProvider';
import ModalTemplate from '@/app/components/modals/ModalTemplate';
import { ModalButton, ModalTitle, OptionContainer } from '@/app/components/GeneralComponents.styles';
import { useProducts } from '@/app/ProductsContextProvider';
import { useBanners } from '@/hooks/useBanners';

const Header = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const router = useRouter();
  const path = usePathname();
  const { logged, logout } = useIsLogged();
  const { productList } = useProducts();
  const { loading } = useProducts();
  const { user } = useIsLogged();
  const { loading: loadingBanners } = useBanners();

  /**
   * Datos de la lista de productos.
   */
  const data = productList || [];

  /**
   * Maneja la apertura del menú.
   * @param {React.MouseEvent<HTMLButtonElement>} event - El evento de clic.
   */
  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  /**
   * Estado para controlar la apertura del cajón (drawer).
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [openDrawer, setOpenDrawer] = useState(false);

  /**
   * Maneja el cierre del menú.
   */
  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenDrawer(false);
  };

  /**
   * Función para abrir el cajón (drawer).
   */
  const openDrawerFunc = () => {
    setOpenDrawer(true);
  };

  /**
   * Estado para controlar la expansión de la lista de productos en el cajón (drawer).
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [expand, setExpand] = useState(false);

  /**
   * Maneja la expansión de la lista de productos en el cajón (drawer).
   */
  const handleExpand = () => {
    setExpand(!expand);
  };

  /**
   * Estado para controlar la apertura del modal de inicio de sesión.
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */

  /**
   * Función para abrir el modal de inicio de sesión.
   */

  /**
   * Función para cerrar el modal de inicio de sesión.
   */

  const handleIngresar = () => {
    setOpenDrawer(false);
    openModal();
  };

  const [modalProfile, setModalProfile] = useState(false);

  const handleAdjustment = () => {
    setModalProfile(false);
    router.push('/profile');
  };

  const handleLogOut = () => {
    logout();

    setModalProfile(false);

    router.push('/');
  };

  const nameLimiter = (name: string) => {
    if (name.length > 11) {
      return name.slice(0, 8) + '...';
    } else {
      return name;
    }
  };

  useEffect(() => {
    setOpenDrawer(false);
  }, [path, logged]);

  const { openModal, setNextAction } = useLoginModalContext();

  const handleConfirmation = () => {
    const token = localStorage.getItem('accessToken');

    if (!token) {
      setNextAction(() => router.push('/siniestros'));
      openModal();
    } else {
      router.push('/siniestros');
    }
  };

  return (
    <>
      <Modal open={loading || loadingBanners}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            width: '100vw',
            backgroundColor: 'rgba(255, 255, 255',
            flexDirection: 'column',
            transition: 'all 0.5s ease-in-out',
          }}
        >
          <Image width={200} height={200} src={Logo} alt="Wiki Seguros" />
          <h1 style={{ fontFamily: 'Poppins' }}>Cargando...</h1>
          <CircularProgress />
        </div>
      </Modal>
      <ModalTemplate open={modalProfile} onClose={() => setModalProfile(false)}>
        <OptionContainer>
          <ModalTitle>Hola {user?.nickname ? user.nickname : user?.firstName}</ModalTitle>
          <ModalButton onClick={handleAdjustment}>Ajustes de perfil</ModalButton>
          <ModalButton onClick={handleLogOut}>Cerrar sesión</ModalButton>
        </OptionContainer>
      </ModalTemplate>
      <AppBar className={styles.navbar} sx={{ boxShadow: 'none !important' }} elevation={0}>
        {/* Logo o título de la aplicación */}
        <Image fill src={Logo} alt="Wiki Seguros" className={styles.navbarLogo} onClick={() => router.push('/')} style={{ cursor: 'pointer' }} />

        <nav className={styles.navbarMenu}>
          {/* Botones de navegación */}
          <Box
            sx={{
              display: { xs: 'none', sm: 'none', lg: 'flex' },
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Button
              color="inherit"
              onClick={handleMenuOpen}
              className={styles.navbarBtn}
              endIcon={<ChevronDown className={styles.arrow} style={Boolean(anchorEl) ? { transform: 'rotate(180deg)' } : { transform: 'rotate(0deg)' }} />}
            >
              Productos
            </Button>
            <Menu
              className={styles.navbarMenu}
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              sx={{
                '& .MuiPaper-root': {
                  borderRadius: '10px',
                  border: '1px solid #e0e0e0',
                },
              }}
              MenuListProps={{
                'aria-labelledby': 'fade-button',
                className: styles.navbarMenuList,
                disabledItemsFocusable: true,
              }}
              autoFocus={false}
            >
              {data.map((item) => (
                <MenuItem
                  key={item.id}
                  onClick={() => {
                    router.push('/products/' + item.id.toString());
                    handleMenuClose();
                  }}
                  className={styles.menuItem}
                  sx={{
                    textTransform: 'capitalize',
                    transition: 'transform 0.2s ease-in-out, color 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'scale(1.05)',
                      color: '#10265f !important',
                      backgroundColor: 'transparent !important',
                    },
                  }}
                >
                  {item.title.toLowerCase()}
                </MenuItem>
              ))}
            </Menu>
            <div className={styles.styleBannerMobile}>
              <Button color="inherit" className={styles.navbarBtn} onClick={handleConfirmation}>
                Siniestros
              </Button>
              {logged && (
                <Button color="inherit" className={styles.navbarBtn} onClick={() => router.push('/insurance-policies')}>
                  Mis pólizas
                </Button>
              )}
              {logged && (
                <Button color="inherit" className={styles.navbarBtn} onClick={() => router.push('/accident-reports')}>
                  Mis reportes
                </Button>
              )}
              <Button color="inherit" className={styles.navbarBtn}>
                App
              </Button>
            </div>
          </Box>
          {!logged ? (
            <Button
              variant="outlined"
              startIcon={<PersonIcon sx={{ color: '#10265F' }} />}
              className={styles.navbarBtnIngresar}
              onClick={openModal}
              sx={{
                display: { xs: 'none', md: 'none', lg: 'flex' },
                alignItems: 'center',
                gap: 1,
                width: '175px',
                maxHeight: '48px',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
              }}
            >
              Ingresar
            </Button>
          ) : (
            <Button
              variant="outlined"
              startIcon={<PersonIcon sx={{ color: '#10265F' }} />}
              className={styles.navbarBtnIngresar}
              sx={{
                display: { xs: 'none', md: 'none', lg: 'flex' },
                alignItems: 'center',
                gap: 1,
                width: '175px',
                maxHeight: '48px',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
              }}
              onClick={() => setModalProfile(true)}
            >
              {user?.nickname ? nameLimiter(user.nickname) : user?.firstName}
            </Button>
          )}
        </nav>
        <Box
          className={styles.drawerButton}
          sx={{
            display: { xs: 'block', sm: 'block', md: 'block', lg: 'none' },
          }}
        >
          <Button color="inherit" onClick={openDrawerFunc}>
            <MenuIcon />
          </Button>
        </Box>
        <Drawer anchor="left" open={openDrawer} onClose={() => setOpenDrawer(false)} sx={{ display: { lg: 'none' } }} component="nav">
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              width: '100vw',
              padding: '40px 20px 20px',
              height: '100%',
              justifyContent: 'space-between',
              backgroundColor: '#F1DEFA',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'flex-end',
                position: 'absolute',
                width: '100%',
                top: '18px',
                right: '18px',
              }}
            >
              <Button onClick={() => setOpenDrawer(false)}>
                <CloseIcon style={{ color: '#333' }} />
              </Button>
            </div>
            <List>
              <ListItemButton onClick={handleExpand} className={styles.drawerBtn}>
                <ListItemText primary="Productos" className={styles.drawerBtnTxt} style={{ color: '#333' }} />
              </ListItemButton>

              <List component="div" disablePadding color="inherit" sx={{ paddingLeft: '10px !important' }}>
                {data.map((item) => (
                  <ListItemButton
                    sx={{ pl: 4 }}
                    key={item.id}
                    onClick={() => {
                      handleMenuClose();
                      router.push('/products/' + item.id.toString());
                    }}
                    className={styles.drawerBtn}
                  >
                    <ListItemText
                      primary={` ${item.title.toLowerCase()}`}
                      className={styles.drawerBtnTxt}
                      sx={{
                        color: 'rgba(125, 125, 125, 1)',
                        fontSize: '12px',
                        textTransform: 'capitalize',
                      }}
                    />
                  </ListItemButton>
                ))}
              </List>

              <ListItemButton color="inherit" className={styles.drawerBtn}>
                <ListItemText primary="Siniestros" className={styles.drawerBtnTxt} style={{ color: '#333' }} />
              </ListItemButton>

              {logged && (
                <ListItemButton color="inherit" className={styles.drawerBtn} onClick={() => router.push('/insurance-policies')}>
                  <ListItemText primary="Mis pólizas" className={styles.drawerBtnTxt} style={{ color: '#333' }} />
                </ListItemButton>
              )}

              <ListItemButton color="inherit" className={styles.drawerBtn}>
                <ListItemText primary="App" className={styles.drawerBtnTxt} style={{ color: '#333' }} />
              </ListItemButton>
            </List>
            {!logged ? (
              <Button
                variant="outlined"
                startIcon={<PersonIcon sx={{ color: '#10265F' }} />}
                className={styles.navbarBtnIngresar}
                onClick={handleIngresar}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  width: '175px',
                  maxHeight: '48px',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  margin: '0 !important',
                }}
              >
                Ingresar
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<PersonIcon sx={{ color: '#10265F' }} />}
                className={styles.navbarBtnIngresar}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  width: '175px',
                  maxHeight: '48px',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  margin: '0 !important',
                }}
                onClick={() => setModalProfile(true)}
              >
                {user?.nickname ? nameLimiter(user.nickname) : user?.firstName}
              </Button>
            )}
          </div>
        </Drawer>
      </AppBar>
    </>
  );
};

export default Header;
