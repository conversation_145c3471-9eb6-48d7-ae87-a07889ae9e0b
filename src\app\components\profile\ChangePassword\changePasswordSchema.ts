import * as Yup from "yup";

export const changePasswordSchema = Yup.object({
  currentPassword: Yup.string()
    .required("Ingresa tu contraseña actual."),

  newPassword: Yup.string()
    .required("Ingresa tu nueva contraseña.")
    .min(8, "Debe tener al menos 8 caracteres.")
    .matches(/[A-Z]/, "Debe tener al menos una letra mayúscula.")
    .matches(/[0-9]/, "Debe tener al menos un número.")
    .matches(/[^A-Za-z0-9]/, "Debe tener al menos un carácter especial.")
    .notOneOf(
      [Yup.ref("currentPassword")],
      "La nueva contraseña no puede ser igual a la actual."
    ),

  confirmNewPassword: Yup.string()
    .required("Confirma tu nueva contraseña.")
    .oneOf(
      [Yup.ref("newPassword")],
      "Las contraseñas no coinciden."
    ),

  logoutAll: Yup.boolean(),
});
