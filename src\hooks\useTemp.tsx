import { useState, useEffect, useRef, useCallback } from "react";

/**
 * Custom hook that exposes a 5‑minute countdown timer.
 *
 * Usage:
 * const { time, disable, restart } = useTemp();
 */
export const useTemp = () => {
  const INITIAL_SECONDS = 2 * 60; // 5 minutes
  const [secondsLeft, setSecondsLeft] = useState<number>(INITIAL_SECONDS);
  const [disable, setDisable] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  /** Converts a number of seconds to `m:ss` format. */
  const formatTime = (totalSeconds: number): string => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  /** Clears the active interval, if any. */
  const clear = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  /** Resets the timer and re‑starts the countdown. */
  const restart = useCallback(() => {
    clear();
    setSecondsLeft(INITIAL_SECONDS);
    setDisable(false);
  }, []);

  useEffect(() => {
    // Start the countdown only if it's not disabled
    if (!disable) {
      intervalRef.current = setInterval(() => {
        setSecondsLeft((prev) => {
          if (prev <= 1) {
            clear();
            setDisable(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    // Clean‑up when component unmounts or when disable changes
    return clear;
  }, [disable]);

  return {
    time: formatTime(secondsLeft),
    disable,
    restart,
  };
};

export default useTemp;
