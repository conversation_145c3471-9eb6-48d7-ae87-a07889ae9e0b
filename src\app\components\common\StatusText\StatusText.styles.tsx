import styled from 'styled-components';

interface StatusTextProps {
  $status: 'Vigente' | 'Vencida' | 'Aprobado' | 'Pendiente' | 'Rechazado';
}

export const StatusTextContainer = styled.span<StatusTextProps>`
  color: ${props => {
    switch (props.$status) {
      case 'Vigente':
      case 'Aprobado':
        return '#3BBE30';
      case 'Vencida':
      case 'Rechazado':
        return '#D54747';
      case 'Pendiente':
        return '#E08E12';
      default:
        return '#6D6D6D';
    }
  }};
  background-color: ${props => {
    switch (props.$status) {
      case 'Vigente':
      case 'Aprobado':
        return 'rgba(59, 190, 48, 0.1)';
      case 'Vencida':
      case 'Rechazado':
        return 'rgba(213, 71, 71, 0.1)';
      case 'Pendiente':
        return 'rgba(237, 108, 2, 0.1)';
      default:
        return 'rgba(109, 109, 109, 0.1)';
    }
  }};
  font-weight: 500;
  font-family: Poppins;
  font-size: 16px;
  padding: 4px 12px;
  border-radius: 16px;
  display: inline-block;

  @media (max-width: 980px) {
    font-size: 14px;
    padding: 3px 10px;
  }
`;
