import { <PERSON>, Button, Typography } from "@mui/material";
import Image from "next/image";
import styled from "styled-components";

export const StyledLayout = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: none;
  padding: 1rem;
`;

export const StyledContainer = styled.div`
  display: flex;
  width: 100%;
  max-width: 1002px;
  height: fit-content;
  padding: 40px 32px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  border-radius: 6px;
  background: #fff;
  position: relative;
`;

export const StyledTitle = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
`;

export const StyledSubtitle = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

export const DatosContainer = styled.div`
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
`;

export const ButtonContainer = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
  gap: 33px;
  align-items: center;

  @media (max-width: 960px) {
    flex-direction: column;
  }
`;

export const StyledButton = styled.button`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  width: 256px;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  text-transform: capitalize;
  color: #ffffff;
  font-weight: 400;
  font-size: 16px;
`;

export const Sendbutton = styled(StyledButton)`
  background-color: #10265f;
  &:disabled {
    background: #ebebeb;
    cursor: not-allowed;
    color: #828282;
  }
`;

export const SMSButton = styled(StyledButton)`
  background-color: #7abbd4;
`;

export const EmailButton = styled(StyledButton)`
  background-color: #af8cc0;
`;

export const Icon = styled(Image)`
  width: 21px;
  height: 21px;
  position: relative;
`;

export const StyledForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  align-items: center;
  justify-content: center;
  max-width: 700px;
  padding: 16px;
  input {
    display: flex;
    padding: 20px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 20px;
    border: 1.5px solid var(--Primary-Navy, #10265f);
    background: var(--Blanco, #fff);
  }
`;

export const ErrorText = styled.div`
  color: red;
  font-size: 14px;
  font-family: Poppins;
`;

export const Title2 = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;

  /* Wiki/Desktop/H3 */
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
`;

export const SectionOtpCode = styled.section`
  display: flex;
  width: 100%;
  padding: 0px 24px;
  justify-content: center;
  align-items: center;
  min-height: 90vh;
`;

export const SectionPlanes = styled.div`
  position: relative;
  border-radius: 18px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 60px 60px;
  gap: 64px;
  text-align: center;
  font-size: 16px;
  color: #828282;
  font-family: Poppins;
  width: 100%;
  max-width: 932px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
`;

export const Text = styled(Box)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
`;

export const HolaSoyTikiSolo = styled(Box)`
  width: 1248px;
  position: relative;
  font-size: 22px;
  font-weight: 600;
  color: #5959a3;
  display: none;
`;

export const HolaSoyTikiSolo1 = styled(Box)`
  color: var(--Gris-Obscuro, #333);
  text-align: center;

  /* Wiki/Common/Body */
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

export const CardDigits = styled(Box)`
  max-width: 612px;
  width: 100%;
  border-radius: 24px;
  background-color: #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 32px;
  box-sizing: border-box;
  gap: 24px;
  color: #7d7d7d;
`;

export const IconSmsWrapper = styled(Box)`
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
`;

export const IconSms = styled(Box)`
  border-radius: 56.6px;
  background-color: #10265f;
  border: 8px solid #f3fafd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
`;

export const StyledImage = styled(Image)`
  width: 48px;
  height: 48px;
  position: relative;
`;

export const Enter4DigitsCodeParent = styled(Box)`
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
`;

export const Enter4Digits = styled(Box)`
  color: var(--Gris-Obscuro, #333);
  text-align: center;

  /* Wiki/Common/Body */
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

export const Grupo16070 = styled(Box)`
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16px;
  min-height: 52px;
`;

export const FrameParent = styled(Box)`
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  color: #af8cc0;
`;

export const ButtonParent = styled(Box)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

export const ButtonText = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: capitalize;
  color: #af8cc0;
  font-size: 16px;
  font-family: Poppins;
  font-weight: 400;
`;

export const StyledTypography = styled(Typography)`
  position: relative;
  color: #828282;
  font-family: Poppins;
  font-size: 14px;
`;

export const ButtonsContainer = styled(Box)`
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  align-content: center;
  gap: 8px 24px;
`;

export const Button1 = styled(Button)`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  background-color: #10265f;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  color: #fff;
  text-transform: capitalize;
  font-family: Poppins;
`;

export const Button2 = styled(Button)`
  cursor: pointer;
  border: 2px solid #10265f;
  padding: 16px 24px;
  background-color: #fff;
  border-radius: 200px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 188px;
  color: #10265f;
  text-transform: capitalize;
  font-family: Poppins;
`;

export const StyledText = styled.p`
  color: var(--Gris-muy-obscuro, #6d6d6d);
  text-align: center;

  /* Wiki/Common/Legal */
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  max-width: 299px;

  span {
    color: var(--Primary-Navy, #10265f);
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
  }
`;

export const MessageResent = styled.div`
  display: flex;
  height: 34px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  background: rgba(16, 38, 95, 0.08);

  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 120%; /* 16.8px */
`;