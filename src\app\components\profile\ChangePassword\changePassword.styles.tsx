import styled from "styled-components";

export const Container = styled.section`
  padding: 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 780px;
`;

export const Title = styled.p`
  color: #333;
  flex: 1 0 0;
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
`;

export const Content = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

export const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
`;

export const InputGroup = styled.div`
  position: relative;
  width: 100%;
`;

export const InputField = styled.input<{ $hasError?: boolean , $isActive?: boolean }>`
  width: 100%;
  padding: ${({ $isActive }) => ($isActive ? "23px 20px 8px 20px" : "20px 8px")};
  border-radius: 20px;
  border: 1.5px solid ${({ $hasError }) => ($hasError ? "#FF5252" : "#10265f")};
  background: #fff;
  font-family: Poppins;
  font-size: 16px;
  color: #333;

  &:focus {
    outline: none;
    border-color: ${({ $hasError }) => ($hasError ? "#FF5252" : "#10265f")};
  }
`;

export const InputLabel = styled.label<{ $isActive?: boolean }>`
  position: absolute;
  left: 20px;
  top: ${({ $isActive }) => ($isActive ? "8px" : "22px")};
  font-size: ${({ $isActive }) => ($isActive ? "12px" : "16px")};
  color: ${({ $isActive }) => ($isActive ? "#666" : "#afafaf")};
  font-family: Poppins;
  font-weight: 400;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
`;

export const ErrorText = styled.p`
  font-size: 12.6px;
  font-style: normal;
  font-weight: 400;
  line-height: 19.8px;
  letter-spacing: 0.09px;
  color: rgb(211, 47, 47);
`;

export const SubmitButton = styled.button`
  display: flex;
  width: 188px;
  padding: 16px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 200px;
  background: #10265f;

  color: #fff;
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  
  &:disabled {
    background: #EBEBEB;
    color: #828282;
    cursor: not-allowed;
    opacity: 0.6;
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;



export const ToggleContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
`;

export const ImageWrapper = styled.div`
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  @media (max-width: 768px) {
    display: none;
  }
`;

