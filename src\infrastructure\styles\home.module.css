/* BANNER */
/* Navbar.css */

/* Estilos generales */

.navbar {
  background-color: #fff;
  color: #4caf50;
  width: 100%;
  max-width: 120rem;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding: 16px 60px 16px 96px;
  @media (max-width: 1199px) {
    padding: 16px 16px 16px 32px;
  }
}

.navbarToolbar {
  justify-content: space-between;
  width: 100%;
}
.navbarBox {
  display: flex;
  align-items: center;
}

.navbarLogo {
  width: 100px !important;
  height: 100px !important;
  object-fit: contain;
  position: relative !important;
}
.navbarMenu {
  display: flex;
  align-content: flex-end;
  align-items: start;
}

.drawerBtn {
  color: #af8cc0;
  font-weight: 700;
  font-family: var(--font-poppins);
  font-size: 16px;
  padding: 16px 24px;
}

.drawerBtnTxt {
  transition: transform 0.3s;
}

.navbarBtn {
  text-transform: none;
  color: #6d6d6d;
  font-weight: 400;
  font-family: var(--font-poppins);
  font-size: 16px;
  text-align: center;
  min-width: 35px;
  padding: 0 12px;
}
.navbarBtnIngresar {
  margin-left: 40px;
  margin-right: 40px;
  font-family: var(--font-poppins);
  font-weight: 700;
  font-size: 16px;
  border-color: #10265f;
  color: #10265f;
  text-transform: none;
  border-radius: 20px;
  padding: 10px 35px;
  text-align: center;
}
.navbarDivider {
  border-color: #6c518a;
  border-width: 1px;
  border-style: dotted;
  height: 30px;
}
.styleBannerMobile {
  display: flex;
  gap: 30px;
  padding: 10px 30px;
}
.navbarMenuList {
  display: flex;
  flex-direction: column;
}
.navbarMenuIcon:active {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menuItem {
  padding: 10px 15px;
  cursor: pointer;
  font-family: var(--font-poppins);
  font-size: 16px;
  color: #333;
  text-transform: capitalize;
}

.menuItem:hover,
.menuItem:focus {
  background-color: #f0f0f0;
}

.selectMenuItem {
  border-radius: 6px;
  margin-top: 8px;
  min-width: 180;
  box-shadow: rgb(255, 255, 255) 0px 0px 0px 0px,
    rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px,
    rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
}

.selectMenuItem:hover,
.selectMenuItem:focus {
  background-color: #f0f0f0;
}

.selectMenuItem:active {
  background-color: #e0e0e0;
}

.selectMenuItem {
  padding: 10px 15px;
  cursor: pointer;
  font-family: var(--font-poppins);
  font-size: 16px;
  color: #333;
}

.selectMenuItem:hover {
  background-color: #f0f0f0;
}

.selectMenuItem:active {
  background-color: #e0e0e0;
}

/* Main Footer */
.allFooter {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 0px 160px 100px 160px;
}
.styleFontFooter {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 24px;
  color: #ffffff;
  text-decoration: none;
}

.downloadApp {
  width: 100%;
  padding: 10px;
  background-color: #86c498;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  margin-top: 0.1rem;
}
.contentTextFooter {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  line-height: 1.5;
}

/* MainInfoContainer */
.mainInfoContainer {
  position: relative;
  /* background-image: url('/web/img/mainAssets/info_background.png'); */
  background-size: cover;
  background-position: center center;
  padding: 3rem 1rem;
  border-radius: 10px;
  color: white;
}
.backIcon {
  display: none;
  position: absolute;
  z-index: 0;
}
.infoContainer {
  position: relative;
  text-align: center;
}
.infoIcon {
  width: 100.8px;
  height: 100.8px;
  z-index: 1;
}
.infoTitle {
  color: var(--Text-Navy, #10265f);
  text-align: center;
  font-family: Poppins;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.infoSubTitle {
  color: var(--Negro, #000);
  text-align: center;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.textInfoContainer {
  margin-top: 2rem;
}
.infoContent {
  color: var(--Gris-muy-obscuro, #6d6d6d);
  text-align: center;

  /* Wiki/Common/Body */
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.textInfoContent {
  font-family: var(--font-poppins);
  font-weight: 500;
  font-size: 2rem;
  line-height: 3rem;
  text-align: center;
  color: #5959a3;
}
.leavesContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: flex-start;
  width: 100%;
  z-index: 10;
}

.infoLeavesImg {
  display: block;
  margin: 0;
  width: 203px;
  height: 113.089px;
  flex-shrink: 0;
}

@media (max-width: 1200px) {
  .leavesContainer {
    display: none;
  }
}

.showInMobile {
  display: none;
}
.showInWeb {
  display: flex;
}
.styleHojaFoter {
  width: 280px;
  height: 160px;
  position: absolute;
  transform: translateX(0%) translateY(-119%);
}

@media (max-width: 600px) {
  /* footer */
  .showInWeb {
    display: none;
  }
  .showInMobile {
    display: block;
  }
  .allFooter {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    padding: 5px;
    margin: 0%;
    width: 100%;
  }

  .footerSection1 {
    width: 100%;
  }

  .footerSection2 {
    display: flex;
    width: 100%;
  }

  .footerSection2 .left,
  .footerSection2 .right {
    width: 50%;
  }

  .footerSection3 {
    width: 100%;
  }
  .styleFontFooter {
    font-family: var(--font-inter);
    font-weight: 400;
    font-size: 18px;
    color: #ffffff;
    text-decoration: none;
  }
  .contentTextFooter {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 14px;
    line-height: 1.2;
    margin-top: 10%;
  }

  /* footer */

  /* navBar */
  .arrow {
    height: 15px;
    width: 15px;
  }

  /* .navbarMenu {
    display: block;
    margin-left: 5%;
    margin-top: 2%;
    align-content: center;
    align-items: center;
  } */
  .styleBannerMobile {
    display: flex;
    align-items: center;
    padding: 0%;
    gap: 0;
  }
  .navbarBox {
    display: flex;
    align-items: center;
    height: 20px;
    margin-left: 1%;
  }
  .navbarBtn {
    font-size: 10px;
  }
  .navbarLogo {
    height: 60px !important;
    width: 60px !important;
  }
  .navbarBtnIngresar {
    font-weight: 200;
    font-size: 10px;
    padding: 5px 15px;
  }
  .navbarMenuIcon {
    display: block;
  }
  /* navBar */

  .mainInfoContainer {
    background-image: none;
  }
  .backIcon {
    display: block;
  }
  .infoContainer {
    position: relative;
    margin: 0;
    padding: 0;
  }
  .infoContainer .leftPalmIcon {
    top: 10px;
    /* right: -35px;  */
  }
  .infoContainer .treeIcon {
    top: 65px;
    /* left: -55px; */
  }
  .infoContainer .rightPalmIcon {
    top: 50px;
    left: -60px;
  }
  .infoTitle {
    color: #7d7d7d;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .infoIcon {
    width: 70px;
    height: 70px;
    flex-shrink: 0;
  }

  .infoContent {
    color: #7d7d7d;
    font-size: 16px;
    font-family: var(--font-poppins);
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
