import { useState, useEffect, useCallback } from "react";
import { BannersService } from "@/infrastructure/services/bannersService";

export interface Banner {
  id: number;
  url_banner: string;
  linkUrl: string;
  order: number;
  isActive?: boolean;
}

export const useBanners = () => {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true); // Iniciar en true para mostrar loader inmediatamente
  const [error, setError] = useState<unknown>(null);

  const fetchBanners = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const service = new BannersService();

      // Agregar timeout para evitar carga infinita
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: La carga de banners está tomando demasiado tiempo')), 10000)
      );

      const bannersPromise = service.getBanners();

      const list = await Promise.race([bannersPromise, timeoutPromise]) as Banner[];
      setBanners(list);
    } catch (err) {
      setError(err);
      console.error('Error fetching banners:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchBanners();
  }, [fetchBanners]);

  return { banners, loading, error, refetch: fetchBanners };
};
