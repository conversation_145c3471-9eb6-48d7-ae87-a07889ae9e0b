'use client';
import React, { Fragment, useEffect, useState } from 'react';
import { ProtectedLayout } from '@/core/layouts/ProtectedLayout';
import { PrimaryButton, StyledTitle } from '@/Styles/General.styles';
import { Checkbox, FormControlLabel } from '@mui/material';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import useAuth from '@/hooks/useAuth';
import EmptyClaims from './components/EmptyClaims';
import EmptyProducts from './components/EmptyProducts';
import Link from 'next/link';

function SectionHeader({ children }: { children: React.ReactNode }) {
  return (
    <>
      <TitleRow>
        <StyledTitle>Mis reportes</StyledTitle>
        <Link
          href="#"
          style={{
            color: '#6D6D6D',
            fontWeight: 500,
            textDecoration: 'underline',
            fontFamily: 'Poppins',
            fontSize: '18px',
          }}
        >
          Ayuda
        </Link>
      </TitleRow>
      <CenteredContainer>{children}</CenteredContainer>
    </>
  );
}

export default function Siniestros() {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const steps = ['Paso 1', 'Paso 2', 'Paso 3', 'Paso 4'];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [myProducts, setMyProducts] = useState<any[]>([]);

  // Log en el render para depuración
  console.log('RENDER myProducts', myProducts, Array.isArray(myProducts) && myProducts.length === 0);

  const navigate = useRouter();

  const handleReporteClick = () => {
    navigate.push('/reportes');
  };

  const { getMyProducts } = useAuth();
  const fetchData = async () => {
    try {
      const userData = await getMyProducts();
      setMyProducts(Array.isArray(userData.data) ? userData.data : []);
    } catch (error) {
      console.error('Error al obtener los datos del usuario:', error);
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const claims = [
    // {
    //   id: "1",
    //   policy: "Wiki Phishing",
    //   policyNumber: "001",
    //   status: "Vigente",
    //   period: "Del 01/03/2024 al 01/03/2025",
    //   insuredAmount: "$7,500.00",
    //   remainingAmount: "$7,500.00",
    //   reportStatus: "Documentos",
    // }
  ];
  return (
    <ProtectedLayout>
      <StyledLayout>
        <div className="containerMain">
          {/* Si no hay productos contratados, mostrar EmptyProducts */}
          {Array.isArray(myProducts) && myProducts.length === 0 ? (
            <SectionHeader>
              <EmptyProducts onReport={() => navigate.push('/')} />
            </SectionHeader>
          ) : Array.isArray(myProducts) && myProducts.length > 0 && claims.length === 0 ? (
            <SectionHeader>
              <EmptyClaims />
            </SectionHeader>
          ) : Array.isArray(myProducts) && myProducts.length > 0 && claims.length > 0 ? (
            <SectionHeader>
              <ContentConatiner>
                <TopContainer>
                  <PrimaryButton style={{ height: '44px' }} onClick={handleReporteClick} disabled={Array.isArray(myProducts) ? false : true}>
                    Reportar un siniestro
                  </PrimaryButton>
                  <FilterContainer>
                    <FormControlLabel
                      control={<Checkbox />}
                      label="Reportes incompletos"
                      sx={{
                        color: '#7D7D7D',
                        fontFamily: 'Poppins',
                        fontWeight: '400',
                        fontSize: '16px',
                      }}
                    />
                    <FormControlLabel
                      control={<Checkbox />}
                      label="Reportes finalizados"
                      sx={{
                        color: '#7D7D7D',
                        fontFamily: 'Poppins',
                        fontWeight: '400',
                        fontSize: '16px',
                      }}
                    />
                  </FilterContainer>
                </TopContainer>
                <TableContainer>
                  <StyledTable>
                    <TableHead>
                      <tr>
                        <TableHeaderCell>Póliza</TableHeaderCell>
                        <TableHeaderCell>Número de póliza</TableHeaderCell>
                        <TableHeaderCell>Estatus de la póliza</TableHeaderCell>
                        <TableHeaderCell>Periodo</TableHeaderCell>
                        <TableHeaderCell>Suma asegurada</TableHeaderCell>
                        <TableHeaderCell>Remanente de suma asegurada</TableHeaderCell>
                        <TableHeaderCell>Estatus del reporte</TableHeaderCell>
                      </tr>
                    </TableHead>
                    <tbody>
                      <tr>
                        <TableDataCell>Wiki Gadget</TableDataCell>
                        <TableDataCell>9847-24</TableDataCell>
                        <TableDataCell>Vigente</TableDataCell>
                        <TableDataCell>Del 01/03/2024 al 01/03/2025</TableDataCell>
                        <TableDataCell>$15,500.00</TableDataCell>
                        <TableDataCell>$15,500.00</TableDataCell>
                        <LastColumnCell>Datos incompletos</LastColumnCell>
                      </tr>
                    </tbody>
                  </StyledTable>
                </TableContainer>
                <StyledContainerSiniestro>
                  <div className="topContainer">
                    <div className="titleContainer">
                      <h2 className="title">Siniestro 1</h2>
                      <p className="date">01/03/2024</p>
                    </div>
                    <p className="numeroSiniestro">Número de siniestro: 123456</p>
                  </div>
                  <StepperContainer>
                    {steps.map((step, index) => (
                      <Fragment key={index}>
                        <StepWrapper onClick={() => setCurrentStep(index)} key={index}>
                          <StepImage activeStep={currentStep === index} />
                          <StepLabel>{step}</StepLabel>
                        </StepWrapper>
                        {index < steps.length - 1 && <StepLine activeStep={index < currentStep} />}
                      </Fragment>
                    ))}
                  </StepperContainer>
                </StyledContainerSiniestro>
              </ContentConatiner>
            </SectionHeader>
          ) : null}
        </div>
      </StyledLayout>
    </ProtectedLayout>
  );
}

const StyledLayout = styled.div`
  background-color: #fdfaff;
  width: 100%;

  .containerMain {
    display: flex;
    flex-direction: column;
    gap: 21px;
    width: 100%;
    align-items: flex-start;
    justify-content: start;
    max-width: 120rem;
    margin: 0 auto;
    padding: 46px 60px;
  }
`;

const ContentConatiner = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  padding: 10px 0;
`;

const TopContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const FilterContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
  width: fit-content;
`;

const TableContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: auto;
  max-height: 600px; /* Ajusta la altura máxima según sea necesario */
  border: 1px solid var(--Gris-Medio-Claro, #afafaf);
  border-radius: 10px;
`;
const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  overflow: auto;
`;

const TableHead = styled.thead`
  background: #51519b;
`;

const TableHeaderCell = styled.th`
  padding: 22px 12px;
  min-width: 180px;
  max-width: 180px;
  color: #fff;
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  align-items: center;
  justify-content: center;
  border: none;
`;

const TableDataCell = styled.td`
  padding: 12px;
  min-width: 180px;
  max-width: 180px;
  color: var(--Gris-muy-obscuro, #6d6d6d);
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  align-items: center;
  justify-content: center;
`;

const LastColumnCell = styled(TableDataCell)`
  color: var(--Text-Navy, #10265f);
  font-weight: 600;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
`;

const StyledContainerSiniestro = styled.div`
  display: flex;
  flex-direction: column;

  margin: 40px auto 0;
  padding: 20px 50px 34px;
  width: 100%;
  max-width: 1081px;
  height: auto;
  min-height: 214px;
  border-radius: 20px;
  border: 3px solid var(--Secondary-Blossom, #af8cc0);
  background: var(--Text-White, #fff);
  box-shadow: -8px 7px 4px 0px rgba(0, 0, 0, 0.1);
  .topContainer {
    gap: 24px;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: space-between;
    .titleContainer {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .title {
        color: var(--Gris-muy-obscuro, #6d6d6d);
        text-align: center;

        /* Wiki/Common/Title */
        font-family: Poppins;
        font-size: 22px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }
      .date {
        color: var(--Gris-muy-obscuro, #6d6d6d);
        text-align: center;

        /* Wiki/Common/Body */
        font-family: Poppins;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
    .numeroSiniestro {
      color: var(--Gris-muy-obscuro, #6d6d6d);
      text-align: center;

      /* Wiki/Common/Body */
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
`;

const StepperContainer = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
`;

const StepWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
`;

const StepImage = styled.div<{ activeStep: boolean }>`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${({ activeStep }) => (activeStep ? '#51519B' : '#ccc')};
  margin-bottom: 8px;
`;

const StepLabel = styled.span`
  font-family: Poppins;
  font-size: 14px;
  color: #6d6d6d;
`;

const StepLine = styled.div<{ activeStep: boolean }>`
  flex: 1;
  height: 2px;
  background: ${({ activeStep }) => (activeStep ? '#51519B' : '#ccc')};
  margin: 0 8px 29px;
`;

const CenteredContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const TitleRow = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
