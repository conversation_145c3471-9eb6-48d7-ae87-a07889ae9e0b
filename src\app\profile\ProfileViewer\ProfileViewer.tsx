"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  FormControlLabel,
  Checkbox,
  Typography,
  Snackbar,
  SnackbarCloseReason,
  Alert,
} from "@mui/material";
import * as S from "./signUp.styles"; // Importa los styled-components refactorizados
import useAuth from "@/hooks/useAuth";
import { CheckCircleIcon } from "lucide-react";

type UserData = {
  firstName: string;
  lastNamePaternal: string;
  lastNameMaternal: string;
  birthDate: string;
  gender: string;
  street: string;
  exteriorNumber: string;
  interiorNumber: string;
  postalCode: string;
  neighborhood: string;
  municipality: string;
  email: string;
  confirmEmail: string;
  phone: string;
};

export default function ProfileViewer() {
  const { fetchUserInfo, updateUserInfo } = useAuth();
  const [initialUserData, setInitialUserData] = useState<UserData | null>(null);

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastNamePaternal: "",
      lastNameMaternal: "",
      birthDate: "",
      nickname: "",
      gender: "",
      street: "",
      exteriorNumber: "",
      interiorNumber: "",
      postalCode: "",
      neighborhood: "",
      municipality: "",
      email: "",
      confirmEmail: "",
      phone: "",
    },
    validationSchema: Yup.object({
      firstName: Yup.string()
        .max(40, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa tu(s) nombre(s)."),
      lastNamePaternal: Yup.string()
        .max(40, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa tu apellido paterno."),
      lastNameMaternal: Yup.string()
        .max(40, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa tu apellido materno."),
      birthDate: Yup.date()
        .required("Por favor, selecciona tu fecha de nacimiento.")
        .test("age", "Debes tener al menos 18 años", (value) => {
          return (
            value &&
            new Date().getFullYear() - new Date(value).getFullYear() >= 18
          );
        }),
      nickname: Yup.string().max(
        20,
        "El texto no puede tener más de ${max} caracteres"
      ),
      gender: Yup.string().required(
        "Por favor, selecciona el género que aparece en tu identificación oficial."
      ),
      street: Yup.string()
        .max(50, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa el nombre de tu calle."),
      exteriorNumber: Yup.string()
        .matches(/^[0-9]+$/, "Solo se permiten números")
        .required("Por favor, ingresa tu número exterior.")
        .max(10, "El texto no puede tener más de ${max} caracteres"),
      interiorNumber: Yup.string().max(
        10,
        "El texto no puede tener más de ${max} caracteres"
      ),
      postalCode: Yup.string()
        .matches(/^[0-9]+$/, "Solo se permiten números")
        .min(5, "Por favor, ingresa un código postal válido de 5 dígitos.")
        .max(5, "Por favor, ingresa un código postal válido de 5 dígitos.")
        .required("Por favor, ingresa un código postal válido de 5 dígitos."),
      neighborhood: Yup.string()
        .required("Por favor, ingresa el nombre de tu colonia.")
        .max(50, "El texto no puede tener más de ${max} caracteres"),
      municipality: Yup.string()
        .required("Por favor, ingresa el nombre de tu municipio.")
        .max(50, "El texto no puede tener más de ${max} caracteres"),
      email: Yup.string()
        .email("Por favor, ingresa un correo electrónico válido.")
        .required("Por favor, ingresa un correo electrónico válido."),
      confirmEmail: Yup.string()
        .oneOf(
          [Yup.ref("email"), undefined],
          "El correo electrónico no coincide, por favor verifica."
        )
        .required("El correo electrónico no coincide, por favor verifica."),
      phone: Yup.string()
        .matches(
          /^[0-9]{10}$/,
          "Por favor, ingresa un número de celular válido de 10 dígitos."
        )
        .required("Este campo es obligatorio"),
    }),
    onSubmit: async (values) => {
      const formattedData = {
        email: values.email,
        firstName: values.firstName,
        lastNamePaternal: values.lastNamePaternal,
        lastNameMaternal: values.lastNameMaternal,
        birthDate: values.birthDate,
        nickname: values.nickname || "",
        gender: values.gender,
        street: values.street,
        exteriorNumber: values.exteriorNumber,
        interiorNumber: values.interiorNumber,
        postalCode: values.postalCode,
        neighborhood: values.neighborhood,
        municipality: values.municipality,
        phone: values.phone,
        userId: 1,
        updatedAt: new Date().toLocaleDateString("es-MX"),
      };
      const response = await updateUserInfo(formattedData);
      if (response.message === "Perfil actualizado correctamente") {
        handleSnackBar();
        loadUserData();
      } else {
        console.error("Error al actualizar datos", response);
      }
    },
  });

  const loadUserData = async () => {
    try {
      const data = await fetchUserInfo();
      setInitialUserData({
        firstName: data.firstName || "",
        lastNamePaternal: data.lastNamePaternal || "",
        lastNameMaternal: data.lastNameMaternal || "",
        birthDate: data.birthDate || "",
        gender: data.gender || "",
        street: data.addres?.street || "",
        exteriorNumber: data.addres?.exteriorNumber || "",
        interiorNumber: data.addres?.interiorNumber || "",
        postalCode: data.addres?.postalCode || "",
        neighborhood: data.addres?.neighborhood || "",
        municipality: data.addres?.municipality || "",
        email: data.email || "",
        confirmEmail: data.email || "",
        phone: data.phone || "",
      });
      formik.resetForm({
        values: {
          firstName: data.firstName || "",
          lastNamePaternal: data.lastNamePaternal || "",
          lastNameMaternal: data.lastNameMaternal || "",
          birthDate: data.birthDate || "",
          nickname: "",
          gender: data.gender || "",
          street: data.addres?.street || "",
          exteriorNumber: data.addres?.exteriorNumber || "",
          interiorNumber: data.addres?.interiorNumber || "",
          postalCode: data.addres?.postalCode || "",
          neighborhood: data.addres?.neighborhood || "",
          municipality: data.addres?.municipality || "",
          email: data.email || "",
          confirmEmail: data.email || "",
          phone: data.phone || "",
        },
      });
    } catch (err) {
      console.error("Error fetching data", err);
    }
  };

  useEffect(() => {
    loadUserData();
    console.log("Datos cargados", initialUserData);
  }, []);

  useEffect(() => {
    console.log("Datos cargados", initialUserData);
  }, [initialUserData]);

  const [openSnackBar, setOpeSnackBar] = useState(false);

  const handleSnackBar = () => {
    setOpeSnackBar(true);
  };

  const handleCloseSnackBar = (
    _event: React.SyntheticEvent | Event,
    reason?: SnackbarCloseReason
  ) => {
    if (reason === "clickaway") {
      return;
    }
    setOpeSnackBar(false);
  };

  return (
    <>
      <Snackbar
        open={openSnackBar}
        autoHideDuration={5000}
        onClose={handleCloseSnackBar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackBar}
          severity="success"
          icon={<CheckCircleIcon fontSize="inherit" />}
          variant="filled"
          sx={{
            display: "flex",
            borderRadius: "8px",
            color: "#72E128",
            backgroundColor: "#e3fcd2",
            fontSize: "1rem",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.3)",
            padding: "14px 16px !important",
            fontWeight: 300,
            gap: "8px",
          }}
        >
          Cambios guardados.
        </Alert>
      </Snackbar>
      <S.Container>
        <S.Greeting>
          <S.WelcomeMessage>¡Hola!</S.WelcomeMessage>
          <S.SubMessage>Estos son tus datos guardados:</S.SubMessage>
        </S.Greeting>
        <S.Form onSubmit={formik.handleSubmit}>
          <S.InputsContainer>
            <div>
              <S.LabelInput>
                Nombres{" "}
                <S.LabelInputSpan>(No lo puedes cambiar)</S.LabelInputSpan>
              </S.LabelInput>
              <S.InputStyles
                type="text"
                name="firstName"
                value={formik.values.firstName}
                readOnly
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.firstName && formik.errors.firstName && (
                <Typography color="error" variant="body2">
                  {formik.errors.firstName}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>
                Apellido Paterno{" "}
                <S.LabelInputSpan>(No lo puedes cambiar)</S.LabelInputSpan>
              </S.LabelInput>
              <S.InputStyles
                type="text"
                name="lastNamePaternal"
                value={formik.values.lastNamePaternal}
                readOnly
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.lastNamePaternal &&
                formik.errors.lastNamePaternal && (
                  <Typography color="error" variant="body2">
                    {formik.errors.lastNamePaternal}
                  </Typography>
                )}
            </div>
            <div>
              <S.LabelInput>
                Apellido Materno{" "}
                <S.LabelInputSpan>(No lo puedes cambiar)</S.LabelInputSpan>
              </S.LabelInput>
              <S.InputStyles
                type="text"
                name="lastNameMaternal"
                value={formik.values.lastNameMaternal}
                readOnly
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.lastNameMaternal &&
                formik.errors.lastNameMaternal && (
                  <Typography color="error" variant="body2">
                    {formik.errors.lastNameMaternal}
                  </Typography>
                )}
            </div>
            <div>
              <S.LabelInput>
                Género{" "}
                <S.LabelInputSpan>(No lo puedes cambiar)</S.LabelInputSpan>
              </S.LabelInput>
              <S.CheckboxContainer>
                <FormControlLabel
                  control={
                    <Checkbox
                      value="femenino"
                      checked={formik.values.gender === "femenino"}
                      disabled
                      sx={{
                        color: "#10265f",
                        "&.Mui-checked": { color: "#10265f" },
                      }}
                    />
                  }
                  label="Femenino"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      value="masculino"
                      checked={formik.values.gender === "masculino"}
                      disabled
                      sx={{
                        color: "#10265f",
                        "&.Mui-checked": { color: "#10265f" },
                      }}
                    />
                  }
                  label="Masculino"
                />
              </S.CheckboxContainer>
              {formik.touched.gender && formik.errors.gender && (
                <Typography color="error" variant="body2">
                  {formik.errors.gender}
                </Typography>
              )}
            </div>
          </S.InputsContainer>

          <S.InputsContainer>
            <div>
              <S.LabelInput>
                Fecha de Nacimiento{" "}
                <S.LabelInputSpan>(No lo puedes cambiar)</S.LabelInputSpan>
              </S.LabelInput>
              <S.DatePicker>
                <S.InputDate
                  type="text"
                  name="birthDate"
                  value={new Date(formik.values.birthDate).toLocaleDateString(
                    "es-MX"
                  )}
                  readOnly
                  style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
                />
              </S.DatePicker>
              {formik.touched.birthDate && formik.errors.birthDate && (
                <Typography color="error" variant="body2">
                  {formik.errors.birthDate}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>
                Email{" "}
                <S.LabelInputSpan>(No lo puedes cambiar)</S.LabelInputSpan>
              </S.LabelInput>
              <S.InputStyles
                type="email"
                autoComplete="email"
                inputMode="email"
                value={formik.values.email}
                readOnly
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.email && formik.errors.email && (
                <Typography color="error" variant="body2">
                  {formik.errors.email}
                </Typography>
              )}
            </div>
          </S.InputsContainer>

          <S.InputsContainer>
            <div>
              <S.LabelInput>Calle</S.LabelInput>
              <S.InputStyles
                type="text"
                name="street"
                value={formik.values.street}
                // onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
                readOnly
              />
              {formik.touched.street && formik.errors.street && (
                <Typography color="error" variant="body2">
                  {formik.errors.street}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>Número exterior</S.LabelInput>
              <S.InputStyles
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                name="exteriorNumber"
                value={formik.values.exteriorNumber}
                // onChange={formik.handleChange}
                readOnly
                // onBlur={formik.handleBlur}
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.exteriorNumber &&
                formik.errors.exteriorNumber && (
                  <Typography color="error" variant="body2">
                    {formik.errors.exteriorNumber}
                  </Typography>
                )}
            </div>
          </S.InputsContainer>

          <S.InputsContainer>
            <div>
              <S.LabelInput>Número interior</S.LabelInput>
              <S.InputStyles
                type="text"
                placeholder="Sin información"
                name="interiorNumber"
                value={formik.values.interiorNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.interiorNumber &&
                formik.errors.interiorNumber && (
                  <Typography color="error" variant="body2">
                    {formik.errors.interiorNumber}
                  </Typography>
                )}
            </div>
            <div>
              <S.LabelInput>Código postal</S.LabelInput>
              <S.InputStyles
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                name="postalCode"
                value={formik.values.postalCode}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
                readOnly
              />
              {formik.touched.postalCode && formik.errors.postalCode && (
                <Typography color="error" variant="body2">
                  {formik.errors.postalCode}
                </Typography>
              )}
            </div>
          </S.InputsContainer>

          <S.InputsContainer>
            <div>
              <S.LabelInput>Colonia</S.LabelInput>
              <S.InputStyles
                type="text"
                name="neighborhood"
                value={formik.values.neighborhood}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
                readOnly
              />
              {formik.touched.neighborhood && formik.errors.neighborhood && (
                <Typography color="error" variant="body2">
                  {formik.errors.neighborhood}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>Municipio</S.LabelInput>
              <S.InputStyles
                type="text"
                name="municipality"
                value={formik.values.municipality}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                readOnly
                style={{ backgroundColor: "#DBDBDB", color: "#7D7D7D" }}
              />
              {formik.touched.municipality && formik.errors.municipality && (
                <Typography color="error" variant="body2">
                  {formik.errors.municipality}
                </Typography>
              )}
            </div>
          </S.InputsContainer>

          <S.InputsContainer>
            <div>
              <S.LabelInput>Número celular</S.LabelInput>
              <S.InputPhoneStyles
                type="tel"
                name="phone"
                autoComplete="tel"
                inputMode="tel"
                value={formik.values.phone}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.phone && formik.errors.phone && (
                <Typography color="error" variant="body2">
                  {formik.errors.phone}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.SubmitButton
            type="submit"
            disabled={initialUserData?.phone === formik.values.phone}
          >
            Actualizar Información
          </S.SubmitButton>
        </S.Form>
      </S.Container>
    </>
  );
}
