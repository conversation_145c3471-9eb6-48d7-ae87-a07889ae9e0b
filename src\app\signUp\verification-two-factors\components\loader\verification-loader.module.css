.cardContainer {
    position: relative;
    top: calc(50% - 234px);
    border-radius: 18px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 40px 190px;
    gap: 50px;
    width: 100%;
    max-width: 617.5px;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
}


.sectionVerifiLoader {
    display: flex;
    width: 100%;
    padding: 0px 24px;
    justify-content: center;
    align-items: center;
    min-height: 90vh;
}

.sectionPlanesInner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px
}

.title {
    font-family: Poppins;
    font-size: 16px;
    color: #828282;
}

.subTitle {
    font-family: Poppins;
    font-size: 29px;
    color: #D8CDE6;
}