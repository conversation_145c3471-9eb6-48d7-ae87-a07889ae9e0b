import React from 'react';
import * as S from './FilterCheckbox.styles';

interface FilterCheckboxProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  id: string;
}

const FilterCheckbox: React.FC<FilterCheckboxProps> = ({ label, checked, onChange, id }) => {
  return (
    <S.CheckboxLabel htmlFor={id}>
      <S.CheckboxInput
        type="checkbox"
        id={id}
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
      />
      {label}
    </S.CheckboxLabel>
  );
};

export default FilterCheckbox;
