/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { ReactNode, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useIsLogged } from "@/app/LoginProvider";

interface AuthGuardProps {
  children: ReactNode;
  out?: boolean;
}

export const AuthGuard = ({ children, out = false }: AuthGuardProps) => {
  const router = useRouter();
   // Se espera que useIsLogged retorne además de logged, un flag loading
  const { logged, loading, openModal } = useIsLogged();

  useEffect(() => {
    // Mientras se carga la sesión, no se hace nada.
    if (loading) return;

    if (out) {
      // Rutas para usuarios NO autenticados.
      if (logged) {
        router.push("/");
      }
    } else {
      // Rutas que requieren autenticación.
      if (!logged) {
        router.push("/");
        setTimeout(() => openModal(), 100); 
      }
    }
  }, [logged, loading, router, out]);

  // Mientras se verifica la autenticación, no se renderiza nada.
  if (loading) return null;

  return <>{children}</>;
};
