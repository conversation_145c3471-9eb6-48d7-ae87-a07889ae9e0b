"use client";
import React from "react";
import { Box, Container } from "@mui/material";
import InfoContent from "./InfoContent";
import { MainInformationProps } from "@/infrastructure/models/home.model";
import { informationItems } from "@/infrastructure/data/home.data";
import styles from "../../styles/home.module.css";

const MainContent = () => {
  // Datos de información principal
  const data: MainInformationProps = informationItems;

  return (
    <Container
      maxWidth="lg"
      className={`${styles.mainInfoContainer} scroll-animate`}
    >
      {/* Título de la sección */}
      <div className={styles.infoTitle}>¿Cómo funciona?</div>
      <Box
        display="flex"
        flexWrap="wrap"
        justifyContent="space-between"
        gap={3}
        sx={{ width: "100%" }}
        className={styles.mainInfoContainer}
      >
        {/* Mapeo de los elementos de información */}
        {data.informationItems.map((item, index) => (
          <Box
            key={index}
            sx={{
              flex: 1,
              minWidth: "280px",
              p: 2,
            }}
            className="scroll-animate"
          >
            <InfoContent
              title={item.title}
              text={item.text}
              icon={item.icon}
              backIcon={item.backIcon}
            />
          </Box>
        ))}
      </Box>
    </Container>
  );
};
export default MainContent;
