import { StaticImageData } from "next/image";

/**
 * Interfaz para las propiedades del pie de página móvil.
 * 
 * @interface MobileFooterProps
 * @prop {Array} navigationItems - Lista de elementos de navegación.
 * @prop {string} navigationItems[].label - Etiqueta del elemento de navegación.
 * @prop {string} navigationItems[].icon - Icono del elemento de navegación.
 * @prop {string} navigationItems[].selectedIcon - Icono del elemento de navegación cuando está seleccionado.
 */
export interface MobileFooterProps {
    navigationItems: { label: string; icon: string, selectedIcon: string }[];
}
/**
 * Interfaz para los elementos del banner.
 * 
 * @interface bannerItem
 * @prop {string} text - Texto del banner.
 * @prop {string} buttonText - Texto del botón del banner.
 * @prop {string} buttonLink - Enlace del botón del banner.
 */
export interface bannerItem {
    item: {
        title: string,
        description: string,
        image: StaticImageData,
        link: {
            text: string,
            url: string,
            more: string
        },
    }[];

}
/**
 * Interfaz para las propiedades de la información principal.
 * 
 * @interface MainInformationProps
 * @prop {Array} informationItems - Lista de elementos de información.
 * @prop {string} informationItems[].title - Título del elemento de información.
 * @prop {string} informationItems[].text - Texto del elemento de información.
 * @prop {string} informationItems[].icon - Icono principal del elemento de información.
 * @prop {string} informationItems[].backIcon - Icono de fondo del elemento de información.
 */
export interface MainInformationProps {
  informationItems: { title:string; text: string; icon: string, backIcon:string }[];
}

