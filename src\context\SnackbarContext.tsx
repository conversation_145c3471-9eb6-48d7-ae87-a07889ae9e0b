"use client";

import { createContext, useContext, useState } from "react";
import { Alert } from "@mui/material";
import {
  CheckCircleIcon,
  LoaderCircleIcon,
  AlertCircleIcon,
} from "lucide-react";

type SnackbarVariant = "success" | "error" | "loading";

type SnackbarContextType = {
  showSuccess: (message: string) => void;
  showError: (message: string) => void;
  showLoading: (message: string) => void;
  hide: () => void;
};

const SnackbarContext = createContext<SnackbarContextType | undefined>(
  undefined
);

export const SnackbarProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [message, setMessage] = useState("");
  const [open, setOpen] = useState(false);
  const [variant, setVariant] = useState<SnackbarVariant>("success");

  const showSnackbar = (msg: string, type: SnackbarVariant) => {
    setMessage(msg);
    setVariant(type);
    setOpen(true);

    if (type !== "loading") {
      setTimeout(() => setOpen(false), 4000);
    }
  };

  const showSuccess = (msg: string) => showSnackbar(msg, "success");
  const showError = (msg: string) => showSnackbar(msg, "error");
  const showLoading = (msg: string) => showSnackbar(msg, "loading");

  const hide = () => setOpen(false);

  const getAlertStyle = () => {
    switch (variant) {
      case "success":
        return {
          color: "#72E128",
          backgroundColor: "#e3fcd2",
          icon: <CheckCircleIcon fontSize="inherit" />,
        };
      case "error":
        return {
          color: "#FF3B3B",
          backgroundColor: "#ffe2e2",
          icon: <AlertCircleIcon fontSize="inherit" />,
        };
      case "loading":
        return {
          color: "#FFB020",
          backgroundColor: "#fff7d0",
          icon: (
            <LoaderCircleIcon className="animate-spin" fontSize="inherit" />
          ),
        };
    }
  };

  const { color, backgroundColor, icon } = getAlertStyle();

  return (
    <SnackbarContext.Provider
      value={{ showSuccess, showError, showLoading, hide }}
    >
      {children}
      {open && (
        <div
          style={{
            position: "fixed",
            top: 100,
            right: 20,
            zIndex: 9999,
          }}
        >
          <Alert
            onClose={hide}
            severity={variant === "loading" ? "info" : variant}
            icon={icon}
            variant="filled"
            sx={{
              display: "flex",
              borderRadius: "8px",
              color,
              backgroundColor,
              fontSize: "1rem",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.3)",
              padding: "14px 16px !important",
              fontWeight: 300,
              gap: "8px",
            }}
          >
            {message}
          </Alert>
        </div>
      )}
    </SnackbarContext.Provider>
  );
};

export const useSnackbar = () => {
  const context = useContext(SnackbarContext);
  if (!context) {
    throw new Error("useSnackbar debe usarse dentro de un SnackbarProvider");
  }
  return context;
};
