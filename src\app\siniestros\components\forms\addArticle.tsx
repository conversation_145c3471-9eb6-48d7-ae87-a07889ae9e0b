import { Button } from "@mui/material";
import { XIcon } from "lucide-react";
import { btnStyles, screenContentStyle, screenOverlayStyle } from "../styles";
import { useClaims } from "@/hooks/useClaim";
import { useSearchParams } from "next/navigation";
import DocumentUpload from "./DocumentUpload";
import FormField from "./FormField";
import { useArticleForm } from "./hooks/useArticleForm";

interface Article {
  cobertura: string;
  articulo: string;
  valor: string;
  documentos?: File[];
}

interface ClaimRequirementsProps {
  open: boolean;
  setOpen: () => void;
  onAddArticle?: (article: Article) => void;
  editingArticle?: Article | null;
  isEditing?: boolean;
}

const ClaimRequirements = ({
  open,
  setOpen,
  onAddArticle,
  editingArticle,
  isEditing,
}: ClaimRequirementsProps) => {
  const { claims } = useClaims();
  const searchParams = useSearchParams();
  const claimId = searchParams.get("claim");

  const selectedClaim = claimId
    ? claims.find((claim) => claim.id.toString() === claimId)
    : null;

  const hasRequiredDocuments = selectedClaim?.coberturas.some((cob) =>
    cob.documentos?.some((doc) => doc.obligatorio)
  ) || false;

  const {
    formData,
    errors,
    handleInputChange,
    handleAddDocument,
    handleRemoveDocument,
    validateForm,
    resetForm,
  } = useArticleForm({
    editingArticle,
    isEditing,
    open,
    hasRequiredDocuments,
  });

  const selectedCoverage = formData.cobertura
    ? selectedClaim?.coberturas.find(
        (cob) => cob.id.toString() === formData.cobertura,
      )
    : null;
  const requiredDocuments = selectedCoverage?.documentos || [];
  const canUploadDocuments = formData.cobertura && formData.articulo;

  const handleSave = () => {
    if (validateForm() && onAddArticle) {
      onAddArticle(formData);
      resetForm();
      setOpen();
    }
  };

  if (!open) return null;

  const modalContentStyle: React.CSSProperties = {
    ...screenContentStyle,
    borderRadius: "10px",
    maxWidth: "600px",
    width: "100%",
    height: "fit-content",
    padding: "2rem",
    overflow: "auto",
  };

  return (
    <div style={screenOverlayStyle} onClick={(e) => e.stopPropagation()}>
      <div style={modalContentStyle} onClick={(e) => e.stopPropagation()}>
        <header
          style={{
            display: "flex",
            justifyContent: "space-between",
            boxShadow: "none",
          }}
        >
          <h2>{isEditing ? "Editar artículo" : "Agregar artículo"}</h2>
          <button
            style={{
              border: "none",
              background: "none",
              alignSelf: "flex-end",
              cursor: "pointer",
            }}
            onClick={setOpen}
          >
            <XIcon />
          </button>
        </header>

        <form style={{ flex: "1" }}>
          <FormField
            label="Cobertura"
            error={errors.cobertura}
            errorMessage="Este campo es requerido"
          >
            <select
              value={formData.cobertura}
              onChange={(e) => handleInputChange("cobertura", e.target.value)}
              style={{
                border: `3px solid ${errors.cobertura ? "#ff0000" : "#51519b"}`,
                borderRadius: "15px",
                padding: "8px",
              }}
            >
              <option value="">Selecciona una cobertura</option>
              {claimId &&
                claims
                  .find((claim) => claim.id.toString() === claimId)
                  ?.coberturas.map((cobertura) => (
                    <option key={cobertura.id} value={cobertura.id}>
                      {cobertura.name}
                    </option>
                  ))}
            </select>
          </FormField>
          <FormField
            label="Artículo"
            error={errors.articulo}
            errorMessage="Este campo es requerido"
          >
            <input
              type="text"
              value={formData.articulo}
              onChange={(e) => handleInputChange("articulo", e.target.value)}
              style={{
                border: `3px solid ${errors.articulo ? "#ff0000" : "#51519b"}`,
                borderRadius: "15px",
                padding: "8px",
              }}
            />
          </FormField>
          {hasRequiredDocuments && (
            <>
              {!canUploadDocuments ? (
                <div
                  style={{
                    marginTop: "2rem",
                    padding: "1rem",
                    backgroundColor: "#fef3cd",
                    border: "1px solid #f59e0b",
                    borderRadius: "8px",
                    fontSize: "0.9rem",
                    color: "#92400e",
                  }}
                >
                  <strong>Nota:</strong> Completa los campos de Cobertura y
                  Artículo para poder subir documentos.
                </div>
              ) : (
                <DocumentUpload
                  documents={formData.documentos || []}
                  requiredDocuments={requiredDocuments}
                  onAddDocument={handleAddDocument}
                  onRemoveDocument={handleRemoveDocument}
                  hasError={errors.documentos}
                />
              )}
            </>
          )}
          <FormField
            label="Valor aproximado"
            error={errors.valor}
            errorMessage="Este campo es requerido"
          >
            <input
              type="number"
              value={formData.valor}
              onChange={(e) => handleInputChange("valor", e.target.value)}
              style={{
                border: `3px solid ${errors.valor ? "#ff0000" : "#51519b"}`,
                borderRadius: "15px",
                padding: "8px",
              }}
            />
          </FormField>
        </form>

        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            ...btnStyles,
            borderRadius: "30px",
            height: "50px",
            width: "100%",
          }}
        >
          {isEditing ? "Actualizar" : "Guardar"}
        </Button>
      </div>
    </div>
  );
};

export default ClaimRequirements;
