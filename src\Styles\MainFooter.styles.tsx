import styled from "styled-components";

export const MainFooterContainer = styled.footer`
  display: grid;
  justify-items: center;
  grid-template-columns: repeat(3, 1fr);
  gap: 48px;
  max-width: 120rem;
  margin: 0 auto;
  align-items: center;

  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    .app-section {
      display: flex;
      gap: 10px;
      flex-direction: column;
    }
  }

  @media (max-width: 1440px) {
    padding: 16px;
    margin: 0;
    grid-template-columns: repeat(3, 1fr);
    gap: 60px 48px;
  }

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
`;

export const ContentTextFooter = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
  line-height: 1.5;

  h4 {
    font-family: var(--font-inter);
    font-weight: 400;
    font-size: 1.125 rem;
    color: #ffffff;
    text-decoration: none;
  }

  @media (max-width: 768px) {
    align-items: center;
  }
`;

export const LinkFontFooter = styled.a`
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 1rem;
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
  background: none;
  border: none;
  font: inherit;
  padding: 0;
  display: inline;
`;

export const PFontFooter = styled.p`
  color: var(--Text-White, #fff);

  /* Wiki/Common/Small Body */
  font-family: Poppins;
  font-size: 1rem !important;
  font-style: normal;
  font-weight: 400 !important;
  line-height: normal;
`;

export const DividerContainer = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
`;
