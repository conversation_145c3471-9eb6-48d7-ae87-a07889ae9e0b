import { Box } from "@mui/material";
import Image from "next/image";
import React from "react";
import styles from "../styles.module.css";

/**
 * Props que recibe el componente ContactButton
 * @property {() => void} onClick - Función que se ejecutará al hacer clic en el botón.
 * @property {string} iconSrc - Ruta del ícono que se mostrará en el botón.
 * @property {string} label - Texto que se mostrará en el botón.
 * @property {string} [iconAlt] - Texto alternativo para el ícono (opcional).
 */
type ContactButtonProps = {
  onClick: () => void; // Función que se ejecutará al hacer clic
  iconSrc: string; // Ruta del ícono
  label: string; // Texto del botón
  iconAlt?: string; // Texto alternativo para el ícono
};

/**
 * Componente ContactButton
 * Renderiza un botón de contacto con un ícono y una etiqueta.
 * @param {() => void} onClick - Función que se ejecutará al hacer clic en el botón.
 * @param {string} iconSrc - Ruta del ícono que se mostrará en el botón.
 * @param {string} label - Texto que se mostrará en el botón.
 * @param {string} [iconAlt] - Texto alternativo para el ícono (opcional).
 */
export const ContactButton: React.FC<ContactButtonProps> = ({
  onClick,
  iconSrc,
  label,
  iconAlt = "",
}) => {
  return (
    <Box className={styles.contactButton} onClick={onClick}>
      <Image
        className={styles.contactButtonIcon}
        width={24}
        height={24}
        alt={iconAlt}
        src={iconSrc}
      />
      <Box className={styles.contactButtonLabel}>{label}</Box>
    </Box>
  );
};
