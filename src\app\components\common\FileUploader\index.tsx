import React, { useRef, useState } from 'react';
import styled from 'styled-components';
import { CloudUpload, Description, Delete } from '@mui/icons-material';
import { DOCUMENT_VALIDATION } from '@/types';

interface FileUploaderProps {
  onFileSelect: (file: File | null) => void;
  currentFile?: File | string;
  disabled?: boolean;
  accept?: string;
  maxSizeMB?: number;
}

const UploaderContainer = styled.div<{ disabled?: boolean; hasError?: boolean }>`
  border: 2px dashed ${props => props.hasError ? '#ef4444' : props.disabled ? '#d1d5db' : '#51519b'};
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  background-color: ${props => props.disabled ? '#f9fafb' : '#fafbff'};
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: ${props => props.disabled ? '#f9fafb' : '#f0f2ff'};
    border-color: ${props => props.disabled ? '#d1d5db' : '#51519b'};
  }
`;

const UploadIcon = styled.div`
  margin-bottom: 12px;
  color: #51519b;
`;

const UploadText = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
`;

const UploadSubtext = styled.div`
  font-size: 14px;
  color: #6b7280;
`;

const FileInfoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8fffe;
  border: 1px solid #10b981;
  border-radius: 8px;
  margin-top: 12px;
`;

const FileInfo = styled.div`
  flex: 1;
  text-align: left;
`;

const FileName = styled.div`
  font-weight: 500;
  color: #374151;
  font-size: 14px;
`;

const FileSize = styled.div`
  font-size: 12px;
  color: #6b7280;
`;

const DeleteButton = styled.button`
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #fee2e2;
  }
`;

const ErrorMessage = styled.div`
  color: #ef4444;
  font-size: 14px;
  margin-top: 8px;
  text-align: left;
`;

const HiddenInput = styled.input`
  display: none;
`;

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const FileUploader: React.FC<FileUploaderProps> = ({
  onFileSelect,
  currentFile,
  disabled = false,
  accept = '.pdf,.jpg,.jpeg,.png',
  maxSizeMB = DOCUMENT_VALIDATION.maxSize
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState<string>('');

  const validateFile = (file: File): string | null => {
    // Validar tamaño
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `El archivo es demasiado grande. Tamaño máximo: ${maxSizeMB}MB`;
    }

    // Validar formato
    const validExtensions = DOCUMENT_VALIDATION.formato.map(ext => ext.toLowerCase());
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!fileExtension || !validExtensions.includes(fileExtension)) {
      return `Formato no válido. Formatos permitidos: ${DOCUMENT_VALIDATION.formato.join(', ')}`;
    }

    return null;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    onFileSelect(file);
  };

  const handleClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const handleDeleteFile = (e: React.MouseEvent) => {
    e.stopPropagation();
    setError('');
    onFileSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const currentFileName = currentFile instanceof File ? currentFile.name : 
    typeof currentFile === 'string' ? currentFile.split('/').pop() || currentFile : '';
  
  const currentFileSize = currentFile instanceof File ? currentFile.size : 0;

  return (
    <div>
      <UploaderContainer
        onClick={handleClick}
        disabled={disabled}
        hasError={!!error}
      >
        <HiddenInput
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          disabled={disabled}
        />
        
        {!currentFile ? (
          <>
            <UploadIcon>
              <CloudUpload sx={{ fontSize: 48 }} />
            </UploadIcon>
            <UploadText>Selecciona un archivo o arrástralo aquí</UploadText>
            <UploadSubtext>
              Formatos: {DOCUMENT_VALIDATION.formato.join(', ')} · Máximo {maxSizeMB}MB
            </UploadSubtext>
          </>
        ) : (
          <FileInfoContainer>
            <Description sx={{ color: '#10b981', fontSize: 24 }} />
            <FileInfo>
              <FileName>{currentFileName}</FileName>
              {currentFileSize > 0 && (
                <FileSize>{formatFileSize(currentFileSize)}</FileSize>
              )}
            </FileInfo>
            {!disabled && (
              <DeleteButton onClick={handleDeleteFile} title="Eliminar archivo">
                <Delete sx={{ fontSize: 20 }} />
              </DeleteButton>
            )}
          </FileInfoContainer>
        )}
      </UploaderContainer>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </div>
  );
};

export default FileUploader;
