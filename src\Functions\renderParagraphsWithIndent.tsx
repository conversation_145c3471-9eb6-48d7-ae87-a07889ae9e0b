import React from 'react';
import './renderParagraphsWithIndent.css';

export function renderParagraphsWithIndent(text: string) {
  // Si el texto contiene listas o varios bloques HTML, lo renderiza todo como un bloque indent-list
  const isComplexBlock = /<(ol|ul|li)[^>]*>/i.test(text);
  return (
    <div className="indent-box">
      <div className={isComplexBlock ? 'indent-list' : 'indent-content'}>
        {isComplexBlock ? (
          <div dangerouslySetInnerHTML={{ __html: text }} />
        ) : (
          text.split('\n\n').map((paragraph, index) => {
            const trimmed = paragraph.trim();
            if (trimmed.startsWith('<ul') || trimmed.startsWith('<ol')) {
              return <div key={index} className="indent-list" dangerouslySetInnerHTML={{ __html: paragraph }} />;
            }
            if (trimmed.startsWith('•') || trimmed.startsWith('<li')) {
              return <p key={index} className="indent-bullet" dangerouslySetInnerHTML={{ __html: paragraph }} />;
            }
            return <p key={index} className="indent-normal" dangerouslySetInnerHTML={{ __html: paragraph }} />;
          })
        )}
      </div>
    </div>
  );
}
