"use client";
import type { NextPage } from "next";
import { Typography, Box } from "@mui/material";
import styles from "./faq.module.css";
import FAQAccordion from "./accordion/FAQAccordion";
import Form from "./form/Form";
import { useEffect, useState } from "react";
import { getFAQs } from "@/infrastructure/services/faqService";
import { FAQData } from "@/infrastructure/models/faq.model";

const FAQ: NextPage = () => {
  // Estado para almacenar los datos de preguntas frecuentes
  const [faqData, setFaqData] = useState<FAQData | null>(null);

  // Efecto para obtener los datos de preguntas frecuentes al montar el componente
  useEffect(() => {
    const fetchFAQs = async () => {
      try {
        const data = await getFAQs();
        setFaqData(data);
      } catch (err) {
        console.log("Error get api faqs ", err);
      }
    };

    fetchFAQs();
  }, []);

  // Mapeo dinámico de categorías
  const categories = [
    { key: "generalidades_servicio", title: "Generalidades del Servicio" },
    { key: "acceso_gestion_de_cuenta", title: "Acceso y Gestión de la Cuenta" },
    { key: "pagos_facturacion", title: "Pagos y Facturación" },
    { key: "polizas_coberturas", title: "Pólizas y Coberturas" },
    { key: "siniestros", title: "Siniestros" },
  ];

  return (
    <Box className={styles.faq}>
      <main className={styles.root}>
        <section className={styles.homepage}>
          <Box className={styles.sectionPlanes}>
            <Box className={styles.bloqueDeTextoWrapper}>
              <Box className={styles.bloqueDeTexto}>
                {/* Encabezado */}
                <Box className={styles.textParent}>
                  <Box className={styles.text}>
                    <Typography
                      className={styles.holaSoyTikiSolo}
                      component="h1"
                      sx={{
                        fontFamily: "inherit",
                        fontWeight: "600",
                        fontSize: "inherit",
                        color: '#10265f',
                      }}
                    >
                      ¿Necesitas ayuda?
                    </Typography>
                  </Box>
                  <Box className={styles.text1}>
                    <Box className={styles.holaSoyTikiSolo1}>
                      Envíanos una consulta o haz clic en alguno de los
                      siguientes temas.
                    </Box>
                  </Box>
                </Box>
                {/* Acordeones */}
                <Box className={styles.serviceContentParent}>
                  <Box className={styles.serviceContent}>
                    <Box className={styles.instanceParent}>
                      {faqData &&
                        categories.map((category) => (
                          <FAQAccordion
                            key={category.key}
                            title={category.title}
                            data={
                              faqData[category.key as keyof FAQData]?.map(
                                (item) => ({
                                  question: item.question,
                                  answer: item.answer,
                                })
                              ) || []
                            }
                          />
                        ))}
                    </Box>
                  </Box>
                  {/* Formulario de contacto */}
                  <Form />
                </Box>
              </Box>
            </Box>
          </Box>
        </section>
      </main>
    </Box>
  );
};

export default FAQ;
