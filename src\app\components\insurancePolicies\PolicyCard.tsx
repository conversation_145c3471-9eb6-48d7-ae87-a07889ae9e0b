"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { ChevronUp, ChevronDown, Download, Edit } from "lucide-react";
import { formatDate } from "@/Functions/formatDate";
import { formatMoney } from "@/Functions/formatMoney";
import { Tooltip } from "@mui/material";
import Image from "next/image";

interface Policy {
  idProducto?: { name: string, mainImage: string };
  numeroPoliza: string;
  fechaInicioCobertura: string;
  fechaFinCobertura: string;
  estatus: string;
  montoPagado: string;
}

interface PolicyCardProps {
  policy: Policy;
  onDownload: (numeroPoliza: string) => void;
  onEdit: (numeroPoliza: string) => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
}

const getProductBgColor = (productName?: string) => {
  if (!productName) return "#eee";
  const name = productName.toLowerCase();
  if (name.includes("gadget")) return "#51519B";
  if (name.includes("phishing")) return "#AF8CC0";
  if (name.includes("asalto")) return "#7ABBD4";
  if (name.includes("rodada")) return "#89C598";
  return "#eee";
};

const PolicyCard: React.FC<PolicyCardProps> = ({
  policy,
  onDownload,
  onEdit,
  isExpanded,
  onToggleExpand,
}) => {
  const {
    idProducto,
    fechaInicioCobertura,
    fechaFinCobertura,
    estatus,
    montoPagado,
    numeroPoliza,
  } = policy;
  console.log("idProducto:", idProducto);

  console.log(
    "numeroPoliza:",
    numeroPoliza,
    "nombreProducto:",
    idProducto?.name
  );

  const getStatusStyles = () => {
    if (estatus === "activa") {
      return {
        backgroundColor: "#E8F5E8",
        color: "#2E7D32",
        border: "1px solid #4CAF50",
      };
    } else {
      return {
        backgroundColor: "#FFEBEE",
        color: "#C62828",
        border: "1px solid #F44336",
      };
    }
  };

  const router = useRouter();
  return (
    <div
      style={{
        backgroundColor: "white",
        borderRadius: "12px",
        overflow: "hidden",
        border: "1px solid #eee",
        marginBottom: "24px",
      }}
    >
      {/* Header con imagen */}
      <div
        style={{
          backgroundColor: getProductBgColor(idProducto?.name),
          height: "180px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
        }}
      >
        {idProducto?.mainImage ? (
          <Image
            src={idProducto.mainImage}
            alt={idProducto?.name || "Producto"}
            width={120}
            height={120}
            style={{
              maxWidth: "120px",
              maxHeight: "120px",
              objectFit: "contain",
            }}
            priority
          />
        ) : (
          <div style={{width:120, height:120, background:'#eee', borderRadius:'12px', display:'flex', alignItems:'center', justifyContent:'center', color:'#aaa', fontSize:'14px'}}>
            Sin imagen
          </div>
        )}
      </div>

      {/* Contenido principal */}
      <div style={{}}>
        {/* Título y estado */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
            borderBottom: "1px solid #eee",
            padding: "5px",
          }}
        >
          <h3
            style={{
              fontSize: "20px",
              fontWeight: "600",
              color: "#10265f",
              margin: 0,
              padding: "20px",
            }}
          >
            {idProducto?.name}
          </h3>
          <span
            style={{
              ...getStatusStyles(),
              padding: "4px 12px",
              borderRadius: "16px",
              fontSize: "12px",
              fontWeight: "500",
              marginRight: "20px",
            }}
          >
            {estatus === "activa" ? "Vigente" : "Vencida"}
          </span>
        </div>

        {/* Botón Más información */}
        <button
          onClick={onToggleExpand}
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "8px",
            width: "100%",
            padding: "12px",
            backgroundColor: "transparent",
            border: "none",
            color: "#10265f",
            fontSize: "14px",
            fontWeight: "500",
            cursor: "pointer",
            borderRadius: "8px",
            marginBottom: "10px",
            transition: "background-color 0.2s ease",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "#F7FAFC";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
        >
          Más información
          {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </button>

        {/* Contenido expandido */}
        {isExpanded && (
          <div
            style={{
              marginTop: "16px",
              paddingTop: "16px",
              borderTop: "1px solid #eee",
              padding: "20px",
              animation: "slideDown 0.2s ease-out",
              overflow: "hidden",
            }}
          >
            {/* Información de la póliza */}
            <div style={{ marginBottom: "35px" }}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "8px",
                }}
              >
                <span style={{ color: "#4A5568", fontWeight: 500 }}>
                  Vigencia:
                </span>
                <span style={{ color: "#1a1a1a" }}>{`Del ${formatDate(
                  fechaInicioCobertura
                )} al ${formatDate(fechaFinCobertura)}`}</span>
              </div>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "8px",
                }}
              >
                <span style={{ color: "#4A5568", fontWeight: 500 }}>
                  Suma asegurada:
                </span>
                <span style={{ color: "#1a1a1a" }}>
                  {formatMoney(montoPagado)}
                </span>
              </div>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "8px",
                }}
              >
                <span style={{ color: "#4A5568", fontWeight: 500 }}>
                  Remanente:
                </span>
                <span style={{ color: "#1a1a1a" }}>
                  {formatMoney(montoPagado)}
                </span>
              </div>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "8px",
                }}
              >
                <span style={{ color: "#4A5568", fontWeight: 500 }}>
                  Póliza:
                </span>
                <span style={{ color: "#1a1a1a" }}>{numeroPoliza}</span>
              </div>
            </div>

            {/* Botones de acción */}
            <div
              style={{
                display: "flex",
                gap: "35px", // más separación entre grupos
                justifyContent: "center",
                marginTop: "8px",
              }}
            >
              {/* Descargar */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Tooltip
                  placement="top"
                  title={
                    <span
                      style={{
                        padding: "16px",
                        display: "block",
                        marginBottom: "8px",
                      }}
                    >
                      Se descargará la póliza, condiciones particulares,
                      condiciones generales y, si aplica, el documento de endoso
                      aprobado por la aseguradora. El archivo se descargará en
                      formato .zip
                    </span>
                  }
                  slotProps={{
                    tooltip: {
                      sx: {
                        backgroundColor: "#ffffff",
                        color: "black",
                        fontSize: "0.8rem",
                        fontFamily: "Poppins, sans-serif",
                        borderRadius: "8px",
                        boxShadow: "0px 4px 12px rgba(0,0,0,0.12)",
                      },
                    },
                  }}
                >
                  <button
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "15px",
                      backgroundColor:
                        estatus === "activa" ? "#10265f" : "#ccc",
                      color: "white",
                      border: "none",
                      borderRadius: "50%",
                      width: "48px",
                      height: "48px",
                      cursor: estatus === "activa" ? "pointer" : "not-allowed",
                      opacity: estatus === "activa" ? 1 : 0.7,
                      transition: "background-color 0.2s ease",
                      marginBottom: "6px",
                    }}
                    onClick={() =>
                      estatus === "activa" && onDownload(numeroPoliza)
                    }
                    disabled={estatus !== "activa"}
                  >
                    <Download size={18} />
                  </button>
                </Tooltip>
                <span
                  style={{
                    fontSize: "12px",
                    color: "#718096",
                    textAlign: "center",
                    width: "60px",
                  }}
                >
                  Descargar
                </span>
              </div>

              {/* Endoso */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Tooltip
                  placement="top"
                  title={
                    <span
                      style={{
                        padding: "16px",
                        display: "block",
                        marginBottom: "8px",
                      }}
                    >
                      Haz clic en el ícono para iniciar un proceso de endoso o
                      cancelación de tu póliza.
                    </span>
                  }
                  slotProps={{
                    tooltip: {
                      sx: {
                        backgroundColor: "#ffffff",
                        color: "black",
                        fontSize: "0.8rem",
                        fontFamily: "Poppins, sans-serif",
                        borderRadius: "8px",
                        boxShadow: "0px 4px 12px rgba(0,0,0,0.12)",
                      },
                    },
                  }}
                >
                  <span>
                    <button
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "15px",
                        backgroundColor:
                          estatus === "activa" ? "#10265f" : "#ccc",
                        color: "white",
                        border: "none",
                        borderRadius: "50%",
                        width: "48px",
                        height: "48px",
                        cursor:
                          estatus === "activa" ? "pointer" : "not-allowed",
                        opacity: estatus === "activa" ? 1 : 0.7,
                        transition: "background-color 0.2s ease",
                        marginBottom: "6px",
                      }}
                    onClick={() => {
                      if (estatus === "activa") {
                        onEdit(numeroPoliza);
                        router.push('/endoso/create');
                      }
                    }}
                      disabled={estatus !== "activa"}
                    >
                      <Edit size={18} />
                    </button>
                  </span>
                </Tooltip>
                <span
                  style={{
                    fontSize: "12px",
                    color: "#718096",
                    textAlign: "center",
                    width: "60px",
                  }}
                >
                  Endoso
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default PolicyCard;
