"use client";
import React, { useEffect } from "react";
import { useLoginModalContext } from "@/app/LoginProvider";
import {
  Overlay,
  CustomModal,
  Container,
  ModalContent,
  CloseButton,
  IconContainer,
  Title,
  Subtitle,
  DataContainer,
  DataRow,
  DataLabel,
  DataValue,
  StatusBadge,
  ActionButton,
  FooterText
} from "./ExistingAccountModal.styles";
import { Close } from "@mui/icons-material";
import Image from "next/image";

interface ExistingAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  email: string;
  phone: string;
  errorMessage: string;
}

const ExistingAccountModal: React.FC<ExistingAccountModalProps> = ({
  isOpen,
  onClose,
  email,
  phone,
  errorMessage
}) => {
  const { openModal } = useLoginModalContext();

  // Bloquear scroll del body cuando el modal esté abierto
  useEffect(() => {
    if (isOpen) {
      // Guardar el scroll actual
      const scrollY = window.scrollY;

      // Bloquear scroll
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.overflow = 'hidden';

      return () => {
        // Restaurar scroll al cerrar
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflow = '';
        window.scrollTo(0, scrollY);
      };
    }
  }, [isOpen]);

  // Determinar qué datos ya existen
  let isEmailExisting = false;
  let isPhoneExisting = false;

  try {
    // Intentar parsear como nueva estructura JSON
    const conflictInfo = JSON.parse(errorMessage);
    if (conflictInfo.inUse) {
      isEmailExisting = conflictInfo.inUse.email || false;
      isPhoneExisting = conflictInfo.inUse.phone || false;
    } else {
      throw new Error('No inUse property found');
    }
  } catch {
    // Fallback al método anterior basado en texto
    isEmailExisting = errorMessage.toLowerCase().includes('email') || errorMessage.toLowerCase().includes('correo');
    isPhoneExisting = errorMessage.toLowerCase().includes('teléfono') || errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('número');
  }

  // Determinar si solo el teléfono es existente (caso especial)
  const isOnlyPhoneExisting = isPhoneExisting && !isEmailExisting;

  const handleAction = () => {
    onClose();
    if (isOnlyPhoneExisting) {
      // Si solo el teléfono es existente, no hacer nada más (permitir corrección)
      return;
    } else {
      // Si el email existe o ambos existen, ir al login
      openModal();
    }
  };

  if (!isOpen) return null;

  return (
    <Overlay>
      <CustomModal>
        <Container>
          <ModalContent>
            <CloseButton onClick={onClose}>
              <Close />
            </CloseButton>

            <IconContainer>
              <Image
                src="/web/img/modals/existing-account-icon.svg"
                alt="Cuenta existente"
                width={200}
                height={120}
                priority
              />
            </IconContainer>

            <Title>Ya tienes una cuenta con nosotros</Title>
            <Subtitle>
              {isOnlyPhoneExisting
                ? "¡Ups! Este número ya está en uso. Prueba con otro, por favor."
                : "Detectamos que los datos que ingresaste ya están vinculados a una cuenta existente."
              }
            </Subtitle>

            <DataContainer>
              <DataRow>
                <DataLabel>Email:</DataLabel>
                <DataValue>{email}</DataValue>
                <StatusBadge $isExisting={isEmailExisting}>
                  {isEmailExisting ? 'Existente' : 'Nuevo'}
                </StatusBadge>
              </DataRow>
              
              <DataRow>
                <DataLabel>Teléfono:</DataLabel>
                <DataValue>{phone}</DataValue>
                <StatusBadge $isExisting={isPhoneExisting}>
                  {isPhoneExisting ? 'Existente' : 'Nuevo'}
                </StatusBadge>
              </DataRow>
            </DataContainer>

            <FooterText>
              {isOnlyPhoneExisting
                ? "Puedes actualizar tu número de teléfono para continuar con el registro. ¡Ya casi terminamos!"
                : "Inicia sesión para disfrutar de todos los beneficios que Wiki tiene para ti."
              }
            </FooterText>

            <ActionButton onClick={handleAction}>
              {isOnlyPhoneExisting ? "Corregir" : "Ir a Login"}
            </ActionButton>
          </ModalContent>
        </Container>
      </CustomModal>
    </Overlay>
  );
};

export default ExistingAccountModal;
