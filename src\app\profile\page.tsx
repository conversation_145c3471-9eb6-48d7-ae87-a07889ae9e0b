"use client";
import { useState } from "react";
import styled from "styled-components";
import ProfileViewer from "./ProfileViewer/ProfileViewer";
import { ProtectedLayout } from "@/core/layouts/ProtectedLayout";
import SecurityViewer from "./SecurityViewer/SecurityViewer";

const Profile = () => {
  const [selected, setSelected] = useState(0);

  const renderSelected = () => {
    switch (selected) {
      case 0:
        return (
          <>
            <ProfileViewer />;
          </>
        );
      case 1:
        return (
          <>
            <SecurityViewer />;
          </>
        );
      default:
        return (
          <>
            <ProfileViewer />;
          </>
        );
    }
  };
  return (
    <>
      <ProtectedLayout>
        <ProfileHeader>
          <h2>Perfil</h2>
          <div>
            <StyledSelectButton
              onClick={() => setSelected(0)}
              selected={selected === 0}
            >
              Información Personal
            </StyledSelectButton>
            <StyledSelectButton
              onClick={() => setSelected(1)}
              selected={selected === 1}
            >
              Seguridad y Privacidad
            </StyledSelectButton>
          </div>
        </ProfileHeader>
        {renderSelected()}
      </ProtectedLayout>
    </>
  );
};

export default Profile;

const ProfileHeader = styled.div<{ selected?: boolean }>`
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  h2 {
    display: flex;
    width: 100%;
    padding: 20px 60px;
    align-items: center;
    gap: 10px;
    background: var(--Segunda-Propuesta-Blossom, #ddd3e2);
    color: #10265f;
    font-family: Poppins;
    font-size: 36px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
  div {
    display: flex;
    width: 100%;
    padding: 20px 70px;
    align-items: center;
    gap: 30px;
    background: #f0f0f0;
  }
`;

const StyledSelectButton = styled.button<{ selected?: boolean }>`
  color: ${(props) => (props.selected ? "#AF8CC0" : "#7D7D7D")};
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: none;
  border: none;
  cursor: pointer;
  border-bottom:${(props) =>
    props.selected
      ? "2px solid #AF8CC0; color: #AF8CC0; font-weight: 500;"
      : "none"}
  &:hover {
    color: #af8cc0;
  }
`;
