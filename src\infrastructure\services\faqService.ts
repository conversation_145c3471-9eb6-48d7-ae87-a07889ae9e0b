import axiosInstance from '@/core/axios';
import { FAQData } from '../models/faq.model';

const FAQ_ENDPOINT = '/v1/faq';

/**
 * Función para obtener las preguntas frecuentes desde la API.
 * @returns {Promise<FAQData>} - Una promesa que resuelve con los datos de preguntas frecuentes.
 * @throws {Error} - Lanza un error si la solicitud falla.
 */
export const getFAQs = async (): Promise<FAQData> => {
  try {
    const response = await axiosInstance.get<FAQData>(FAQ_ENDPOINT);
    return response.data;
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    throw error;
  }
};

/**
 * Función para enviar los datos del formulario al backend.
 * @param {Object} formData - Datos del formulario a enviar.
 * @param {boolean} isLogged - Indica si el usuario está logueado.
 * @returns {Promise<void>} - Una promesa que se resuelve si la solicitud es exitosa.
 * @throws {Error} - Lanza un error si la solicitud falla.
 */
export const sendFormData = async (
  formData: {
    name: string;
    email: string;
    phone: string;
    message: string;
  },
  isLogged: boolean
): Promise<void> => {
  try {
    // Define el endpoint según el estado de autenticación
    const endpoint = isLogged ? '/v1/user/send-auth-email' : '/v1/user/send-public-email';

    // Configura los encabezados
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (isLogged) {
      const accessToken = window.localStorage.getItem('accessToken');
      if (!accessToken || accessToken === 'undefined') {
        throw new Error('No access token found');
      }
      headers['Authorization'] = `Bearer ${accessToken}`; // Agrega el token de autorización
    }

    // Realiza la solicitud POST
    const response = await axiosInstance.post(endpoint, formData, { headers });
    return response.data;
    //console.log("Formulario enviado con éxito:", response);
  } catch (error) {
    console.error('Error al enviar el formulario:', error);
    throw error;
  }
};
