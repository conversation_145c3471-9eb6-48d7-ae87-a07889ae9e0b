"use client";
import React, {
  useState,
  useRef,
  KeyboardEvent,
  ClipboardEvent,
  useEffect,
} from "react";
import { Box, Typography } from "@mui/material";
import { ContainerInputs, Digit } from "../otp-code.styles";

interface OTPInputProps {
  setOtpSent: React.Dispatch<React.SetStateAction<string>>;
  errors?: boolean;
  setErrors: React.Dispatch<React.SetStateAction<boolean>>;
}

const OTPInput: React.FC<OTPInputProps> = ({
  setOtpSent,
  errors,
  setErrors,
}) => {
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (index: number, value: string) => {
    if (isNaN(Number(value))) return;

    const newOtp = [...otp];
    newOtp[index] = value.slice(-1);
    setOtp(newOtp);

    // Move to next input if value is entered
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Check if OTP is complete
    if (newOtp.every((val) => val !== "")) {
      setOtpSent(newOtp.join(""));
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      // Move to previous input on backspace if current input is empty
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").slice(0, 6);

    if (!/^\d+$/.test(pastedData)) return;

    const newOtp = [...otp];
    pastedData.split("").forEach((char, index) => {
      if (index < 6) {
        newOtp[index] = char;
      }
    });
    setOtp(newOtp);

    // Focus last input or the next empty input
    const lastFilledIndex = newOtp.findIndex((val) => !val);
    const focusIndex = lastFilledIndex === -1 ? 5 : lastFilledIndex;
    inputRefs.current[focusIndex]?.focus();

    if (newOtp.every((val) => val !== "")) {
      setOtpSent(newOtp.join(""));
    }
  };

  useEffect(() => {
    if (otp.every((val) => val === "")) {
      setErrors(false);
    }
  }, [otp, setErrors]);

  return (
    <ContainerInputs>
      <Box display="flex" gap={2}>
        {otp.map((digit, index) => (
          <Digit
            key={index}
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
            type="text"
            inputMode="numeric"
            maxLength={1}
            value={digit}
            onChange={(e) => handleChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={handlePaste}
            style={errors ? { border: "1px solid red" } : undefined}
          />
        ))}
      </Box>
      <Typography
        variant="caption"
        color="error"
        sx={{ fontFamily: "Poppins", fontSize: "12px", fontWeight: "400" }}
      >
        {errors && "Código incorrecto"}
      </Typography>
    </ContainerInputs>
  );
};

export default OTPInput;
