import axios from '@/core/axios';

export class PoliciesServices {
  async getPolicies() {
    const accessToken = window.localStorage.getItem('accessToken');

    if (!accessToken || accessToken === 'undefined') {
      throw new Error('No access token found');
    }
    const response = await axios.get('v1/policies/user', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.data.policies;
  }

  async getPolicyPDFById(policyId: string) {
    const accessToken = window.localStorage.getItem('accessToken');

    if (!accessToken || accessToken === 'undefined') {
      throw new Error('No access token found');
    }

    const response = await axios.get(`v1/policies/pdf/${policyId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      responseType: 'blob', // importante para recibir el PDF como archivo
    });

    // Crear un objeto URL para el blob recibido
    const fileURL = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));
    return { file: fileURL };
  }
}
