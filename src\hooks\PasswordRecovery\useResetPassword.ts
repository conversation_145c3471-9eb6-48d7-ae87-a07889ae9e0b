import { AuthService } from "@/infrastructure/services/authService";
import { changePasswordDto, updatePasswordDto } from "@/infrastructure/services/dtos/passwordRecovery";
import { SendOtpCodeDto } from "@/infrastructure/services/dtos/register-dto";
import { useEffect, useState } from "react";

export const useResetPassword = () => {
  const authService = new AuthService();
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log("errorrrrrrrr", error);
  }, [error]);

  const otpCode = async (params: Partial<SendOtpCodeDto>) => {
    console.log("params", params);
    setLoading(true);
    try {
      const response = await authService.sendOtpCode(params);
      console.log("responseee otp", response);
      setError(null);
      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      setError(error as Error);
      console.log("responseee otp Failed to send otp code", error);
    } finally {
      setLoading(false);
    }
  };

  const otpCodeEmail = async (params: Partial<SendOtpCodeDto>) => {
    console.log("params", params);
    setLoading(true);
    try {
      const response = await authService.sendOtpCodeEmail(params);
      console.log("responseee otp", response);
      setError(null);
      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      setError(error as Error);
      console.log("responseee otp Failed to send otp code", error);
    } finally {
      setLoading(false);
    }
  };

  const verifyOtpCode = async (
    params: Partial<SendOtpCodeDto>,
    otpCode: string
  ) => {
    setLoading(true);
    try {
      const response = await authService.verifyOtpCode(params, otpCode);
      setError(null);
      return response;
    } catch (error) {
      setError(error as Error);
      console.error("Failed to verify otp code", error);
    } finally {
      setLoading(false);
    }
  };

  const verifyEmailOtpCode = async (
    params: Partial<SendOtpCodeDto>,
    otpCode: string
  ) => {
    setLoading(true);
    try {
      const response = await authService.verifyOtpCode(params, otpCode);
      setError(null);
      return response;
    } catch (error) {
      setError(error as Error);
      console.error("Failed to verify otp code", error);
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (values: updatePasswordDto) => {
    setLoading(true);
    console.log("values", values);
    try {
      const response = await authService.resetPassword(values);
      console.log("responseee reset password", response);
      setError(null);
      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.error(
        "responseee reset password Failed to reset password",
        error
      );
      setError(error);
    } finally {
      setLoading(false);
    }
  };


  const changePassword = async (values: changePasswordDto) => {
    setLoading(true);
    console.log("values", values);
    try {
      const response = await authService.changePassword(values);
      console.log("responseee change password", response);
      setError(null);
      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.error(
        "responseee change password Failed to change password",
        error
      );
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return {
    otpCode,
    error,
    loading,
    verifyOtpCode,
    verifyEmailOtpCode,
    setLoading,
    resetPassword,
    otpCodeEmail,
    changePassword,
  };
};
