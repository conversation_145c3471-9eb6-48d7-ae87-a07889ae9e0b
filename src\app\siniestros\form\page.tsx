'use client';

import { But<PERSON> } from "@mui/material";
import { ArrowLeft } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, Suspense, useEffect } from "react";
import { btnStyles, screenContentStyle, screenOverlayStyle } from "../components/styles";
import TalkAboutForm from "./talkAbout";
import BeforeEndForm from "./beforeEnd";
import ConfirmationModal from "../components/ConfirmationModal";
import { useClaimForm } from "./hooks/useClaimForm";

const SiniesterCreate = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const claimId = searchParams.get('claim');
  const [steps, setSteps] = useState<number>(1);
  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const { submitForm, isTalkAboutValid, isBeforeEndValid } = useClaimForm({ claimId: claimId ?? '' });

  // Usar los estados del hook centralizado pero mantener compatibilidad con componentes hijos
  const [isStep1Valid, setIsStep1Valid] = useState<boolean>(isTalkAboutValid);
  const [isStep2Valid, setIsStep2Valid] = useState<boolean>(isBeforeEndValid);

  // Sincronizar estados con el hook centralizado
  useEffect(() => {
    setIsStep1Valid(isTalkAboutValid);
  }, [isTalkAboutValid]);

  useEffect(() => {
    setIsStep2Valid(isBeforeEndValid);
  }, [isBeforeEndValid]);

  const handleFormSubmit = async () => {
    setIsSubmitting(true);
    try {
      const result = await submitForm();
      if (result.success) {
        setShowConfirmationModal(true); 
      } else {
        console.error('Error al enviar formulario:', result.message);
      }
    } catch (error) {
      console.error('Error inesperado:', error);
    } finally {
      setIsSubmitting(false);
    }
  };


  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <div style={screenOverlayStyle}>
        <div style={screenContentStyle}>
          <Button
            variant="contained"
            onClick={() => {
              if (steps === 1) {
                router.back();
              } else {
                setSteps(1);
              }
            }}
            sx={btnStyles}
          >
            <ArrowLeft />
          </Button>

          { steps === 1 && <TalkAboutForm onValidateForm={setIsStep1Valid} />}
          { steps === 2 && <BeforeEndForm onValidateForm={setIsStep2Valid} />}

          {steps === 1 && !isStep1Valid && (
            <span style={{ color: '#ff0000', textAlign: 'center' }}>Completa toda la información antes de continuar</span>
          )}
          {steps === 2 && !isStep2Valid && (
            <span style={{ color: '#ff0000', textAlign: 'center' }}>Completa toda la información antes de continuar</span>
          )}

          <Button
            variant="contained"
            onClick={() => {
              if (steps === 1) {
                setSteps(2);
              } else if (steps === 2) {
                // Mostrar modal de confirmación cuando ambos pasos están completos
                handleFormSubmit();
              }
            }}
            disabled={(steps === 1 && !isStep1Valid) || (steps === 2 && !isStep2Valid) || isSubmitting}
            sx={{
              ...btnStyles,
              alignSelf: 'center',
              borderRadius: "20px",
              height: "60px",
              minHeight: '60px',
              width: "200px",
              opacity: ((steps === 1 && !isStep1Valid) || (steps === 2 && !isStep2Valid) || isSubmitting) ? 0.5 : 1
            }}
          >
            {isSubmitting ? 'Enviando...' : (steps === 2 ? 'Finalizar' : 'Continuar')}
          </Button>
        </div>

        {/* Modal de Confirmación */}
        <ConfirmationModal
          isOpen={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          onConfirm={() => {
            setShowConfirmationModal(false);
            router.push("/siniestros")
          }}
          isLoading={isSubmitting}
        />
      </div>
    </Suspense>
  );
}


export default SiniesterCreate;
