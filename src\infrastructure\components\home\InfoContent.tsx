"use client";
import React from "react";
import Image from "next/image";
import styles from "@/infrastructure/styles/home.module.css";

/**
 * Componente InfoContent que renderiza el contenido de la información.
 *
 * @componente
 * @ejemplo
 * return (
 *   <InfoContent title="Título" text="Texto de la información" icon="icono" backIcon="iconoFondo" />
 * )
 *
 * @param {Object} props - Propiedades del componente.
 * @param {string} props.title - El título de la información.
 * @param {string} props.text - El texto de la información.
 * @param {string} props.icon - El nombre del icono principal.
 * @param {string} props.backIcon - El nombre del icono de fondo.
 *
 * @returns {JSX.Element} El componente InfoContent.
 */
const InfoContent = ({
  title,
  text,
  icon,
}: //   backIcon,
{
  title: string;
  text: string;
  icon: string;
  backIcon: string;
}) => {
  return (
    <>
      <div className={styles.infoContainer}>
        <Image
          src={`/web/img/mainAssets/${icon}.svg`}
          alt={`image-${icon}`}
          className={styles.infoIcon}
          width={50} // Adjust the width as needed
          height={50} // Adjust the height as needed
        />
        {/* Icono de fondo */}
        <div>
          {/* <img
            src={`/web/img/mainAssets/${backIcon}.svg`}
            alt={`image-${backIcon}`}
            className={styles.backIcon}
          /> */}
        </div>
        {/* Título de la información */}
        <p className={styles.infoSubTitle}>{title}</p>
        {/* Texto de la información */}
        <p className={styles.infoContent}>{text}</p>
      </div>
    </>
  );
};

export default InfoContent;
