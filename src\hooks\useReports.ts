import { useState, useCallback, useMemo } from 'react';
import { ReportsService } from '@/infrastructure/services/reportsService';
import {
  ReportListParams,
  // ReportListResponse,
  ReportItem,
  FiscalInfoRequest,
  AssociateDocsRequest
} from '@/types/reports';
import { Document, TimelineStep } from '@/types';

export const useReports = () => {
  // Estados para reportes
  const [reports, setReports] = useState<ReportItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });

  // Estados para documentos
  const [documents, setDocuments] = useState<Document[]>([]);
  const [timelineSteps, setTimelineSteps] = useState<TimelineStep[]>([]);

  // Estados para información fiscal
  const [fiscalSuccess, setFiscalSuccess] = useState(false);

  // Servicio único para todas las operaciones
  const reportsService = useMemo(() => new ReportsService(), []);

  /**
   * Transforma un ReportItem a formato legacy cuando sea necesario
   */
  const transformToLegacyFormat = useCallback((report: ReportItem) => ({
    id: report.id.toString(),
    fechaCreacion: report.createdAt,
    fechaIncidente: report.fechaIncidente,
    tipoIncidente: report.cobertura.name,
    numeroPoliza: report.poliza.numeroPoliza,
    resultado: report.totalReclamado,
    cobertura: report.cobertura.name,
    status: report.estatusReclamacion
  }), []);

  /**
   * Obtiene la lista de reportes con parámetros opcionales
   */
  const fetchReports = useCallback(async (params?: ReportListParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await reportsService.getReports(params);

      setReports(response.data);
      setPagination(response.meta);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error fetching reports:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Obtiene los detalles de un reporte específico por número de póliza
   */
  const fetchReportById = useCallback(async (numeroPoliza: string) => {
    try {
      setLoading(true);
      setError(null);

      // Si no tenemos reportes cargados, cargarlos primero
      if (reports.length === 0) {
        await fetchReports();
      }

      // Buscar el reporte por número de póliza en los datos ya cargados
      const report = reports.find(r => r.poliza.numeroPoliza === numeroPoliza);

      if (!report) {
        throw new Error(`No se encontró el reporte con póliza: ${numeroPoliza}`);
      }

      // Transformar al formato legacy para compatibilidad
      const legacyReport = transformToLegacyFormat(report);

      return legacyReport;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error fetching report details:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [reports, fetchReports, transformToLegacyFormat]);

  /**
   * Actualiza el estado de un reporte
   */
  const updateReportStatus = useCallback(async (reportId: string, status: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const updatedReport = await reportsService.updateReportStatus(reportId, { status });
      
      // Actualizar el reporte en la lista local
      setReports(prevReports =>
        prevReports.map(report =>
          report.id.toString() === reportId
            ? { ...report, estatusReclamacion: updatedReport.status }
            : report
        )
      );
      
      return updatedReport;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error updating report status:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // ==================== FUNCIONES DE DOCUMENTOS ====================

  /**
   * Obtiene los documentos de un reporte por número de póliza
   */
  const fetchDocuments = useCallback(async (numeroPoliza: string) => {
    try {
      setLoading(true);
      setError(null);

      const documentsData = await reportsService.getDocumentsByPoliza(numeroPoliza);
      setDocuments(documentsData);

      // Generar timeline basado en el primer documento
      const reportStatus = documentsData.length > 0 ? documentsData[0].estado : 'Documentos';
      const timeline = reportsService.generateTimelineSteps(reportStatus);
      setTimelineSteps(timeline);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error fetching documents:', err);
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  /**
   * Descarga un documento específico
   */
  const downloadDocument = useCallback(async (document: Document) => {
    if (!document.url) {
      setError('El documento no tiene una URL válida para descargar');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const downloadUrl = await reportsService.downloadDocument(document.id);

      // Crear enlace de descarga
      const link = window.document.createElement('a');
      link.href = downloadUrl;
      link.download = `${document.documento}-${document.producto}.pdf`;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

    } catch (err) {
      console.error('Error al descargar documento:', err);
      setError('Error al descargar el documento. Inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  /**
   * Sube un documento para un reporte específico
   */
  const uploadDocument = useCallback(async (numeroPoliza: string, documentId: string, file: File) => {
    try {
      setLoading(true);
      setError(null);

      const updatedDocument = await reportsService.uploadDocument(numeroPoliza, documentId, file);

      // Actualizar el documento en la lista local
      setDocuments(prev =>
        prev.map(doc =>
          doc.id === documentId
            ? { ...doc, ...updatedDocument, estado: 'Pendiente' }
            : doc
        )
      );

    } catch (err) {
      console.error('Error al subir documento:', err);
      setError('Error al subir el documento. Inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  /**
   * Obtiene estadísticas de progreso de los documentos
   */
  const getDocumentStats = useCallback(() => {
    let total = 0;
    let approved = 0;
    let rejected = 0;
    let pending = 0;

    documents.forEach(doc => {
      total++;
      switch (doc.estado) {
        case 'Aprobado': approved++; break;
        case 'Rechazado': rejected++; break;
        case 'Pendiente': pending++; break;
      }
    });

    return { total, approved, rejected, pending };
  }, [documents]);

  // ==================== FUNCIONES DE INFORMACIÓN FISCAL ====================

  /**
   * Guarda la información fiscal del usuario
   */
  const saveFiscalInfo = useCallback(async (data: FiscalInfoRequest) => {
    try {
      setLoading(true);
      setError(null);
      setFiscalSuccess(false);

      const response = await reportsService.saveFiscalInfo(data);

      setFiscalSuccess(true);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error saving fiscal info:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  /**
   * Asocia documentos fiscales precargados a una reclamación
   */
  const associateFiscalDocs = useCallback(async (data: AssociateDocsRequest) => {
    try {
      setLoading(true);
      setError(null);
      setFiscalSuccess(false);

      const response = await reportsService.associateFiscalDocs(data);

      setFiscalSuccess(true);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error associating fiscal docs:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  // ==================== FUNCIONES UTILITARIAS ====================

  /**
   * Obtiene un reporte específico por número de póliza de los datos ya cargados
   */
  const getReportByPoliza = useCallback((numeroPoliza: string): ReportItem | null => {
    return reports.find(r => r.poliza.numeroPoliza === numeroPoliza) || null;
  }, [reports]);

  /**
   * Refresca la lista de reportes
   */
  const refreshReports = useCallback((params?: ReportListParams) => {
    return fetchReports(params);
  }, [fetchReports]);

  /**
   * Limpia los errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Limpia el estado de éxito fiscal
   */
  const clearFiscalSuccess = useCallback(() => {
    setFiscalSuccess(false);
  }, []);

  /**
   * Resetea todos los estados
   */
  const reset = useCallback(() => {
    setError(null);
    setFiscalSuccess(false);
    setLoading(false);
    setDocuments([]);
    setTimelineSteps([]);
  }, []);

  return {
    // Estados de reportes
    reports,
    loading,
    error,
    pagination,

    // Estados de documentos
    documents,
    timelineSteps,

    // Estados de información fiscal
    fiscalSuccess,

    // Funciones de reportes
    fetchReports,
    fetchReportById,
    updateReportStatus,
    refreshReports,

    // Funciones de documentos
    fetchDocuments,
    downloadDocument,
    uploadDocument,
    getDocumentStats,

    // Funciones de información fiscal
    saveFiscalInfo,
    associateFiscalDocs,

    // Funciones utilitarias
    getReportByPoliza,
    transformToLegacyFormat,
    clearError,
    clearFiscalSuccess,
    reset,
  };
};
