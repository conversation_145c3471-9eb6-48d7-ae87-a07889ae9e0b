"use client";
import { Box, Typography } from "@mui/material";
import styles from "./styles.module.css";
import { FAQItem } from "@/infrastructure/models/faq.model";

/**
 * Props que recibe el componente QuestionsAnswers
 * @property {FAQItem[]} arrFAQ - Lista de preguntas y respuestas.
 * @property {boolean} isOpen - Indica si el acordeón está abierto.
 * @property {boolean} isHovered - Indica si el acordeón está en estado de hover.
 */
export type QuestionsAnswersType = {
  arrFAQ: FAQItem[];
  isOpen: boolean;
  isHovered: boolean;
};

/**
 * Componente QuestionsAnswers
 * Renderiza una lista de preguntas y respuestas asociadas a una categoría.
 * @param {FAQItem[]} arrFAQ - Lista de preguntas y respuestas.
 * @param {boolean} isOpen - Estado del acordeón (abierto o cerrado).
 * @param {boolean} isHovered - Estado de hover del acordeón.
 */
const QuestionsAnswers = ({
  arrFAQ,
  isOpen,
  isHovered,
}: QuestionsAnswersType) => {
  return (
    <Box className={styles.paymentInfo}>
      {/* Verifica si hay preguntas disponibles */}
      {arrFAQ.length > 0 ? (
        arrFAQ.map((item, index) => (
          <Box key={index} className={styles.cmoIngresoA}>
            {/* Pregunta */}
            <Typography
            className={styles.question}
              style={{
                color: isOpen || isHovered ? "#333333" : "#7d7d7d",
              }}
            >
              {item.question}
            </Typography>

            {/* Respuesta */}
            {isOpen && (
              <Typography className={styles.answer}>{item.answer}</Typography>
            )}
          </Box>
        ))
      ) : (
        <Typography className={styles.noQuestions}>
          No hay preguntas disponibles.
        </Typography>
      )}
    </Box>
  );
};

export default QuestionsAnswers;
