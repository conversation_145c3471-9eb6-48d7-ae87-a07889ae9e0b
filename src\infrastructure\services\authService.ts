/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/core/axios';
import { LoginDto, RegisterDto, SendOtpCodeDto, UnverifiedAccountError, CompleteOtpDto } from './dtos/register-dto';
import { FormattedData } from '../models/userData.model';
import { changePasswordDto, updatePasswordDto } from './dtos/passwordRecovery';

export class UnverifiedAccountException extends Error {
  public userInfo: UnverifiedAccountError['userInfo'];
  public code: string;
  public uuid?: string;

  constructor(message: string, code: string, userInfo: UnverifiedAccountError['userInfo'], uuid?: string) {
    super(message);
    this.name = 'UnverifiedAccountException';
    this.code = code;
    this.userInfo = userInfo;
    this.uuid = uuid;
  }
}

export class AuthService {
  //   private apiUrl = `${process.env.NEXT_PUBLIC_API_URL}`;

  async register(registerDto: RegisterDto) {
    const response = await axios.post('v1/user/register', {
      ...registerDto,
    });
    return response.data;
  }

  async sendOtpCode(params: Partial<SendOtpCodeDto>) {
    const response = await axios.post('v1/user/send-otp', {
      ...params,
    });
    return response.data;
  }

  async sendOtpCodeEmail(params: Partial<SendOtpCodeDto>) {
    console.log('params', params);
    try {
      const response = await axios.post('v1/user/send-otp', {
        ...params,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to send OTP code:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message);
      }
    }
  }

  async completeOtp(params: CompleteOtpDto) {
    const response = await axios.post('v1/user/complete-otp', {
      ...params,
    });
    return response.data;
  }

  async verifyOtpCode(params: Partial<SendOtpCodeDto>, otpCode: string) {
    const response = await axios.post(`v1/user/verify-otp?otp=${otpCode}`, {
      ...params,
    });

    if (response.data.accessToken) {
      window.localStorage.setItem('accessToken', response.data.accessToken);
    }

    return response.data;
  }

  async login({ email, password }: LoginDto) {
    try {
      const response = await axios.post('v1/auth/login', { email, password });
      window.localStorage.setItem('accessToken', response.data.accessToken);
      window.localStorage.setItem('refreshToken', response.data.refreshToken);
      return response.data;
    } catch (error: any) {
      // Verificar si el error es por cuenta no verificada
      if (error.response?.status === 403) {
        const errorData = error.response?.data;

        // Verificar si el mensaje indica cuenta no verificada
        if (errorData?.message === 'Usuario no verificado') {
          // Crear información básica del usuario con el email proporcionado
          const userInfo = {
            email: email,
            phone: errorData?.userInfo?.phone,
            firstName: errorData?.userInfo?.firstName,
            nickname: errorData?.userInfo?.nickname,
          };

          throw new UnverifiedAccountException(errorData.message, 'ACCOUNT_NOT_VERIFIED', userInfo, errorData?.uuid);
        }
      }

      // Re-lanzar el error original si no es de cuenta no verificada
      throw error;
    }
  }

  async getUserInfo() {
    const accessToken = window.localStorage.getItem('accessToken');

    if (!accessToken || accessToken === 'undefined') {
      throw new Error('No access token found');
    }

    const response = await axios.get('v1/user/get-token-auth0', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.data;
  }

  async updateProfile(profileData: FormattedData) {
    const accessToken = window.localStorage.getItem('accessToken');

    if (!accessToken || accessToken === 'undefined') {
      throw new Error('No access token found');
    }

    const response = await axios.post('v1/user/update-profile', profileData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.data;
  }

  async getMyProducts() {
    const accessToken = window.localStorage.getItem('accessToken');

    if (!accessToken || accessToken === 'undefined') {
      throw new Error('No access token found');
    }

    const response = await axios.get('v1/claims/contracted-products', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.data;
  }

  async resetPassword(recoveryData: updatePasswordDto) {
    const response = await axios.patch('v1/user/forget-password', recoveryData);

    return response;
  }

  async changePassword(data: changePasswordDto) {
    const accessToken = window.localStorage.getItem('accessToken');

    if (!accessToken || accessToken === 'undefined') {
      throw new Error('No access token found');
    }

    const response = await axios.patch('v1/user/reset-password', data, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.data;
  }

  async refreshToken() {
    const refreshToken = window.localStorage.getItem('refreshToken');

    if (!refreshToken) {
      throw new Error('No refresh token found');
    }

    const response = await axios.post('v1/auth/refresh-token', {
      refreshToken,
    });

    window.localStorage.setItem('accessToken', response.data.accessToken);
    window.localStorage.setItem('refreshToken', response.data.refreshToken);

    return response.data;
  }

  async getPostalCodeData(postalCode: string) {
    const response = await axios.get(`v1/user/info-postal-code?postalCode=${postalCode}`);
    return response.data;
  }
}
