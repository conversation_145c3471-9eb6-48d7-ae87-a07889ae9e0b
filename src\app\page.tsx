"use client";
import { ReactNode } from "react";
// import { Banner } from "@/infrastructure/components/home/<USER>";
import MainContent from "@/infrastructure/components/home/<USER>";
import { Products } from "@/infrastructure/components/products/Products";
// import NotificationContent from "@/infrastructure/components/home/<USER>";
import { useScrollAnimation } from "@/hooks/scrollanimation";
import styled from "styled-components";
import NewBanner from "@/infrastructure/components/home/<USER>";

const Section = ({
  children,
  className = "",
  style = {},
}: {
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
}) => (
  <section
    className={`max-w-7xl mx-auto px-4 py-12 ${className}`}
    style={style}
  >
    {children}
  </section>
);

// /**
//  * El componente Home renderiza la estructura principal de la página de inicio.
//  * Incluye el encabezado, la sección de notificaciones, el banner, los productos, el contenido principal y el pie de página.
//  *
//  * @returns {JSX.Element} El componente de la página de inicio renderizado.
//  */
// const FullWidthSection = ({
//   children,
//   className = "",
//   style = {},
// }: {
//   children: ReactNode;
//   className?: string;
//   style?: React.CSSProperties;
// }) => (
//   <div className={`w-full ${className}`} style={style}>
//     <div className="max-w-7xl mx-auto">{children}</div>
//   </div>
// );

export default function Home() {
  useScrollAnimation(".scroll-animate", {
    start: "top 95%", // Empieza cuando el elemento está al 75% del viewport
    toggleActions: "play none none reverse", // Reversa la animación al salir del viewport
    animationProps: {
      from: { opacity: 0, y: 100 }, // Cambia posición inicial
      to: { opacity: 1, y: 0 }, // Cambia posición final
    },
    once: true, // La animación se dispara solo una vez
  });
  return (
    <>
      {/* <FullWidthSection className="bg-green-500 px-4 py-2 text-white">
        <NotificationContent />
      </FullWidthSection> */}
      <main className="flex-1">
        {/* <FullWidthSection className="bg-[#f8f9fa] px-4 py-12"> */}
        {/* <Banner />
         */}

        <NewBanner />
        {/* </FullWidthSection> */}
        <Products />
        <TextSection>
          ¿Por qué complicarse con trámites? La vida es demasiado corta para
          eso. <br />
          ¡Fácil y sin complicaciones!
        </TextSection>
        <Section
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <MainContent />
        </Section>
      </main>
    </>
  );
}

const TextSection = styled.p`
  color: var(--Gris-muy-obscuro, #6d6d6d);
  width: 100%;
  max-width: 1080px;
  margin: 0 auto;
  text-align: center;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  padding: 70px 0;
`;
