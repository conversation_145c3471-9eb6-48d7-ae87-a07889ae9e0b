import Image from "next/image";
import {
  <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  StyledForm,
  StyledSubtitle,
  StyledTitle,
} from "../../Styles/RecoveryPassword.styles";
import { Formik, Field, ErrorMessage } from "formik";
import * as Yup from "yup";

const phoneValidationSchema = Yup.object().shape({
  phone: Yup.string()
    .required("El número de teléfono es obligatorio")
    .length(10, "El número de teléfono debe contener 10 dígitos")
    .matches(/^\d+$/, "El número de teléfono debe contener solo dígitos"),
});

type Props = {
  handleSendVerificationCode: (params: { phone: string }) => void;
  error: Error | null;
};

const SMS = ({ handleSendVerificationCode, error }: Props) => {
  return (
    <>
      <Image
        src="/web/img/cardsProducts/wikiHackeo.png"
        alt="Logo"
        width={195}
        height={180}
      />
      <StyledTitle>Recuperar <PERSON>ña</StyledTitle>
      <StyledSubtitle>
        Por favor, ingresa el teléfono con el cual te registraste para recibir
        un código de recuperación.
      </StyledSubtitle>
      <Formik
        initialValues={{ phone: "" }}
        validationSchema={phoneValidationSchema}
        onSubmit={(values) => {
          handleSendVerificationCode({
            phone: values.phone,
          });
        }}
      >
        {({ errors, touched, handleSubmit }) => (
          <StyledForm onSubmit={handleSubmit}>
            <Field name="phone" type="tel" placeholder="Número de teléfono" />
            {errors.phone && touched.phone && (
              <ErrorText>
                <ErrorMessage name="phone" />
              </ErrorText>
            )}
            {error && (
              <ErrorText>
                Error al enviar número. Intentalo nuevamente
              </ErrorText>
            )}
            <Sendbutton
              type="submit"
              disabled={!!errors.phone && touched.phone}
            >
              Enviar
            </Sendbutton>
          </StyledForm>
        )}
      </Formik>
    </>
  );
};

export default SMS;
