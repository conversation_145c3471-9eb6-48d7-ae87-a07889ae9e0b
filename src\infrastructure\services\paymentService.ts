import axios from '@/core/axios';

export interface PaymentRequest {
  proveedor: string;
  montoPagado: string; // Debe ser string, con hasta dos decimales
  notas?: string;
  planId: number;
}

export interface PaymentResponse {
  success: boolean;
  message: string;
  status: number; // 201 para éxito
  poliza?: string; // ID de la póliza creada, si aplica
  // Puedes agregar más campos según la respuesta del backend
}

export class PaymentService {
  async makePayment(data: PaymentRequest): Promise<PaymentResponse> {
    const res = await axios.post('v1/policies', data);
    return res.data as PaymentResponse;
  }
}
