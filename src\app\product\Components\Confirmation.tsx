'use client';
import { ButtonBase, TextField } from '@mui/material';
import gsap from 'gsap';
import React, { useEffect, useState } from 'react';
import { styled as styledMUI } from '@mui/material/styles';
import styled from 'styled-components';
import useAuth from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { CoverageType } from '../products.types';
import { usePayment } from '@/app/hooks/usePayment';
import { Check, CheckCircle, Close as CloseIcon, Error as ErrorIcon } from '@mui/icons-material';
import { useDownloadPolicyPDF } from '@/hooks/usePolicies';
import Image from 'next/image';
import axiosInstance from '@/core/axios';

const Confirmation = ({ selectedProduct, productName }: { selectedProduct: CoverageType | null; productName: { name: string; productId: string } }) => {
  const [viewPayment, setViewPayment] = useState(false);
  const [resultModal, setResultModal] = useState<{
    open: boolean;
    success: boolean;
    message: string;
    poliza?: string;
    status?: number;
  }>({ open: false, success: false, message: '', status: 200 });
  const { downloadPolicyPDF, loadingPDF } = useDownloadPolicyPDF();
  const { loading, handlePayment } = usePayment(
    (res) => {
      setResultModal({
        open: true,
        success: !!res.success,
        message: res.message || '¡Pago realizado correctamente!',
        poliza: res.poliza,
        status: typeof res.status === 'number' ? res.status : 200,
      });
    },
    (err: unknown) => {
      let errorMessage = 'Ocurrió un error al procesar el pago.';
      let status = 500;
      if (err && typeof err === 'object') {
        if ('message' in err && typeof (err as { message?: unknown }).message === 'string') {
          errorMessage = (err as { message: string }).message;
        }
        if ('status' in err && typeof (err as { status?: unknown }).status === 'number') {
          status = (err as { status: number }).status;
        }
      }
      setResultModal({ open: true, success: false, message: errorMessage, status });
    }
  );
  const { fetchUserInfo } = useAuth();
  const [userInfo, setUserInfo] = useState({
    firstName: '',
    lastNamePaternal: '',
    lastNameMaternal: '',
    birthDate: '',
    nickname: '',
    gender: '',
    street: '',
    exteriorNumber: '',
    interiorNumber: '',
    postalCode: '',
    neighborhood: '',
    municipality: '',
    email: '',
    confirmEmail: '',
    phone: '',
    userId: '',
  });

  const router = useRouter();

  // Estado para código promocional y descuento
  const [promoCode, setPromoCode] = useState('');
  const [discount, setDiscount] = useState<number | null>(null);
  const [typeDiscount, setTypeDiscount] = useState<'Monto fijo' | 'Porcentaje' | null>(null);
  const [promoError, setPromoError] = useState<string>('');
  const [promoLoading, setPromoLoading] = useState(false);

  const handlePromo = async () => {
    setPromoError('');
    setPromoLoading(true);

    try {
      const token = localStorage.getItem('accessToken'); // Obtén el token desde el almacenamiento local
      if (!token) {
        throw new Error('No se encontró un token de autenticación.');
      }

      const res = await axiosInstance.post(
        'v1/promotions/validate-code',
        {
          code: promoCode,
          userId: Number(userInfo.userId),
          productId: Number(productName.productId),
          insuredAmountId: Number(selectedProduct?.id),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`, // Agrega el token como Bearer
          },
        }
      );

      if (res.data.data?.discount) {
        console.log('Código promocional válido:', res.data);
        setDiscount(res.data.data.discount);
        setTypeDiscount(res.data.data.typeDiscount);
        setPromoError('');
      } else {
        setDiscount(null);
        setTypeDiscount(null);
        setPromoError('Código no válido o expirado');
      }
      //eslint-disable-next-line
    } catch (error: any) {
      console.error('Error al validar el código promocional:', error);
      setPromoError(error.response?.data?.message || 'Ocurrió un error al validar el código.');
      setDiscount(null);
    } finally {
      setPromoLoading(false);
    }
  };

  useEffect(() => {
    // Verifica si el elemento es encontrado
    gsap.fromTo(
      '.confirmation-elements',
      { opacity: 0, x: 100 },
      {
        opacity: 1,
        x: 0,
        duration: 0.7,
        delay: 0.5,
      }
    );
    fetchUserInfo().then((data) => {
      setUserInfo({
        firstName: data.firstName || '',
        lastNamePaternal: data.lastNamePaternal || '',
        lastNameMaternal: data.lastNameMaternal || '',
        birthDate: data.birthDate || '',
        nickname: data.nickname || '',
        gender: data.gender || '',
        street: data.addres?.street || '',
        exteriorNumber: data.addres?.exteriorNumber || '',
        interiorNumber: data.addres?.interiorNumber || '',
        postalCode: data.addres?.postalCode || '',
        neighborhood: data.addres?.neighborhood || '',
        municipality: data.addres?.municipality || '',
        email: data.email || '',
        confirmEmail: data.email || '',
        phone: data.phone || '',
        userId: data.userId || '',
      });
    });
  }, []);

  const handleConfirmation = () => {
    gsap.to('.confirmation-elements', {
      opacity: 0,
      x: -100,
      duration: 0.7,
      onComplete: () => {
        setViewPayment(true);
      },
    });
  };

  useEffect(() => {
    gsap.fromTo(
      '.payment-elements',
      { opacity: 0, x: 100 },
      {
        opacity: 1,
        x: 0,
        duration: 0.7,
        delay: 0.5,
      }
    );
  }, [viewPayment]);

  // Loader modal simple
  const LoaderModal = ({ open, text }: { open: boolean; text: string; onClose?: () => void }) => {
    if (!open) return null;
    // Detecta si es modal de resultado (éxito/error) o loader
    const isResult = typeof resultModal?.success === 'boolean' && resultModal.open;
    // Detecta si el status es diferente de 2xx (200-299)
    const isError = resultModal && typeof resultModal.status === 'number' && (resultModal.status < 200 || resultModal.status >= 300);
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(0,0,0,0.4)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 9999,
        }}
      >
        <div
          style={{
            background: '#fff',
            borderRadius: 20,
            padding: 40,
            fontSize: 22,
            fontFamily: 'Poppins',
            boxShadow: '0 4px 32px rgba(0,0,0,0.15)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            minWidth: 320,
            minHeight: 120,
            gap: 16,
            position: 'relative',
          }}
        >
          {/* Botón X arriba a la derecha solo en modal de resultado */}
          {isResult && (
            <button
              onClick={() => {
                if (isError) {
                  setResultModal({ ...resultModal, open: false });
                } else {
                  window.location.href = '/';
                }
              }}
              style={{
                position: 'absolute',
                top: 12,
                right: 12,
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                padding: 4,
                zIndex: 2,
              }}
              aria-label="Cerrar"
            >
              <CloseIcon style={{ fontSize: 28, color: '#888' }} />
            </button>
          )}
          {isResult ? (
            isError ? (
              <ErrorIcon style={{ color: '#e53935', fontSize: 48, marginBottom: 16 }} />
            ) : (
              <CheckCircle style={{ color: '#4caf50', fontSize: 48, marginBottom: 16 }} />
            )
          ) : (
            <span style={{ marginBottom: 16 }}>⏳</span>
          )}
          {text}
          {/* Botones extra solo en modal de resultado exitoso y no error */}
          {isResult && !isError && (
            <div style={{ display: 'flex', gap: 12, marginTop: 24 }}>
              <button
                style={{
                  padding: '8px 20px',
                  borderRadius: 20,
                  border: 'none',
                  background: '#10265f',
                  color: '#fff',
                  fontFamily: 'Poppins',
                  fontSize: 16,
                  cursor: 'pointer',
                }}
                onClick={() => {
                  // Redirigir a mis pólizas
                  window.location.href = '/insurance-policies';
                }}
              >
                Ir a mis pólizas
              </button>
              <button
                style={{
                  padding: '8px 20px',
                  borderRadius: 20,
                  border: 'none',
                  background: '#88cfec',
                  color: '#10265f',
                  fontFamily: 'Poppins',
                  fontSize: 16,
                  cursor: loadingPDF ? 'wait' : 'pointer',
                  opacity: loadingPDF ? 0.7 : 1,
                }}
                onClick={() => {
                  console.log('Descargando PDF de póliza:', resultModal);
                  if (resultModal.poliza) downloadPolicyPDF(resultModal.poliza);
                }}
              >
                {loadingPDF ? 'Generando PDF...' : 'Imprimir póliza'}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (viewPayment) {
    return (
      <>
        {loading && <LoaderModal open={true} text="Procesando pago..." />}
        {resultModal.open && (
          <LoaderModal
            open={true}
            text={resultModal.success ? resultModal.message : `${resultModal.message}`}
            onClose={() => setResultModal({ ...resultModal, open: false })}
          />
        )}
        <div
          className="payment-elements"
          style={{
            fontFamily: 'Poppins',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
            gap: '30px',
            position: 'relative',
            zIndex: 0,
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '10px',
              width: '100%',
              alignItems: 'center',
            }}
          >
            <RevisionTitleText>Detalles de pago</RevisionTitleText>
            <RevisionText>Es momento de elegir cómo quieres pagar.</RevisionText>
          </div>
          <Card style={{ maxWidth: '547px', borderRadius: '10px', gap: '38px' }}>
            <PaymentPruductTitle>{productName.name}</PaymentPruductTitle>
            <div
              style={{
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                gap: '10px',
                width: '100%',
                flexDirection: 'column',
              }}
            >
              {selectedProduct?.features.map((feature) => (
                <RevisionText style={{ textAlign: 'left' }} key={feature.concepto}>
                  {feature.icon ? <Image key={feature.concepto} src={feature.icon} alt={'Icon'} width={25} height={25} /> : <Check />} {feature.concepto}
                </RevisionText>
              ))}
            </div>
            <StyledTextField2>
              <input
                type="text"
                placeholder="¿Tienes un código promocional?"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
                disabled={promoLoading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !promoLoading && promoCode.trim()) {
                    handlePromo();
                  }
                }}
              />
              <button onClick={handlePromo} disabled={promoLoading || !promoCode.trim()}>
                {promoLoading ? 'Verificando...' : 'Aplicar'}
              </button>
            </StyledTextField2>
            {promoError && <RevisionText style={{ color: '#e53935', marginTop: 4 }}>{promoError}</RevisionText>}
            <StyledSection>
              <div>
                <p>Subtotal</p>
                <span>${selectedProduct?.precio}</span>
              </div>
              {discount && (
                <>
                  <div>
                    <p>Descuento</p>
                    <span>{typeDiscount === 'Monto fijo' ? `$ ${discount.toFixed(2)}` : typeDiscount === 'Porcentaje' ? `${Math.round(discount)}% ` : ''}</span>
                  </div>
                  {promoCode && (
                    <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: 8 }}>
                      <span style={{ fontSize: 13, color: '#5959a3', fontStyle: 'italic' }}>Cupón: {promoCode}</span>
                      <button
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#e53935',
                          fontSize: 14,
                          cursor: 'pointer',
                          marginLeft: 0,
                          padding: '2px 8px',
                          borderRadius: 8,
                          fontFamily: 'Poppins',
                          fontWeight: 600,
                          transition: 'background 0.2s',
                        }}
                        title="Eliminar descuento"
                        onClick={() => {
                          setDiscount(null);
                          setTypeDiscount(null);
                          setPromoCode('');
                          setPromoError('');
                        }}
                      >
                        Quitar
                      </button>
                    </div>
                  )}
                </>
              )}
            </StyledSection>
            <StyledSection>
              <div>
                <p className="total">Total a pagar</p>
                <p className="total">
                  <span>MXN</span> $
                  {(() => {
                    // Asegura que el precio sea un número, quitando comas
                    const precioNumber = Number((selectedProduct?.precio || '0').toString().replace(/,/g, ''));
                    function formatWithCommas(num: number) {
                      return num.toLocaleString('es-MX', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      });
                    }
                    if (discount && typeDiscount) {
                      let total = precioNumber;
                      if (typeDiscount === 'Monto fijo') {
                        total = precioNumber - discount;
                      } else if (typeDiscount === 'Porcentaje') {
                        total = precioNumber - (precioNumber * discount) / 100;
                      }
                      return total > 0 ? formatWithCommas(total) : '0.00';
                    }
                    return formatWithCommas(precioNumber);
                  })()}
                </p>
              </div>
            </StyledSection>
            <ActionButton
              onClick={() => {
                if (!selectedProduct) return;
                handlePayment({
                  proveedor: 'Zurich',
                  montoPagado: selectedProduct.precio,
                  notas: 'Pago realizado desde WikiFront',
                  planId: selectedProduct.id,
                  ...(promoCode ? { promoCode } : {}),
                });
              }}
            >
              Realizar pago
            </ActionButton>
          </Card>
        </div>
      </>
    );
  }

  return (
    <div
      className="confirmation-elements"
      style={{
        fontFamily: 'Poppins',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
        gap: '30px',
        zIndex: '2',
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '10px',
          width: '100%',
          alignItems: 'center',
        }}
      >
        <RevisionTitleText>Revisión de Datos</RevisionTitleText>
        <RevisionText>Por favor, revisa que tus datos aparezcan bien, es sólo para estar 100% seguro.</RevisionText>
      </div>

      <Card>
        <StyledTextField label="Póliza" variant="standard" multiline value={productName.name || 'pruebaProduct'} aria-readonly disabled />
        <StyledTextField
          label="Nombre"
          variant="standard"
          multiline
          aria-readonly
          value={`${userInfo.firstName} ${userInfo.lastNamePaternal} ${userInfo.lastNameMaternal}`}
          disabled
        />
        <StyledTextField label="Correo Electrónico" variant="standard" multiline aria-readonly value={userInfo.email} disabled />
        <StyledTextField label="Sexo" variant="standard" multiline aria-readonly value={userInfo.gender} disabled />
        <StyledTextField
          label="Fecha de Nacimiento"
          variant="standard"
          multiline
          aria-readonly
          value={
            userInfo.birthDate
              ? new Date(userInfo.birthDate).toLocaleDateString('es-MX', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                })
              : ''
          }
          disabled
        />
        <div
          style={{
            display: 'flex',
            gap: '20px',
            flexDirection: 'row',
            width: '100%',
          }}
        >
          <StyledTextField
            label="Dirección"
            variant="standard"
            sx={{ width: 'calc(100% - 85px)' }}
            multiline
            aria-readonly
            value={`${userInfo.street} ${userInfo.exteriorNumber} ${userInfo.interiorNumber}`}
            disabled
          />
          <ButtonBase
            sx={{ width: '60px', color: 'rgba(136, 207, 236, 1)' }}
            onClick={() => {
              router.push('/profile');
            }}
          >
            Editar
          </ButtonBase>{' '}
        </div>
        <ActionButton onClick={handleConfirmation}>Confirmar</ActionButton>
      </Card>
    </div>
  );
};

export default Confirmation;

const StyledTextField = styledMUI(TextField)({
  '& .MuiInputBase-root': {
    marginTop: '26px !important',
    color: 'rgba(89, 89, 163, 1)',
  },
  '& .MuiInputBase-input': {
    margin: '0 0 2px 2px !important',
    fontSize: '1.5rem',
  },
  '& .MuiInputLabel-root': {
    color: 'rgba(125, 125, 125, 1)', // Color por defecto
  },
  '& .MuiInputLabel-root.Mui-focused, & .MuiInputLabel-root.MuiFormLabel-filled': {
    color: '#10265F !important', // Color azul cuando está seleccionado;
    WebkitTextFillColor: '#10265F !important', // Evita que se vuelva gris
    fontFamily: 'Poppins',
    fontSize: '22px',
    fontWeight: 600,
  },
  '& .MuiInput-underline:after': {
    borderBottomColor: 'rgba(125, 125, 125, 1)',
  },

  '& .Mui-disabled': {
    color: 'rgba(89, 89, 163, 1) !important', // Mismo color que cuando está activo
    WebkitTextFillColor: '#333 !important', // Evita que se vuelva gris
    borderBottomColor: 'rgba(125, 125, 125, 1) !important', // Mantiene la línea
    opacity: 1, // Asegura que no se vea desvanecido
  },
  width: '100%',
});

const Card = styled.div`
  background-color: #fff;

  width: 100%;
  max-width: 1200px;
  padding: 32px 40px;
  text-align: left;
  position: relative;
  gap: 20px;
  display: flex;
  flex-direction: column;
  border-radius: 60px;
  border: 1px solid var(--Secondary-Blossom, #af8cc0);
  /* background: #fff; */

  @media (max-width: 768px) {
    padding: 20px;
    border-radius: 20px;
  }
`;

const RevisionTitleText = styled.h2`
  color: var(--Text-Navy, #10265f);
  text-align: center;
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
`;

const RevisionText = styled.p`
  color: var(--Gris-muy-obscuro, #6d6d6d);

  /* Wiki/Common/Body */
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

const ActionButton = styled.button`
  background: var(--Text-Navy, #10265f);
  color: #ffffff;
  border: none;
  border-radius: 200px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 2rem;
  margin: 2rem auto 0;

  &:hover {
    background-color: #28458f;
  }
  &:active {
    background-color: #0a183b;
  }

  @media (max-width: 768px) {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
  }
`;

const PaymentPruductTitle = styled.h2`
  color: var(--Text-Navy, #10265f);
  text-align: center;
  /* Wiki/Desktop/H1 */
  font-family: Poppins;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
`;

const StyledTextField2 = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
  width: 100%;
  border-radius: 60px;
  border: 2px solid var(--Secondary-Blossom, #af8cc0);
  padding: 20px 30px;
  font-family: Poppins;
  input {
    border: none;
    outline: none;
    background-color: none;
    width: 100%;
    &:hover {
      outline: none;
      border: none;
      background-color: none;
    }
    &:focus {
      outline: none;
      border: none;
      background-color: none;
    }
  }
  button {
    outline: none;
    border: none;
    background-color: rgb(0, 0, 0, 0);
    color: var(--Terciario, #88cfec);

    /* Wiki/Common/BodyBold */
    font-family: Poppins;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    &:hover {
      outline: none;
      border: none;
      background-color: none;
      cursor: pointer;
    }
    &:focus {
      outline: none;
      border: none;
      background-color: none;
      color: #5959a3;
    }
  }
`;

const StyledSection = styled.section`
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  align-items: start;
  justify-content: start;
  div {
    display: flex;
    gap: 4px;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    p {
      color: var(--Gris-muy-obscuro, #6d6d6d);

      /* Wiki/Common/Body */
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .total {
      color: var(--Gris-Obscuro, #333);

      /* Wiki/Common/Title */
      font-family: Poppins;
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;

      span {
        color: #333;
        text-align: right;

        /* Wiki/Common/Small body Bold */
        font-family: Poppins;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
    }

    span {
      color: var(--Gris-muy-obscuro, #6d6d6d);

      /* Wiki/Common/Body */
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: start;
    }
  }
`;
