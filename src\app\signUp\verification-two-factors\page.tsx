"use client";
import type { NextPage } from "next";
import { <PERSON>, Button } from "@mui/material";
import Image from "next/image";
import styles from "./verification-two-factors.module.css";
import WhatsAppImage from "../../../../public/web/img/verification/whatsApp.svg";
import SMSImage from "../../../../public/web/img/verification/message.svg";
import EmailImage from "../../../../public/web/img/verification/email.svg";
import { useRouter } from "next/navigation";
import { useOtpCode } from "@/store/signUp/otpCode.store";
import { useSendOtpCode } from "@/hooks/signUp/useSendOtpCode";
import { OtpCodeEnum } from "@/enums/signUp/otp-code.enum";
import VerificationLoader from "./components/loader/VerificationLoader";
import { useEffect } from "react";

const ModalVerification: NextPage = () => {
  const router = useRouter();
  const { name, phone, email, nickname, setOtpCode } = useOtpCode((state) => state);

  const { otpCode, loading, setLoading } = useSendOtpCode();

  const handleSendOtp = async (bySend: string) => {
    setLoading(true);
    const otpSentByMessage =
      bySend === "sms" || bySend === "whatsapp" ? phone : "";
    const otpSentByEmail = bySend === "email" ? email : "";

    setOtpCode({
      sendBy: {
        whatsapp: bySend === "whatsapp",
        sms: bySend === "sms",
        email: bySend === "email",
      },
    });
    const response = await otpCode({
      phone: otpSentByMessage,
      email: otpSentByEmail,
      acction: OtpCodeEnum.REGISTER,
    });
    if (response) {
      setLoading(false);
      router.push("/signUp/otp-code");
    }
    return;
  };

  useEffect(() => {
    setLoading(false);
    //eslint-disable-next-line
  }, []);

  if (loading) {
    return <VerificationLoader />;
  }
  return (
    <section className={styles.sectionVerification}>
      <Box className={styles.textParent}>
        <Box className={styles.text}>
          <Box className={styles.holaSoyTikiSolo}>¡{nickname || name} muchas gracias!</Box>
          <Box className={styles.holaSoyTikisolo}>
            Te voy a mandar un código de verificación para saber que eres tú.
          </Box>
        </Box>
        <Box className={styles.datos}>
          <Box className={styles.holaSoyTikiSolo}>
            ¿Por dónde deseas recibirlo?
          </Box>
          <Box
            sx={{
              display: "flex",
              width: "100%",
              justifyContent: "center",
              gap: "33px",
              alignItems: "center",
              flexDirection: {
                xs: "column",
                sm: "column",
                md: "row",
              },
            }}
          >
            <Button
              className={styles.button}
              startIcon={
                <Image
                  className={styles.icons}
                  width={21}
                  height={21}
                  alt=""
                  src={WhatsAppImage}
                />
              }
            >
              Whatsapp
            </Button>
            <Button
              className={styles.button1}
              startIcon={
                <Image
                  className={styles.icons}
                  width={21}
                  height={21}
                  alt=""
                  src={SMSImage}
                />
              }
              onClick={() => handleSendOtp("sms")}
            >
              SMS
            </Button>
            <Button
              className={styles.button2}
              onClick={() => handleSendOtp("email")}
              startIcon={
                <Image
                  width={21}
                  height={21}
                  alt=""
                  src={EmailImage}
                  className={styles.icons}
                />
              }
            >
              Email
            </Button>
          </Box>
        </Box>
      </Box>
    </section>
  );
};

export default ModalVerification;
