import { Modal } from "@mui/material";
import { FunctionComponent } from "react";
import { StyledModal } from "../GeneralComponents.styles";

type ModalTemplateProps = {
  onClose?: () => void;
  children?: React.ReactNode;
  open?: boolean;
};

const ModalTemplate: FunctionComponent<ModalTemplateProps> = ({
  children,

  onClose,
  open = true,
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <StyledModal>{children}</StyledModal>
    </Modal>
  );
};

export default ModalTemplate;
