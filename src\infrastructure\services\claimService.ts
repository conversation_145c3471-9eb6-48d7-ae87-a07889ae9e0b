import axios from '@/core/axios';
import { Claim } from '@/hooks/useClaim';

export class ClaimService {
  async getDocuments() {
    const res = await axios.get('v1/claims/documents/required');

    return res.data as Claim[];
  }

  async remainingCoverage(id: string): Promise<{idPoliza: number, montoAsegurado: number}> {
    const res = await axios.post(`v1/claims/remaining-coverage`, { idPoliza: id });

    return res.data;
  }
}