import { AuthService } from "@/infrastructure/services/authService";
import { SendOtpCodeDto } from "@/infrastructure/services/dtos/register-dto";
import { useState } from "react";

export const useSendOtpCode = () => {
  const authService = new AuthService();
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  const otpCode = async (params: Partial<SendOtpCodeDto>) => {
    console.log("paramssss", params);
    try {
      const response = await authService.sendOtpCode(params);
      console.log("responseee otp", response);
      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.log("responseee otp Failed to send otp code", error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  const verifyOtpCode = async (
    params: Partial<SendOtpCodeDto>,
    otpCode: string
  ) => {
    try {
      const response = await authService.verifyOtpCode(params, otpCode);
      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.error("Failed to verify otp code", error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return { otpCode, error, loading, verifyOtpCode, setLoading };
};
