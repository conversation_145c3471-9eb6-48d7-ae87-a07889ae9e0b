/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import { PoliciesServices } from '@/infrastructure/services/policiesServices';
import { useSnackbar } from '@/context/SnackbarContext';

const policiesServices = new PoliciesServices();

// Hook para obtener las pólizas
export const usePolicies = () => {
  const [policies, setPolicies] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchPolicies = async () => {
    try {
      setLoading(true);
      const result = await policiesServices.getPolicies();
      setPolicies(result);
      setError(null);
    } catch (err) {
      if (err instanceof Error) {
        setError(err);
        console.error('Error fetching policies:', err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPolicies();
  }, []);

  return { policies, loading, error };
};

// Hook para descargar el PDF de una póliza
export const useDownloadPolicyPDF = () => {
  const [loadingPDF, setLoadingPDF] = useState(false);
  const { showSuccess, showError, showLoading, hide } = useSnackbar();

  const downloadPolicyPDF = async (policyId: string) => {
    try {
      setLoadingPDF(true);
      showLoading('Generando PDF...');

      const response = await policiesServices.getPolicyPDFById(policyId);

      if (!response?.file) {
        throw new Error('No se recibió archivo PDF');
      }

      // Abrir el PDF en una nueva pestaña para visualizar
      window.open(response.file, '_blank');

      // También permitir la descarga automática
      const link = document.createElement('a');
      link.href = response.file;
      link.download = `poliza-${policyId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      hide(); // oculta loading
      showSuccess('PDF descargado correctamente.');
    } catch (error) {
      hide(); // oculta loading
      if (error instanceof Error) {
        console.error('Error al descargar el PDF:', error.message);
        showError('Ocurrió un error al descargar el PDF.');
      }
    } finally {
      setLoadingPDF(false);
    }
  };

  return {
    downloadPolicyPDF,
    loadingPDF,
  };
};
