'use client';
import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Link from 'next/link';
import loginImage from '/public/web/img/mainAssets/LoginImage.png';
import useAuth from '@/hooks/useAuth';
import { useIsLogged } from '@/app/LoginProvider';
import { useLoginModalContext } from '@/app/LoginProvider';
import {
  Overlay,
  CustomModal,
  Container,
  ModalContent,
  ImageWrapper,
  StyledImage,
  FormWrapper,
  Input,
  Button,
  Title,
  ErrorText,
  PasswordReset,
  Disclaimer,
  Footer,
  WhiteLine,
  FooterText,
  FooterButton,
} from './Login.styles';
import PasswordRecovery from '../PasswordRecovery/PasswordRecovery';
import PrivacyModal from '@/app/components/modals/LegalDocuments/PrivacyModal';
import TermsModal from '@/app/components/modals/LegalDocuments/TermsModal';
import UnverifiedAccountModal from '../UnverifiedAccountModal';
import { UnverifiedAccountException } from '@/infrastructure/services/authService';
import { Eye, EyeClosed, X } from 'lucide-react';
import { IconButton } from '@mui/material';
import { useSnackbar } from '@/context/SnackbarContext';

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { login, user, error } = useAuth();
  const { setLogged, setUser } = useIsLogged();
  const [openPasswordRecovery, setOpenPasswordRecovery] = useState(false);
  const { isModalOpen, closeModal, nextAction } = useLoginModalContext();
  const [showPrivacy, setShowPrivacy] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [viewPassword, setViewPassword] = useState(false);
  const [showUnverifiedModal, setShowUnverifiedModal] = useState(false);
  const [unverifiedUserInfo, setUnverifiedUserInfo] = useState<UnverifiedAccountException | null>(null);

  // Notificación global
  const { showSuccess } = useSnackbar();

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: Yup.object({
      email: Yup.string().email('Debe ser un email válido').required('Ingresa tu email'),
      password: Yup.string().required('Ingresa tu contraseña'),
    }),
    onSubmit: async (values) => {
      setLoading(true);
      try {
        const response = await login({
          email: values.email,
          password: values.password,
        });
        if (response) {
          showSuccess('¡Sesión iniciada correctamente!');
          setLogged(true);
          localStorage.setItem('user', JSON.stringify(response.user));
          setUser(response.user);
          nextAction?.();
          closeModal();
        }
      } catch (err) {
        if (err instanceof UnverifiedAccountException) {
          console.log('Account not verified, showing verification modal');
          setUnverifiedUserInfo(err);
          setShowUnverifiedModal(true);
        } else {
          console.error('Login error:', err);
        }
        setLogged(false);
      } finally {
        setLoading(false);
      }
    },
  });

  // Ya no se permite cerrar haciendo click fuera del modal
  const handleOverlayClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (!isModalOpen) return null;

  if (openPasswordRecovery) {
    return (
      <PasswordRecovery
        handleClose={() => {
          setOpenPasswordRecovery(false);
          closeModal();
        }}
        open={openPasswordRecovery}
      />
    );
  }

  if (showPrivacy) {
    return <PrivacyModal isOpen={showPrivacy} onClose={() => setShowPrivacy(false)} />;
  }

  if (showTerms) {
    return <TermsModal isOpen={showTerms} onClose={() => setShowTerms(false)} />;
  }

  if (showUnverifiedModal && unverifiedUserInfo) {
    return (
      <UnverifiedAccountModal
        isOpen={showUnverifiedModal}
        onClose={() => {
          setShowUnverifiedModal(false);
          setUnverifiedUserInfo(null);
          closeModal();
        }}
        userInfo={unverifiedUserInfo.userInfo}
        uuid={unverifiedUserInfo.uuid}
      />
    );
  }
  return (
    <>
      {isModalOpen && (
        <Overlay onClick={handleOverlayClick}>
          <CustomModal>
            <Container style={{ position: 'relative' }}>
              <button
                onClick={() => {
                  formik.resetForm();
                  closeModal();
                }}
                style={{
                  position: 'absolute',
                  top: 20,
                  right: 20,
                  background: '#fff',
                  border: '1.5px solid #e0e0e0',
                  borderRadius: '50%',
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.07)',
                  color: '#5959a3',
                  cursor: 'pointer',
                  zIndex: 2,
                  transition: 'box-shadow 0.2s',
                }}
                aria-label="Cerrar"
                onMouseOver={(e) => (e.currentTarget.style.boxShadow = '0 4px 16px rgba(0,0,0,0.12)')}
                onMouseOut={(e) => (e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.07)')}
              >
                <X size={22} />
              </button>
              <ModalContent>
                <FormWrapper onSubmit={formik.handleSubmit}>
                  <Title>Iniciar Sesión</Title>
                  <Input
                    name="email"
                    type="email"
                    placeholder="Dirección de email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    style={{ width: 'calc(100% - 32px)' }}
                  />
                  {formik.touched.email && formik.errors.email && <ErrorText>{formik.errors.email}</ErrorText>}
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Input
                      type={viewPassword ? 'text' : 'password'}
                      name="password"
                      placeholder="Contraseña"
                      value={formik.values.password}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                    <IconButton
                      onClick={() => {
                        setViewPassword(!viewPassword);
                      }}
                    >
                      {viewPassword ? <Eye /> : <EyeClosed />}
                    </IconButton>
                  </div>
                  {formik.touched.password && formik.errors.password && <ErrorText>{formik.errors.password}</ErrorText>}{' '}
                  {error && !user && <ErrorText>Email o contraseña incorrectos</ErrorText>}
                  <Button type="submit" disabled={!formik.isValid || !formik.dirty}>
                    {loading ? 'Cargando...' : 'Ingresar'}
                  </Button>
                  <PasswordReset onClick={() => setOpenPasswordRecovery(true)}>Recuperar contraseña</PasswordReset>
                  <Disclaimer>
                    Al continuar, aceptas las{' '}
                    <button type="button" onClick={() => setShowTerms(true)}>
                      Condiciones de uso
                    </button>{' '}
                    y el{' '}
                    <button type="button" onClick={() => setShowPrivacy(true)}>
                      aviso de privacidad
                    </button>{' '}
                    de Wiki Protección.
                  </Disclaimer>
                </FormWrapper>
                <ImageWrapper>
                  <StyledImage src={loginImage} alt="Login Image" width={200} height={200} />
                </ImageWrapper>
              </ModalContent>
              <Footer>
                <WhiteLine />
                <FooterText>¿Eres nuevo en Wiki Seguros?</FooterText>
                <Link href="/signUp" passHref legacyBehavior>
                  <a>
                    <FooterButton onClick={closeModal}>Crear tu cuenta Wiki</FooterButton>
                  </a>
                </Link>
              </Footer>
            </Container>
          </CustomModal>
        </Overlay>
      )}
    </>
  );
};

export default Login;
