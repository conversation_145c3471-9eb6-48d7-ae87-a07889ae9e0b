import React from 'react';
import * as S from './IconWrapper.styles';

interface IconWrapperProps {
  icon: React.ReactNode;
  isDisabled?: boolean;
  className?: string;
  onClick?: () => void;
  title?: string;
  noBackground?: boolean;
}

const IconWrapper: React.FC<IconWrapperProps> = ({
  icon,
  isDisabled = false,
  className,
  onClick,
  title,
  noBackground = false,
}) => {
  return (
    <S.IconContainer
      $isDisabled={isDisabled}
      $noBackground={noBackground}
      className={className}
      onClick={!isDisabled ? onClick : undefined}
      role={onClick ? "button" : undefined}
      tabIndex={onClick && !isDisabled ? 0 : undefined}
      title={title}
      aria-disabled={isDisabled}
    >
      <S.SVGWrapper $isDisabled={isDisabled}>
        {icon}
      </S.SVGWrapper>
    </S.IconContainer>
  );
};

export default IconWrapper;
