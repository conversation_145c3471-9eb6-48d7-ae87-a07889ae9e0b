import styled from "styled-components";
import { <PERSON><PERSON>, <PERSON> } from "@mui/material";

// Contenedor principal
export const Container = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
  padding: 20px;
`;

// Título principal
export const Title = styled.h1`
  margin-bottom: 20px;
`;

// Contenedor para el saludo
export const Greeting = styled.div`
  margin-bottom: 30px;
`;

// Mensaje de bienvenida
export const WelcomeMessage = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
`;

// Submensaje
export const SubMessage = styled.p`
  color: var(--Text-Black, #000);
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

// Formulario
export const Form = styled.form`
  width: 100%;
  max-width: 846px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

// Estilo base para los inputs
export const InputStyles = styled.input`
  width: 100%;
  padding: 10px 15px;
  border-radius: 60px;
  border: 2px solid var(--Primary-Navy, #10265f);
  height: 57px;
  font-size: 16px;
  box-sizing: border-box;
  outline: none;
  margin-top: 0.8rem;
  display: flex;
  flex-direction: column;

  &::placeholder {
    color: #afafaf;
  }

  &:focus {
    border-color: #684e79;
    outline: none;
  }
`;

export const SelectStyles = styled(Select)`
  width: 100%;
  padding: 10px 15px;
  border-radius: 60px;
  border: 2px solid var(--Primary-Navy, #10265f);
  height: 57px;
  font-size: 16px;
  box-sizing: border-box;
  outline: none;
  margin-top: 0.8rem;
  display: flex;
  flex-direction: column;
  justify-content: center;

  & .MuiSelect-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  & .MuiSelect-select .placeholder {
    color: #afafaf;
  }

  &:focus-within {
    border-color: #684e79;
  }
`;

// Estilo base para los inputs
export const PasswordInput = styled.div`
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 0.5rem;
  margin-top: 0.8rem;
  align-items: center;

  input {
    width: 100%;
    padding: 10px 15px;
    border-radius: 60px;
    border: 2px solid var(--Primary-Navy, #10265f);
    height: 57px;
    font-size: 16px;
    box-sizing: border-box;
    outline: none;
    display: flex;
    flex-direction: column;
    &::placeholder {
      color: #afafaf;
    }
    &:focus {
      border-color: #684e79;
      outline: none;
    }
  }
`;

// Input para teléfono (extiende InputStyles)
export const InputPhoneStyles = styled(InputStyles)`
  position: relative;
  padding-left: 40px;
  background-image: url("/web/img/phone.svg");
  background-size: 24px 24px;
  background-repeat: no-repeat;
  background-position: 10px center;
`;

// Input para fecha
export const InputDate = styled.input`
  padding: 10px 15px;
  width: 100%;
  border-radius: 60px;
  border: 2px solid var(--Primary-Navy, #10265f);
  height: 57px;
  font-size: 16px;
  box-sizing: border-box;
  outline: none;
  color: #696868;
`;

// Contenedor para el datepicker
export const DatePicker = styled.div`
  margin-top: 1rem;
`;

// Etiqueta para los inputs
export const LabelInput = styled.label`
  color: var(--Primary-Violeta-azulado, #5959a3);
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: left;
  display: flex;
  margin-top: 1rem;
  width: 100%;
`;

// Etiqueta interna para aclarar
export const LabelInputSpan = styled.span`
  font-size: 14px;
  font-weight: 400;
`;

// Contenedor para organizar inputs en grid
export const InputsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  width: 100%;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

// Etiqueta pequeña
export const SmallLabel = styled.p`
  font-family: Poppins;
  font-size: 14px;
  color: #afafaf;
  text-align: left;
  width: 100%;
  display: flex;
`;

// Botón de envío
export const SubmitButton = styled(Button)`
  background-color: #10265f;
  border-radius: 200px;
  color: #f0f0f0;
  font-family: Poppins;
  font-weight: 700;
  font-size: 16px;
  margin-top: 20px;
  min-width: 188px;
  height: 56px;
  width: 188px;
  padding: 16px 24px;
  text-transform: none;
  gap: 8px;

  &:disabled {
    background-color: #f0f0f0;
    border-radius: 200px;
    color: #828282;
  }

  &:hover {
    background-color: #cacaca;
    color: #696969;
  }
`;

// Texto de información pequeño
export const SmallTextInfo = styled.p`
  font-family: Poppins;
  font-weight: 400;
  font-size: 14px;
  color: #afafaf;
  text-align: center;
  line-height: 17.57px;
  margin-top: 1rem;
`;

// Contenedor para los checkboxes
export const CheckboxContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 1rem;
`;

// Ícono para el teléfono
export const PhoneIcon = styled.img`
  width: 24px;
  height: 24px;
`;

// Clase utilitaria para forzar ancho completo en el datepicker
export const FullWidthDatepicker = styled.div`
  width: 100% !important;
`;
