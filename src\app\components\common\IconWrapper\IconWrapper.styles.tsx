import styled from "styled-components";

interface IconWrapperProps {
  $isDisabled?: boolean;
  $noBackground?: boolean;
}

export const IconContainer = styled.div<IconWrapperProps>`
  display: flex;
  width: 40px;
  height: 40px;
  padding: 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px;
  background: #af8cc0;
  transition: all 0.2s ease-in-out;
  cursor: ${({ $isDisabled }) => ($isDisabled ? "default" : "pointer")};
  flex-shrink: 0;

  ${({ $isDisabled }) =>
    $isDisabled
      ? `
    background: #F1DEFA;
    color: #6D6D6D;
  `
      : `
    color: white;
  `}

  ${({ $noBackground }) =>
    $noBackground
      ? `
    background: transparent;
    padding: 0;
    color: #10265F;
  `
      : ``}

  &:focus-visible {
    outline: 2px solid
      ${({ $isDisabled }) => ($isDisabled ? "#6D6D6D" : "#AF8CC0")};
    outline-offset: 2px;
  }
`;

export const SVGWrapper = styled.div<IconWrapperProps>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;

  svg {
    width: 100%;
    height: 100%;

    path,
    rect,
    circle {
      stroke: currentColor;
      transition: stroke 0.2s ease-in-out;
    }
  }
`;
