"use client";
import React, { useEffect } from "react";
import {
  Overlay,
  Modal,
  CloseButton,
  Title,
  SectionTitle,
  Content,
  FooterButton,
} from "./LegalModal.styles";
import { useLegalDocuments } from "@/hooks/useLegalDocuments";
import { EmptyState } from "@/app/components/common";

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

interface TermsSection {
  id: string | number;
  tituloDeSeccion?: string;
  contenidoDeSeccion: string;
}

const TermsModal: React.FC<Props> = ({ isOpen, onClose }) => {
  const { termsSections = [], loading } = useLegalDocuments(isOpen) as {
    termsSections: TermsSection[];
    loading: boolean;
  };

  // Bloquear scroll del fondo mientras el modal esté abierto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <Overlay>
      <Modal>
        <CloseButton onClick={onClose}>×</CloseButton>
        <Title>Términos y Condiciones de Uso de Wiki</Title>

        {loading ? (
          <p>Cargando...</p>
        ) : (
          <>
            {termsSections.length === 0 ? (
              <EmptyState message="No hay información disponible en este momento." />
            ) : (
              termsSections.map((section) => (
                <div key={`terms-${section.id}`}>
                  {section.tituloDeSeccion && (
                    <SectionTitle>{section.tituloDeSeccion}</SectionTitle>
                  )}
                  <Content>
                    {section.contenidoDeSeccion
                      .split("\n\n")
                      .map((paragraph, index) => (
                        <p
                          key={index}
                          dangerouslySetInnerHTML={{
                            __html: paragraph
                              .replace(/\n/g, "<br/>")
                              .replace(/•/g, "•&nbsp;"),
                          }}
                        />
                      ))}
                  </Content>
                </div>
              ))
            )}
          </>
        )}
        <FooterButton onClick={onClose}>De acuerdo</FooterButton>
      </Modal>
    </Overlay>
  );
};

export default TermsModal;
