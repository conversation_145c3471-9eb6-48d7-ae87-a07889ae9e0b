"use client";
import Hero from "@/app/product/Components/Hero";
import PlanCard from "@/app/product/Components/PlanCard";
import { FunctionComponent, useEffect } from "react";
import { Product } from "./products.types";
import { useScrollAnimation } from "@/hooks/scrollanimation";
import gsap from "gsap";
import { resetPurchaseStore, usePurchaseStore } from "@/hooks/usePurchaseStore";
// import Coverage from "./Components/Coverage";

interface ProductPageProps {
  product: Product;
}

const ProductPage: FunctionComponent<ProductPageProps> = ({ product }) => {
  useScrollAnimation(".scroll-animate", {
    start: "top 75%", // Empieza cuando el elemento está al 75% del viewport
    toggleActions: "play none none reverse", // Reversa la animación al salir del viewport
    animationProps: {
      from: { opacity: 0, y: 100 }, // Cambia posición inicial
      to: { opacity: 1, y: 0 }, // Cambia posición final
    },
    once: true, // La animación se dispara solo una vez
  });

  // const [coverageView, setCoverageView] = useState<boolean>(false);

  const { currentStep, confirmStep, setCurrentProduct, currentProduct } =
    usePurchaseStore();

  useEffect(() => {
    if (currentProduct === null) {
      setCurrentProduct(product.id);
    }
    if (currentProduct !== null && currentProduct !== product.id) {
      resetPurchaseStore();
      setCurrentProduct(product.id);
    }
  }, [currentProduct]);

  const handleSelection = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
    gsap.to(".card-elements", {
      opacity: 0,
      duration: 0.4,
      x: -300,
      delay: 0.3,

      onComplete: () => {
        confirmStep();
        // selectProduct(product); // Cambia el producto seleccionado
        // setCoverageView(true); // Cambia la vista a cobertura
        window.scrollTo({ top: 0, behavior: "smooth" }); // Lleva al usuario al inicio de la página
      },
    });

    // Muestra los elementos de cobertura con una animación
    gsap.fromTo(
      ".coverage-elements",
      { opacity: 0, x: 100 }, // Estado inicial
      {
        opacity: 1,
        x: 0,
        duration: 0.7,
        delay: 0.4, // Sincronización con la animación previa
      }
    );
  };

  return (
    <div
      className="max-w-7xl mx-auto px-4 py-12"
      style={{ fontFamily: "Poppins" }}
    >
      {currentStep === "productSelection" && <Hero product={product} />}
      <PlanCard product={product} handleSelection={handleSelection} />
      {/* {!coverageView && <Coverage product={product} />} */}
    </div>
  );
};

export default ProductPage;
