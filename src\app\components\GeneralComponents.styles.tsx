import styled from "styled-components";

export const StyledModalLayout = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: #575a6f85;
`;

export const StyledModal = styled.div`
  display: inline-flex;
  padding: 20px;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  gap: 20px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
  color: #fff;
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  min-width: 300px;
`;

export const ModalTitle = styled.h2`
  color: #fff;
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
`;

export const ModalButton = styled.div`
  color: #a8a8a8;
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    color: #e3e3e3;
  }
`;

export const ModalButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  width: 100%;
  align-items: center;
`;

export const OptionContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 0 0 10px;
  border-left: 1px solid #fff;
`;
