"use client";
import TermsContent from "../components/modals/LegalDocuments/TermsContent";
import { useLegalDocuments } from "@/hooks/useLegalDocuments";


interface TermsSection {
  id: string | number;
  tituloDeSeccion?: string;
  contenidoDeSeccion: string;
}

export default function TermsPage() {
  const { termsSections = [], loading } = useLegalDocuments(true) as {
    termsSections: TermsSection[];
    loading: boolean;
  };
  return (
    <main style={{ padding: 24, maxWidth: 900, margin: "0 auto" }}>
      <h1 style={{ fontSize: 32, fontWeight: 700, marginBottom: 24, color: '#10265f' }}>Términos y Condiciones</h1>
      <TermsContent termsSections={termsSections} loading={loading} />
    </main>
  );
}
