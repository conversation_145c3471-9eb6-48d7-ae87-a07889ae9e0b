"use client";
import React, { FunctionComponent, useEffect, useState } from "react";
import styled from "styled-components";
// import CheckCircleIcon from "@mui/icons-material/CheckCircle";
// import { ChevronLeft, ChevronRight } from "@mui/icons-material";
// import { ChevronRight, ChevronLeft } from "lucide-react";
import { Product } from "../products.types";
import gsap from "gsap";
import Confirmation from "./Confirmation";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import { getEquivalentColor } from "@/Functions/getEquivalentColor";
import Image from "next/image";
import { usePurchaseStore } from "@/hooks/usePurchaseStore";
import { useIsLogged, useLoginModalContext } from "@/app/LoginProvider";
import { useRouter, usePathname } from "next/navigation";
import { useProductsDoc } from "@/hooks/useProductsDoc";
// import Tree from "/public/elements/tree.svg";
// import Image from "next/image";

interface PlanCardProps {
  product: Product;
  handleSelection: (id: number | null) => void;
}

const PlanCard: FunctionComponent<PlanCardProps> = ({
  product,
  handleSelection,
}) => {
  const [selectedPlan, setSelectedPlan] = useState<number | null>(null);
  const [confirmation, setconfirmation] = useState<boolean>(false);
  const [showPlans, setShowPlans] = useState<number[]>([0, 1, 2, 3]);
  const { openModal, setNextAction } = useLoginModalContext();
  const { user } = useIsLogged();

  const router = useRouter();
  const pathname = usePathname();

  const { currentStep, selectProduct, selectedProduct } = usePurchaseStore();
  const { downloadDocument } = useProductsDoc();

  const plans = product.CovergaeOptions.map((option) => ({
    precio: option.precio,
    suma: option.suma,
    id: option.id,
  }));

  useEffect(() => {
    setSelectedPlan(plans[0].id);
  }, []);

  const handlePrevious = () => {
    if (!showPlans.includes(0)) {
      setShowPlans((prev) => prev.map((num) => num - 1));
    }
  };

  const handlePlanSelection = (id: number) => {
    setSelectedPlan(id);
  };

  const handleNext = () => {
    if (!showPlans.includes(plans.length - 1)) {
      setShowPlans((prev) => prev.map((num) => num + 1));
    }
  };

  const handleDownloadDocument = async () => {
    await downloadDocument(product.id, product.title || "producto");
  };

  const handleConfirmation = () => {
    const token = localStorage.getItem("accessToken");

    if (!token) {
      openModal();
      setNextAction(() => {
        router.push(pathname);
      });
      localStorage.setItem("purchaseProduct", pathname);
    } else {
      window.scrollTo({ top: 0, behavior: "smooth" });
      // Oculta los elementos de la tarjeta con una animación
      gsap.to(".coverage-elements", {
        opacity: 0,
        duration: 0.4,
        x: -300,
        delay: 0.3,
        onComplete: () => {
          setconfirmation(true); // Actualiza el plan seleccionado
        },
      });
    }
  };

  return (
    <Wrapper>
      {confirmation ? (
        <>
          <Confirmation
            selectedProduct={selectedProduct}
            productName={{
              name: (product.title || "").toLowerCase(),
              productId: product.id,
            }}
          />
        </>
      ) : (
        <>
          {currentStep === "productSelection" && (
            <>
              <Title className="card-elements">
                ¡Estos son los planes que tenemos para ti!
              </Title>
              <Subtitle className="card-elements">
                Elige la suma asegurada que te haga sentir más tranki
              </Subtitle>
              <PlanSelectorMobile>
                <div>
                  {plans // Filtra los índices presentes en showPlans
                    .map((plan) => (
                      <React.Fragment key={plan.id}>
                        {plan.id === selectedPlan ? (
                          <SelectedPlanButton
                            color={getEquivalentColor(product.color)}
                          >
                            ${plan.suma}
                          </SelectedPlanButton>
                        ) : (
                          <PlanButton
                            style={{ backgroundColor: "#fff" }}
                            onClick={() => handlePlanSelection(plan.id)}
                          >
                            ${plan.suma}
                          </PlanButton>
                        )}
                      </React.Fragment>
                    ))}
                </div>
              </PlanSelectorMobile>
              <PlanSelector className="card-elements">
                <ChevronLeft
                  style={{
                    cursor: "pointer",
                    color: "#89c598",
                    width: "41px",
                    height: "41px",
                  }}
                  onClick={handlePrevious}
                />
                <div
                  style={{
                    borderRadius: "90px",
                    display: "flex",
                    gap: "0.5rem",
                    backgroundColor: "#fff",
                  }}
                >
                  {plans
                    .filter((_, index) => showPlans.includes(index)) // Filtra los índices presentes en showPlans
                    .map((plan) => (
                      <React.Fragment key={plan.id}>
                        {plan.id === selectedPlan ? (
                          <SelectedPlanButton
                            color={getEquivalentColor(product.color)}
                          >
                            ${plan.suma}
                          </SelectedPlanButton>
                        ) : (
                          <PlanButton
                            style={{ backgroundColor: "#fff" }}
                            onClick={() => handlePlanSelection(plan.id)}
                          >
                            ${plan.suma}
                          </PlanButton>
                        )}
                      </React.Fragment>
                    ))}
                </div>
                <ChevronRight
                  className="card-elements"
                  onClick={handleNext}
                  style={{
                    cursor: "pointer",
                    color: "#89c598",
                    width: "41px",
                    height: "41px",
                  }}
                />
              </PlanSelector>
            </>
          )}
          {currentStep === "coverageConfirmation" && (
            <>
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                  width: "100%",
                  alignItems: "center",
                  marginBottom: "24px",
                }}
              >
                <Title2 className="scroll-animate coverage-elements">
                  Excelente decisión
                  {user
                    ? `, ${user.nickname ? user.nickname : user.firstName}`
                    : null}
                </Title2>
                <Subtitle2 className="scroll-animate coverage-elements">
                  Así quedaría tu plan, has escogido una suma asegurada de $
                  {selectedProduct?.suma}. Ésta te cubrirá un añote.
                </Subtitle2>
              </div>
            </>
          )}
          {selectedProduct ? (
            <>
              <Card className="coverage-elements">
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "10px",
                    }}
                  >
                    <LogoMobile
                      src="https://edge.sitecorecloud.io/zurichinsurf8c0-zwpshared-prod-d824/media/project/zurich-headless/shared/corporate/zurich-logo-blue.svg?iar=0"
                      alt="zurich"
                    />
                    <Price>
                      <p>Sólo vas a pagar</p> ${selectedProduct.precio}
                      <span>MXN</span>
                    </Price>
                    <Disclaimer>
                      Disponible con distintas opciones de pago
                    </Disclaimer>
                  </div>
                  <Logo>
                    <span>Respaldado por</span>

                    <Image
                      src="https://edge.sitecorecloud.io/zurichinsurf8c0-zwpshared-prod-d824/media/project/zurich-headless/shared/corporate/zurich-logo-blue.svg?iar=0"
                      alt="Zurich Insurance Group Logo"
                      width={215}
                      height={55.17}
                    />
                  </Logo>
                </div>

                <TableContainer>
                  <Row>
                    <span>Coberturas</span>{" "}
                    <span className="doNotShowonMobile">
                      Tu protección, en
                      <br /> números claros:
                    </span>
                  </Row>
                  {selectedProduct.features.map((item, index) => (
                    <Row key={index}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "10px",
                        }}
                      >
                        {item.icon && (
                          <Image
                            src={item.icon}
                            alt={item.sumaAsegurada}
                            width={30}
                            height={30}
                            color="#000"
                            style={{ filter: "brightness(0)" }}
                          />
                        )}
                        <Concepto>{item.concepto}</Concepto>
                      </div>
                      <SumaAsegurada>{item.sumaAsegurada}</SumaAsegurada>
                    </Row>
                  ))}
                  <Row>
                    Deducible:{" "}
                    <span>
                      {(selectedPlan !== null &&
                        Number(selectedProduct.deducible)) ||
                        0}
                      %
                    </span>
                  </Row>
                </TableContainer>

                <ActionButton
                  style={{ backgroundColor: " #10265F" }}
                  onClick={handleConfirmation}
                >
                  ¡Quiero este!
                </ActionButton>
              </Card>
              <DownloadLinkContainer>
                <DownloadLink onClick={handleDownloadDocument}>
                  Descarga los detalles de la póliza
                </DownloadLink>
              </DownloadLinkContainer>
            </>
          ) : (
            <>
              <>
                {selectedPlan !== null && !selectedProduct && (
                  <>
                    <Card className="coverage-elements">
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "10px",
                          }}
                        >
                          <LogoMobile
                            src="https://edge.sitecorecloud.io/zurichinsurf8c0-zwpshared-prod-d824/media/project/zurich-headless/shared/corporate/zurich-logo-blue.svg?iar=0"
                            alt="zurich"
                          />
                          <Price>
                            <p>Sólo vas a pagar</p> $
                            {selectedPlan !== null &&
                              product.CovergaeOptions?.filter(
                                (plan) => plan.id === selectedPlan
                              )[0].precio}
                            <span>MXN</span>
                          </Price>
                          <Disclaimer>
                            Disponible con distintas opciones de pago
                          </Disclaimer>
                        </div>
                        <Logo>
                          <span>Respaldado por</span>

                          <Image
                            src="https://edge.sitecorecloud.io/zurichinsurf8c0-zwpshared-prod-d824/media/project/zurich-headless/shared/corporate/zurich-logo-blue.svg?iar=0"
                            alt="Zurich Insurance Group Logo"
                            width={215}
                            height={55.17}
                          />
                        </Logo>
                      </div>

                      <TableContainer>
                        <Row>
                          <span>Coberturas</span>{" "}
                          <span className="doNotShowonMobile">
                            Tu protección, en
                            <br /> números claros:
                          </span>
                        </Row>
                        {selectedPlan !== null &&
                          selectedPlan !== null &&
                          product.CovergaeOptions?.filter(
                            (plan) => plan.id === selectedPlan
                          )[0].features.map((item, index) => (
                            <Row key={index}>
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "10px",
                                }}
                              >
                                {item.icon && (
                                  <Image
                                    src={item.icon}
                                    alt={item.sumaAsegurada}
                                    width={30}
                                    height={30}
                                    color="#000"
                                    style={{ filter: "brightness(0)" }}
                                  />
                                )}
                                <Concepto>{item.concepto}</Concepto>
                              </div>
                              <SumaAsegurada>
                                {item.sumaAsegurada}
                              </SumaAsegurada>
                            </Row>
                          ))}
                        <Row>
                          Deducible:{" "}
                          <span>
                            {selectedPlan !== null &&
                              Math.floor(
                                Number(
                                  product.CovergaeOptions.find(
                                    (plan) => plan.id === selectedPlan
                                  )?.deducible
                                ) || 0
                              )}
                            %
                          </span>
                        </Row>
                      </TableContainer>

                      <ActionButton
                        style={{ backgroundColor: "#10265F" }}
                        onClick={() => {
                          handleSelection(selectedPlan);
                          selectProduct(
                            product.CovergaeOptions?.filter(
                              (plan) => plan.id === selectedPlan
                            )[0]
                          );
                        }}
                      >
                        ¡Quiero este!
                      </ActionButton>
                    </Card>
                    <DownloadLinkContainer>
                      <DownloadLink onClick={handleDownloadDocument}>
                        Descarga los detalles de la póliza
                      </DownloadLink>
                    </DownloadLinkContainer>
                  </>
                )}
              </>
            </>
          )}
        </>
      )}
      {/* <StyledImage1 src={DoublePalm} alt="Double Palm" /> */}
      {/* <StyledImage1 src={Tree} alt="Tree" className="confirmation-elements" />
      <StyledImage2 src={Tree} alt="Tree" className="confirmation-elements" /> */}
    </Wrapper>
  );
};

export default PlanCard;

const Wrapper = styled.div`
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
`;

const Title = styled.h1`
  color: var(--Text-Navy, #10265f);
  text-align: center;
  font-family: Poppins;
  font-size: 48px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  @media (max-width: 768px) {
    font-size: 22px;
    font-weight: 400;
  }
`;

const Title2 = styled.h1`
  color: var(--Text-Navy, #10265f);
  text-align: center;

  /* Wiki/Common/Title */
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  @media (max-width: 768px) {
    font-size: 18px;
    font-weight: 600;
  }
`;

const Subtitle = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 34px;
  font-style: normal;
  font-weight: 400;
  line-height: 36px; /* 105.882% */
  padding-bottom: 2rem;
`;

const Subtitle2 = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  /* Wiki/Common/Body */
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  max-width: 500px;
`;

const PlanSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 2rem 0;

  @media (max-width: 768px) {
    display: none;
  }
`;

const PlanSelectorMobile = styled.div`
  display: none;
  width: 100vw;
  z-index: 3;

  div {
    width: fit-content;
    max-width: 100%;
    margin: 2rem auto;
    overflow: scroll;
    align-items: center;
    gap: 0.5rem;
    display: flex;
    flex-direction: row;
    padding: 0 2rem;
    ::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;
  }

  @media (max-width: 768px) {
    display: block;
  }
`;

const PlanButton = styled.button`
  background-color: #f1f1f1;
  color: #6d6d6d;
  border: none;
  border-radius: 90px;
  padding: 28px 38px;
  cursor: pointer;
  font-size: 22px;
  transition: background-color 0.3s ease;
  font-family: Poppins;
  font-weight: 600;

  &:hover {
    background-color: #e0e0e0;
  }

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
`;

const SelectedPlanButton = styled(PlanButton)<{ color?: string }>`
  background-color: ${(props) => props.color};
  color: #ffffff;

  &:hover {
    background-color: ${(props) => props.color};
  }
`;

const Card = styled.div`
  background: #ffffff;
  box-shadow: 0px 4px 24px 0px var(--Gris-Obscuro, #333);
  width: 100%;
  max-width: 1200px;
  padding: 2rem;
  text-align: left;
  border-radius: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 2;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const DownloadLinkContainer = styled.div`
  display: flex;
  width: 100%;
  max-width: 1200px;
  flex-direction: column;
  align-items: flex-end;
`;

const DownloadLink = styled.button`
  color: #a483df;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: underline;
  cursor: pointer;
  margin: 2rem 0 0 0;
  background: none;
  border: none;
  padding: 0;

  &:hover {
    color: #8854c6;
  }

  @media (max-width: 768px) {
    font-size: 14px;
  }
`;

const Price = styled.h2`
  color: var(--Gris-Obscuro, #333);
  font-family: Poppins;
  font-size: 38px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.456px;

  p {
    color: var(--Gris-muy-obscuro, #6d6d6d);
    font-family: Poppins;
    font-size: 26px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.312px;
  }

  span {
    color: var(--Gris-Obscuro, #333);
    text-align: center;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.192px;
  }

  @media (max-width: 768px) {
    font-size: 36px;

    p {
      font-size: 20px;
    }

    span {
      font-size: 14px;
    }
  }
`;

const Disclaimer = styled.p`
  color: #7d7d7d;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  @media (max-width: 768px) {
    font-size: 12px;
  }
`;

const ActionButton = styled.button`
  /* background-color: #a483df; */
  color: #ffffff;
  border: none;
  border-radius: 200px;
  padding: 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 2rem;
  margin: 2rem auto 0;
  width: 188px;

  /* &:hover {
    background-color: #8854c6;
  } */

  @media (max-width: 768px) {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
  }
`;

const Logo = styled.div`
  position: relative;
  width: 215px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;

  img {
    width: 100%;
    height: auto;
  }
  span {
    color: var(--Gris-Medio-Claro, #afafaf);
    text-align: right;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const LogoMobile = styled.img`
  display: none;
  max-width: 80px;
  width: 100%;
  height: auto;

  @media (max-width: 768px) {
    display: block;
  }
`;

const TableContainer = styled.div`
  margin: 20px;
  border-radius: 8px;
  overflow: hidden;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  @media (max-width: 768px) {
    font-size: 14px;
  }
`;

// Estilos para cada fila
const Row = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #ddd;
  gap: 20px;
  color: var(--Gris-Obscuro, #333);
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  &:last-child {
    border-bottom: none;
  }

  .doNotShowonMobile {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
    .doNotShowonMobile {
      display: none;
    }
  }
`;

// Estilos para el texto de los conceptos
const Concepto = styled.div`
  color: var(--Gris-Obscuro, #333);
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

// Estilos para la suma asegurada
const SumaAsegurada = styled.div`
  color: var(--Gris-muy-obscuro, #6d6d6d);
  text-align: right;
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

// const StyledImage1 = styled(Image)`
//   position: absolute;
//   top: 160px;
//   width: 8vw;
//   height: auto;
//   left: 0;
//   @media (max-width: 1024px) {
//     display: none;
//   }
// `;

// const StyledImage2 = styled(Image)`
//   position: absolute;
//   bottom: -60px;
//   width: 8vw;
//   height: auto;
//   right: 0;
//   transform: rotateY(180deg);
//   @media (max-width: 1024px) {
//     display: none;
//   }
// `;
