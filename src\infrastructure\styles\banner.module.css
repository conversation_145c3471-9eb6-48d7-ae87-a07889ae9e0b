.securityBanner {
  /* background: linear-gradient(to right, #E7F4FA 7%, white 7%, white 62%, #E7F4FA 62%); */
  position: relative;
} 
   
.slideContainer {
  position: relative;
  
  aspect-ratio: 60/25;
  width: 100%;
  max-width: 1200px;

} 
  
.slide {
  position: absolute;
  inset: 0;
  transition: transform 500ms ease-in-out;
  width: 100vw;
}
  
.slideContent {
  height: auto;
width:100%;
max-width: 1200px;
  margin: 0 auto;

}
  
.slideGrid {

  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 24px;
}
  
.slideText {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
  
.slideLink {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #722ED1;
  background: #F9F0FF;
  border-radius: 14.97px;
  padding: 6px 10px;
  font-family: var(--font-poppins);
  font-size: 16.47px;
  font-weight: 400;
  text-decoration: none;
  width: fit-content;
}
  
.slideLink:hover {
  background: #f0e1fa;
}
 
.slideTitle {
  color: #333333;
  font-size: 34px;
  font-weight: 400;
  letter-spacing: -0.025em;
}
  
.slideDescription {
  font-size: 18px;
  font-weight: 400;
  color: #7D7D7D;
}
  
.slideImage {

  width: 100%;
  object-fit: cover;
}
  
.dotContainer {
  position: absolute;
  bottom: 16px;
  left: 50%;
  display: flex;
  transform: translateX(-50%);
  gap: 8px;
}
  
.dot {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  transition: background-color 0.3s;
}
  
.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  padding: 8px;
  border-radius: 50%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
  
.navButton:hover {
  background-color: #F3F4F6;
}
  
.btnCotizarSlider {  
  display: flex;
  color: white;
  width: 100% !important;
  align-content: space-around;
  align-items: center;
  justify-content: center;
  padding: 14px 22px;
  border-radius: 20px;
  background-color: #AF8CC0;
  font-family: var(--font-poppins);
  font-size: 11.98px;
  font-weight: 700;
}
  
.btnCotizarSlider:hover {
  background-color: #5959A3 !important;
  width: 100% !important;
}
  
.imgCotizar {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}
  
.navButtonLeft {
  left: 16px;
}
  
.navButtonRight {
  right: 16px;
}
  

.mobileImg {
  display: flex;
  
}

@media (max-width: 600px) {
 

  .slideContent {
    height: 100%;
    padding: 0 0 0 16px;
  }
  .securityBanner {
    padding: 0px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
  } 
  .btnCotizarSlider{
    font-size: 10px;
    text-transform: none;
    font-weight: 600;
    width: 100% !important;
    padding: 3px 8px;
  }
  .dot {
    height: 10px;
    width: 10px;
  }
  .slideDescription {
    font-size: 13px;
  }
  .slideTitle {
    font-size: 30px;
    font-weight: 400;
    letter-spacing: -0.025em;
  }
  .navButtonLeft {
    display: none;
  }
    
  .navButtonRight {
    display: none;
  }
    
    
  .mobileImg {
    display: none;
  }
  .slideGrid {
    grid-template-columns: repeat(2, 1fr);
  
  }
 
}

.sparkles {
  font-size: 20px;
  color: #8B5CF6;
}