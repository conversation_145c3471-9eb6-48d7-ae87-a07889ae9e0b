import React from 'react';
import { StatusTextContainer } from './StatusText.styles';
import { StatusType } from '@/types';

interface StatusTextProps {
  status: StatusType;
  children?: React.ReactNode;
  className?: string;
}

const StatusText: React.FC<StatusTextProps> = ({ status, children, className }) => {
  return (
    <StatusTextContainer $status={status} className={className}>
      {children || status}
    </StatusTextContainer>
  );
};

export default StatusText;
