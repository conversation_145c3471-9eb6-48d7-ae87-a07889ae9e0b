import styled from "styled-components";

export const Overlay = styled.div`
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow: hidden;
  overscroll-behavior: none;
  touch-action: none;
  -webkit-overflow-scrolling: touch;
  pointer-events: all;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
`;

export const CustomModal = styled.div`
  background: #fff;
  border-radius: 24px;
  padding: 40px;
  max-width: 520px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: none;
  pointer-events: all;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  position: relative;
  z-index: 10000;

  @media (max-width: 768px) {
    padding: 32px 24px;
    margin: 16px;
    border-radius: 20px;
  }
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
`;

export const ModalContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
  position: relative;
`;

export const CloseButton = styled.button`
  position: absolute;
  top: -30px;
  right: -30px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #999;
  transition: color 0.2s ease;
  z-index: 1;

  &:hover {
    color: #666;
  }

  svg {
    width: 24px;
    height: 24px;
  }
`;

export const IconContainer = styled.div`
  font-size: 64px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;

  @media (max-width: 768px) {
    font-size: 48px;
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
  }
`;

export const Title = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 22px;
  }
`;

export const UserGreeting = styled.p`
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin: 0 0 24px 0;
  text-align: center;
  line-height: 1.5;
  max-width: 400px;

  @media (max-width: 768px) {
    font-size: 15px;
    margin: 0 0 20px 0;
  }
`;

export const Subtitle = styled.p`
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 32px 0;
  line-height: 1.4;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 15px;
    margin: 0 0 28px 0;
  }
`;

export const ChannelSelection = styled.div`
  display: flex;
  flex-direction: row;
  gap: 16px;
  width: 100%;
  justify-content: center;
  margin-bottom: 24px;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 12px;
  }
`;

export const ChannelButton = styled.button<{ selected: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  &:focus {
    outline: none;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 479px) {
    width: 100%;
    min-width: auto;
  }

  /* Estilos específicos para SMS */
  &.sms {
    background: #5A9FD4;
    color: #fff;

    &:hover:not(:disabled) {
      background: #4A8BC2;
      box-shadow: 0 6px 20px rgba(90, 159, 212, 0.4);
    }
  }

  /* Estilos específicos para Email */
  &.email {
    background: #9C5AE2;
    color: #fff;

    &:hover:not(:disabled) {
      background: #8A4BD1;
      box-shadow: 0 6px 20px rgba(156, 90, 226, 0.4);
    }
  }
`;

export const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 20px;
    height: 20px;
  }
`;



export const ErrorText = styled.p`
  color: #D32F2F;
  font-size: 14px;
  margin: 8px 0 0 0;
  text-align: center;
  background: #FFEBEE;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #FFCDD2;
  width: 100%;
`;
