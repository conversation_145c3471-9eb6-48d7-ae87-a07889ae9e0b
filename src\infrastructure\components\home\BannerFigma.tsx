"use client";
import BannerImg from "@/../public/web/img/pruebaBanner/prueba4.jpg";
import Image from "next/image";
import styled from "styled-components";

const BannerFigma = () => {
  return (
    <>
      <div
        style={{
          width: "100%",
          height: "100%",
          background: "#BC8DC3",
          display: "flex",
          flexDirection: "row",
        }}
      >
        <div
          style={{
            width: "100%",
            height: "100%",
            background: "#BC8DC3",
            maxWidth: "90%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            margin: "0 0 0 auto",
          }}
        >
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              background: "#fff",
              alignItems: "start",
              justifyContent: "center",
              height: "100%",
              gap: "22px",
              padding: "20px",
            }}
          >
            <StyledH2>
              Los hackers no duermen…
              <br />
              pero nosotros tampoco.
            </StyledH2>
            <StyledP>
              Con nuestro seguro, tienes cobertura 24/7 contra hackeos, robo de
              identidad y más.
            </StyledP>
            <StyledButton>Más información</StyledButton>
          </div>
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
            }}
          >
            <Image
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
              src={BannerImg}
              alt="figma-banner"
              layout="responsive"
              width={500}
              height={500}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default BannerFigma;

const StyledH2 = styled.h2`
  color: #0f265d;
  font-family: Poppins;
  font-size: 34px;
  font-style: normal;
  font-weight: 400;
  line-height: 36px;
`;
const StyledP = styled.p`
  color: #8989a2;
  font-family: var(--Font-Font-family-Font-Family, Poppins);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 122.222% */
`;

const StyledButton = styled.button`
  border-radius: 149.733px;
  background: var(--Secondary-Blossom, #af8cc0);
  display: flex;
  min-width: 140.749px;
  padding: 11.979px 17.968px;
  justify-content: center;
  align-items: center;
  gap: 5.989px;
  border: none;
  outline: none;
  color: #fff;
  cursor: pointer;
  &:hover {
    background: #8c6a9c;
  }
  &:active {
    background: #6e547b;
  }
`;
