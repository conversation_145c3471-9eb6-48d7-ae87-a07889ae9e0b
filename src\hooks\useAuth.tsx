import { useState } from "react";
import { AuthService, UnverifiedAccountException } from "../infrastructure/services/authService";
import {
  LoginDto,
  RegisterDto,
} from "@/infrastructure/services/dtos/register-dto";
import { FormattedData } from "@/infrastructure/models/userData.model";

const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [unverifiedAccountInfo, setUnverifiedAccountInfo] = useState<UnverifiedAccountException | null>(null);
  const authService = new AuthService();

  const login = async (credentials: LoginDto) => {
    try {
      setLoading(true);
      setError(null);
      setUnverifiedAccountInfo(null);
      const userData = await authService.login(credentials);
      setUser(userData);
      return userData;
    } catch (error) {
      if (error instanceof UnverifiedAccountException) {
        console.log("Account not verified", error);
        setUnverifiedAccountInfo(error);
        setError(null);
        setUser(null);
        throw error; // Re-lanzar para que el componente pueda manejarlo
      } else if (error instanceof Error) {
        console.log("Failed to login", error);
        setError(error);
        setUnverifiedAccountInfo(null);
        setUser(null);
      }
    } finally {
      setLoading(false);
    }
  };

  const register = async (credentials: RegisterDto) => {
    try {
      setLoading(true);
      const userData = await authService.register(credentials);
      setUser(userData);
      setError(null);
      return userData;
    } catch (error) {
      setError(error as Error);
      setUser(null);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const userInfo = await authService.getUserInfo();
      //   setUser(userInfo);
      setError(null);
      console.log("User info", userInfo);
      return userInfo;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.error("Failed to retrieve user info", error);
      setError(error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const updateUserInfo = async (profileData: FormattedData) => {
    try {
      setLoading(true);
      const userInfo = await authService.updateProfile(profileData);
      setError(null);
      authService.refreshToken();
      return userInfo;
    } catch {
      if (!(error instanceof Error)) return;
      console.error("Failed to update user info", error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  const getMyProducts = async () => {
    try {
      setLoading(true);
      const products = await authService.getMyProducts();
      setError(null);
      return products;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.error("Failed to retrieve user products", error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  const clearUnverifiedAccountInfo = () => {
    setUnverifiedAccountInfo(null);
  };

  return {
    user,
    loading,
    error,
    unverifiedAccountInfo,
    login,
    register,
    fetchUserInfo,
    updateUserInfo,
    getMyProducts,
    clearUnverifiedAccountInfo,
  };
};

export default useAuth;
