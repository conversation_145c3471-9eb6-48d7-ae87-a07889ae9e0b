import React from "react";
import { SectionTitle, Content } from "./LegalModal.styles";
import { EmptyState } from "@/app/components/common";

interface TermsSection {
  id: string | number;
  tituloDeSeccion?: string;
  contenidoDeSeccion: string;
}

interface TermsContentProps {
  termsSections: TermsSection[];
  loading: boolean;
}

const TermsContent: React.FC<TermsContentProps> = ({ termsSections, loading }) => {
  if (loading) return <p>Cargando...</p>;

  if (!termsSections || termsSections.length === 0) {
    return <EmptyState message="No hay información disponible en este momento." />;
  }

  return (
    <>
      {termsSections.map((section) => (
        <div key={`terms-${section.id}`}>
          {section.tituloDeSeccion && (
            <SectionTitle>{section.tituloDeSeccion}</SectionTitle>
          )}
          <Content>
            {section.contenidoDeSeccion
              .split("\n\n")
              .map((paragraph, index) => (
                <p
                  key={index}
                  dangerouslySetInnerHTML={{
                    __html: paragraph
                      .replace(/\n/g, "<br/>")
                      .replace(/•/g, "•&nbsp;"),
                  }}
                />
              ))}
          </Content>
        </div>
      ))}
    </>
  );
};

export default TermsContent;
