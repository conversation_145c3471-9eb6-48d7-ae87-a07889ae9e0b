import { Switch, styled as muiStyled } from "@mui/material";

const CustomSwitch = muiStyled(Switch)(() => ({
  width: 52,
  height: 32,
  padding: 0,
  display: "flex",
  position: "relative",

  "& .MuiSwitch-switchBase": {
    padding: 6,
    top: "50%",
    left: 6,
    position: "absolute",
    transform: "translateY(-50%)",
    transition: "transform 300ms",

    "&.Mui-checked": {
      transform: "translate(22px, -50%)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        backgroundColor: "#10265F",
        boxShadow: "none"
      },
    },
  },

  "& .<PERSON>iSwitch-thumb": {
    width: 20,
    height: 20,
    borderRadius: "50%",
    backgroundColor: "#fff",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
    zIndex: 2,
  },

  "& .MuiSwitch-track": {
    borderRadius: 32,
    backgroundColor: "#F1F3F5",
    boxShadow: "inset 0 0 0 4px rgba(209, 213, 219, 0.8)",
    opacity: 1,
    transition: "all 300ms",
  },
}));

export default CustomSwitch;
