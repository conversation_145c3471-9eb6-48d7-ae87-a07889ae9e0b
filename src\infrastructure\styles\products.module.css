.contentCards {
  width: 100%;
  max-width: 7xl;
  padding: 4px 12px;
}

.secctionTitleProduct {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  color: var(--Text-Navy, #10265f);
  text-align: center;
  font-family: Poppins;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.secctionSubtitleProduct {
  color: var(--Negro, #000);
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  text-align: center;
}

.cardStyle {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
  width: 100%;
  min-width: 180px;
  max-width: 300px;
  margin: 2px;
  border-radius: 16px;
  border-style: solid;
  border-width: 1px;
  border-color: #86649b;
  box-shadow: 0px 4px 10px rgba(134, 100, 155, 0.8);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  padding: 10%;
}

/* .cardStyle:hover {
     height: auto;
     align-items: center;
     transform: scale(1.08);
     padding: 30px 70px 20px 10px;
     box-shadow: 0px 8px 20px rgba(134, 100, 155, 1);
} */

.cardContent {
  text-align: center;
}

.cardBody {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.cardMedia {
  display: flex;
  min-height: 150px;
  width: auto;
  height: 100%;
  max-height: 200px;
}

.cardDescription {
  display: none;
  flex-direction: column;
  text-align: left;
  font-family: var(--font-poppins);
  font-weight: 400;
  font-size: 16px;
  margin-left: 0.5rem;
  align-items: center;
  color: #5959a3;
}

.cardStyle:hover .cardDescription {
  display: flex;
}

.cardStyle .titleTypographyM {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.cardStyle:hover .titleTypography {
  display: none;
}
.cardStyle:hover .subtitleTypography {
  font-size: 16.94px;
  font-family: var(--font-inter);
}

.btnCotizarProduct {
  margin-top: 1rem;
  font-family: var(--font-poppins);
  font-size: 13.5px;
  font-weight: 700;
  margin-bottom: 1rem;
  height: fit-content;
  width: 100%;
  background-color: #af8cc0;
  color: #ffffff;
  font-weight: bold;
  border-radius: 30px;
}

/* .btnCotizarProduct:hover {
     background-color: #AF8CC0;
     width: 70%;
} */

.titleTypography {
  margin-top: 1rem;
  font-size: 22px;
  font-weight: 700;
  color: #10265e;
  font-family: var(--font-mulish);
}
.titleTypographyM {
  display: flex;
  font-size: 22px;
  font-weight: 700;
  color: #10265e;
  font-family: var(--font-mulish);
}
.subtitleTypography {
  font-size: 14px;
  font-weight: 400;
  color: #273270;
  font-family: var(--font-poppins);
}

@media (max-width: 600px) {
  .contentCards {
    width: 100%;
    padding: 0px 12px 0px 12px;
  }
  .secctionSubtitleProduct {
    font-size: 18px;
  }
  .secctionTitleProduct {
    font-size: 30px;
    font-weight: 500;
  }
  .cardDescription {
    text-align: center;
    margin-left: 0.5rem;
    align-items: center;
  }
  .cardStyle {
    border-radius: 7px;
  }
  .titleTypographyM {
    font-size: 24px;
  }
  .titleTypography {
    font-size: 24px;
  }
  .titleTypographyM:hover {
    color: #ffffff !important;
  }
  .subtitleTypography {
    display: none;
  }
  /* .cardBody {
          display: flex;
          flex-direction: row;
          align-items: center;
     } */
  /* .btnCotizarProduct {
          display: none;
     } */
}
