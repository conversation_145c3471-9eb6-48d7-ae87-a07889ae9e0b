import { Button } from "@mui/material";
import { CheckCircle, X } from "lucide-react";
import { btnStyles } from "./styles";

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  subtitle?: string;
  confirmButtonText?: string;
  isLoading?: boolean;
}

const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "¡Listo! tu reporte ha sido registrado",
  subtitle = "Puedes revisar el estatus del proceso en la sección de reportes",
  confirmButtonText = "Ir a reportes",
  isLoading = false
}: ConfirmationModalProps) => {
  if (!isOpen) return null;

  return (
    <div 
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '10px',
          padding: '2rem',
          maxWidth: '500px',
          width: '90%',
          textAlign: 'center',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '1.5rem',
          position: 'relative',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Botón de cerrar */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '1rem',
            right: '1rem',
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            padding: '0.5rem',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f3f4f6';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <X size={20} color="#6b7280" />
        </button>

        {/* Imagen/Icono */}
        <div
          style={{
            width: '120px',
            height: '120px',
            backgroundColor: '#dcfce7',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: '1rem'
          }}
        >
          <CheckCircle size={60} color="#16a34a" />
        </div>

        {/* Título */}
        <h2
          style={{
            fontSize: '1.3rem',
            fontWeight: 600,
            margin: 0
          }}
        >
          {title}
        </h2>

        {/* Subtítulo */}
        <p
          style={{
            fontSize: '1.1rem',
            color: '#6b7280',
            fontWeight: 500,
            margin: 0,
            lineHeight: '1.5'
          }}
        >
          {subtitle}
        </p>

        {/* Botón de acción */}
        <Button
          variant="contained"
          onClick={onConfirm}
          disabled={isLoading}
          sx={{
            ...btnStyles,
            borderRadius: '25px',
            height: '50px',
            width: '200px',
            fontSize: '1rem',
            fontWeight: 600,
            marginTop: '1rem',
            opacity: isLoading ? 0.7 : 1
          }}
        >
          {isLoading ? 'Enviando...' : confirmButtonText}
        </Button>
      </div>
    </div>
  );
};

export default ConfirmationModal;