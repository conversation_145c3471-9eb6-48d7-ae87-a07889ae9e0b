import { useState, useEffect } from "react";

interface Article {
  cobertura: string;
  articulo: string;
  valor: string;
  documentos?: File[];
}

interface UseArticleFormProps {
  editingArticle?: Article | null;
  isEditing?: boolean;
  open: boolean;
  hasRequiredDocuments: boolean;
}

export const useArticleForm = ({
  editingArticle,
  isEditing,
  open,
  hasRequiredDocuments,
}: UseArticleFormProps) => {
  const [formData, setFormData] = useState<Article>({
    cobertura: "",
    articulo: "",
    valor: "",
    documentos: [],
  });
  const [errors, setErrors] = useState<{ [key: string]: boolean }>({});

  const handleInputChange = (field: keyof Article, value: string | File[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: false }));
    }
  };

  const handleAddDocument = (file: File) => {
    const currentDocs = formData.documentos || [];
    setFormData((prev) => ({ ...prev, documentos: [...currentDocs, file] }));
    if (errors.documentos) {
      setErrors((prev) => ({ ...prev, documentos: false }));
    }
  };

  const handleRemoveDocument = (index: number) => {
    const currentDocs = formData.documentos || [];
    const newDocs = currentDocs.filter((_, i) => i !== index);
    setFormData((prev) => ({ ...prev, documentos: newDocs }));
  };

  const validateForm = () => {
    const newErrors: { [key: string]: boolean } = {};

    if (!formData.cobertura) newErrors.cobertura = true;
    if (!formData.articulo) newErrors.articulo = true;
    if (!formData.valor) newErrors.valor = true;
    if (
      hasRequiredDocuments &&
      (!formData.documentos || formData.documentos.length === 0)
    )
      newErrors.documentos = true;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({ cobertura: "", articulo: "", valor: "", documentos: [] });
    setErrors({});
  };

  useEffect(() => {
    if (isEditing && editingArticle) {
      setFormData(editingArticle);
      setErrors({});
    } else if (!isEditing) {
      resetForm();
    }
  }, [isEditing, editingArticle, open]);

  return {
    formData,
    errors,
    handleInputChange,
    handleAddDocument,
    handleRemoveDocument,
    validateForm,
    resetForm,
  };
};