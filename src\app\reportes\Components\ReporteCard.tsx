"use client";
import { PrimaryButton } from "@/Styles/General.styles";
import { Divider } from "@mui/material";
import { FunctionComponent, useEffect, useState } from "react";
import styled from "styled-components";
import ModalConfirm from "../ModalConfirm";

type ReporteCardProps = {
  selected?: boolean;
  color?: string;
  onClick: () => void;
  onFinish: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
};

const ReporteCard: FunctionComponent<ReporteCardProps> = ({
  selected,
  data,
  onClick,
  onFinish,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const handleOptionClick = (optionId: string) => {
    setSelectedOptions((prev) =>
      prev.includes(optionId)
        ? prev.filter((id) => id !== optionId)
        : [...prev, optionId]
    );
  };
  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const handleConfirm = () => {
    setOpenConfirmModal(true);
  };

  useEffect(() => {
    console.log("data que llega", data);
  }, [data]);
  return (
    <>
      <StyledLayout
        bgColor={selected ? (data.color ? data.color : "#333") : "#fff"}
        onClick={onClick}
      >
        <StyledHeader selected={selected}>
          <h2 className="title">Wiki something</h2>
          <button className="details">Detalles de la wiki póliza</button>
        </StyledHeader>
        <StyledDataMobile selected={selected} color={data.color}>
          <div className="title">Número de Póliza</div>
          <div className="data">
            {data.noPoliza ? data.noPoliza : "sin data"}
          </div>
          <Divider color={selected ? "#fff" : "#333"} />
          <div className="title">Estatus</div>
          <div className="data">{data.estatus}</div>
          <Divider color={selected ? "#fff" : "#333"} />
          <div className="title">Periodo</div>
          <div className="data">
            {data.fechaInicioCobertura} al {data.fechaFinCobertura}
          </div>
          <Divider color={selected ? "#fff" : "#333"} />
          <div className="title">Suma asegurada</div>
          <div className="data">{data.sumaAsegurada}</div>
          <Divider color={selected ? "#fff" : "#333"} />
          <div className="title">Remanente de suma asegurada</div>
          <div className="data">{data.remanenteSumaAsegurada}</div>
        </StyledDataMobile>
        <TableContainer>
          <StyledTable selected={selected}>
            <thead>
              <tr>
                <th>Número de Póliza</th>
                <th>Estatus</th>
                <th>Periodo</th>
                <th>Suma asegurada</th>
                <th>Remanente de suma asegurada</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{data.noPoliza}</td>
                <td>{data.estatus}</td>
                <td>
                  {data.fechaInicioCobertura} al {data.fechaFinCobertura}
                </td>
                <td>{data.sumaAsegurada}</td>
                <td>{data.remanenteSumaAsegurada}</td>
              </tr>
            </tbody>
          </StyledTable>
        </TableContainer>
      </StyledLayout>
      {selected && (
        <>
          <StyledHeader2>
            <StyledTitle2>¿Qué cobertura quieres reportar?</StyledTitle2>
            <StyledSubtitle>
              Selecciona todas las coberturas que quieras aplicar dentro de tu
              reporte
            </StyledSubtitle>
          </StyledHeader2>
          <OptionButtonContainer>
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {data.idProducto.coberturas?.map((option: any) => (
              <OptionButton
                key={option.id}
                color={data.color}
                onClick={() => handleOptionClick(option.id)}
                selectedOption={selectedOptions.includes(option.id)}
              >
                {option.name}
              </OptionButton>
            ))}
          </OptionButtonContainer>
          <PrimaryButton
            style={{ margin: "0 auto" }}
            disabled={selectedOptions.length === 0}
            onClick={handleConfirm}
          >
            Continuar
          </PrimaryButton>
        </>
      )}
      {openConfirmModal && (
        <>
          <ModalConfirm
            onClose={() => {
              setOpenConfirmModal(false);
              onFinish();
            }}
          />
        </>
      )}
    </>
  );
};

export default ReporteCard;

const StyledLayout = styled.div<{ bgColor?: string }>`
  display: flex;
  border-radius: 20px;
  border: 3px solid var(--Primary-Navy, #10265f);
  box-shadow: -8px 7px 4px 0px rgba(0, 0, 0, 0.1);
  min-height: 133px;
  padding: 10px 92px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 20px;
  background: ${(props) => props.bgColor || "#fff"};
  width: 100%;
  @media (max-width: 1023px) {
    padding: 10px 20px;
  }
`;

const StyledHeader = styled.div<{ selected?: boolean }>`
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .title {
    color: ${(props) => (props.selected ? "#fff" : "#333")};
    text-align: center;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .details {
    color: ${(props) => (props.selected ? "#fff" : "#333")};
    text-align: center;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: auto;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    background: none;
    border: none;
    cursor: pointer;
  }

  @media (max-width: 1023px) {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
`;

const StyledTable = styled.table<{ selected?: boolean }>`
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  overflow: auto;

  thead {
    th {
      color: ${(props) => (props.selected ? "#fff" : "#333")};
      text-align: center;
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      padding: 0 15px;
      white-space: nowrap;
    }
    border-bottom: 2px solid var(--Primary-Navy, #10265f);
  }

  td {
    color: ${(props) => (props.selected ? "#fff" : "#333")};
    text-align: center;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 0 15px;
    white-space: nowrap;
  }
`;

const StyledDataMobile = styled.div<{ selected?: boolean }>`
  display: none;
  color: ${(props) => (props.selected ? "#fff" : "#333")};
  @media (max-width: 1023px) {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    padding: 10px 0;
    .title {
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
    .data {
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
`;

const StyledTitle2 = styled.h2`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 28px;
  font-style: normal;
  font-weight: 400;
  line-height: 50px;
`;

const StyledSubtitle = styled.h2`
  color: var(--Gris-muy-obscuro, #6d6d6d);
  text-align: center;

  /* Wiki/Common/Legal */
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
`;
const StyledHeader2 = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
`;

const OptionButtonContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px 0;
`;

const OptionButton = styled.button<{ color: string; selectedOption?: boolean }>`
  display: flex;
  padding: 20px 30px;
  justify-content: center;
  align-items: center;
  gap: 9.095px;
  color: ${(props) => (props.selectedOption ? "#fff" : props.color)};
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-radius: 10px;
  border: 2px solid ${(props) => props.color};
  background: ${(props) => (props.selectedOption ? props.color : "#fff")};
  width: fit-content;
  cursor: pointer;
  &:hover {
    background: ${(props) => props.color};
    color: #fff;
  }
  &:active {
    background: ${(props) => props.color};
    color: #fff;
    transform: scale(0.95);
    transition: transform 0.1s ease-in-out;
  }
`;

const TableContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
  @media (max-width: 1023px) {
    display: none;
  }
`;
