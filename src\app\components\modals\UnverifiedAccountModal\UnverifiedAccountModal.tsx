"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useOtpCode } from "@/store/signUp/otpCode.store";
import { useCompleteOtp } from "@/hooks/useCompleteOtp";
import {
  Overlay,
  CustomModal,
  Container,
  ModalContent,
  CloseButton,
  IconContainer,
  Title,
  Subtitle,
  UserGreeting,
  ChannelSelection,
  ChannelButton,
  ErrorText,
  IconWrapper
} from "./UnverifiedAccountModal.styles";
import { Email, Sms, Close } from "@mui/icons-material";

interface UnverifiedAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  userInfo: {
    email?: string;
    phone?: string;
    firstName?: string;
    nickname?: string;
  };
  uuid?: string;
}

type VerificationChannel = 'whatsapp' | 'sms' | 'email';

const UnverifiedAccountModal: React.FC<UnverifiedAccountModalProps> = ({
  isOpen,
  onClose,
  userInfo,
  uuid
}) => {
  const [selectedChannel, setSelectedChannel] = useState<VerificationChannel | null>(null);
  const router = useRouter();
  const { completeOtp, loading, error } = useCompleteOtp();
  const { setOtpCode } = useOtpCode();

  const displayName = userInfo.nickname || userInfo.firstName || "Usuario";

  // Bloquear scroll del body cuando el modal esté abierto
  useEffect(() => {
    if (isOpen) {
      // Guardar el scroll actual
      const scrollY = window.scrollY;

      // Bloquear scroll
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.overflow = 'hidden';

      // Prevenir scroll con rueda del mouse y teclas
      const preventScroll = (e: Event) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
      };

      const preventKeyScroll = (e: KeyboardEvent) => {
        // Prevenir teclas de navegación (flechas, page up/down, home, end, space)
        const keys = [32, 33, 34, 35, 36, 37, 38, 39, 40];
        if (keys.includes(e.keyCode)) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }
      };

      // Agregar event listeners
      document.addEventListener('wheel', preventScroll, { passive: false });
      document.addEventListener('touchmove', preventScroll, { passive: false });
      document.addEventListener('keydown', preventKeyScroll);

      return () => {
        // Restaurar scroll al cerrar
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflow = '';
        window.scrollTo(0, scrollY);

        // Remover event listeners
        document.removeEventListener('wheel', preventScroll);
        document.removeEventListener('touchmove', preventScroll);
        document.removeEventListener('keydown', preventKeyScroll);
      };
    }
  }, [isOpen]);

  const handleChannelClick = async (channel: VerificationChannel) => {
    setSelectedChannel(channel);

    try {
      if (!uuid) {
        throw new Error('UUID is required for OTP completion');
      }

      const completeOtpParams = {
        uuid: uuid,
        channel: channel === 'sms' ? 'sms' as const : 'email' as const
      };

      await completeOtp(completeOtpParams);

      // Guardar información en el store para la pantalla OTP
      const sendByConfig = {
        whatsapp: false,
        sms: channel === 'sms',
        email: channel === 'email'
      };

      setOtpCode({
        email: userInfo.email || '',
        phone: userInfo.phone || '',
        name: displayName,
        sendBy: sendByConfig,
        nickname: userInfo.nickname || displayName
      });

      // Cerrar modal y redirigir a pantalla OTP
      onClose();
      router.push('/signUp/otp-code');

    } catch (err) {
      console.error("Error sending OTP:", err);
      // El error ya se maneja en el hook useCompleteOtp
    }
  };

  if (!isOpen) return null;

  return (
    <Overlay>
      <CustomModal>
        <Container>
          <ModalContent>
            <CloseButton onClick={onClose}>
              <Close />
            </CloseButton>

            <IconContainer>
              <Image
                src="/web/img/modals/unverified-account-icon.svg"
                alt="Verificación de cuenta"
                width={200}
                height={120}
                priority
              />
            </IconContainer>

            <Title>Finalizar Registro</Title>
            <UserGreeting>
              Hola! para finalizar tu proceso de registro, te enviaré un código de verificación.
            </UserGreeting>
            <Subtitle>
              ¿Por dónde deseas recibirlo?
            </Subtitle>
            
            <ChannelSelection>
                <ChannelButton
                  className="sms"
                  selected={selectedChannel === 'sms'}
                  onClick={() => handleChannelClick('sms')}
                  disabled={loading}
                >
                  <IconWrapper>
                    <Sms />
                  </IconWrapper>
                  SMS
                </ChannelButton>

                <ChannelButton
                  className="email"
                  selected={selectedChannel === 'email'}
                  onClick={() => handleChannelClick('email')}
                  disabled={loading}
                >
                  <IconWrapper>
                    <Email />
                  </IconWrapper>
                  Email
                </ChannelButton>
            </ChannelSelection>

            {error && <ErrorText>{error.message}</ErrorText>}
          </ModalContent>
        </Container>
      </CustomModal>
    </Overlay>
  );
};

export default UnverifiedAccountModal;
