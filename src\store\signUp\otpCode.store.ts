import { create, type StateCreator } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface OtpCode {
  email: string;
  phone: string;
  name: string;
  nickname?: string;
  sendBy: {
    whatsapp: boolean;
    sms: boolean;
    email: boolean;
  };
  setOtpCode: (view: Partial<OtpCode>) => void;
}

const useOtpCodeStore: StateCreator<OtpCode> = (set) => ({
  email: '',
  phone: '',
  name: '',
  nickname: '',
  sendBy: {
    whatsapp: false,
    sms: false,
    email: false,
  },
  setOtpCode: (view: Partial<OtpCode>) => set({ ...view }),
});

export const useOtpCode = create<OtpCode>()(
  devtools(
    persist(useOtpCodeStore, {
      name: 'otp-code-storage',
    })
  )
);
