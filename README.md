This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app). test

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON><PERSON><PERSON>](https://vercel.com/font), a new font family for Vercel.

## Wiki

### Project Description

Wiki is a project that aims to transform the experience of acquiring and managing insurance through a Web Marketplace and a Mobile Application. This digital ecosystem allows users to create accounts, purchase insurance, check policies, report claims, and access Equinox's contact channels in an agile and convenient manner.

### Objective of the Web Marketplace

The Web Marketplace will allow:

- Creating an account and purchasing insurance from anywhere.
- Checking policies in real-time.
- Reporting claims with ease.
- Accessing Equinox's contact channels.
- Providing an accessible experience from any web browser.

### Objective of the Mobile Application Marketplace

The Mobile Application Marketplace, available on iOS and Android stores, will offer:

- Insurance management and contracting anytime, anywhere.
- Efficient claim reporting.
- Push notifications to keep customers informed.
- Interactive features that promote loyalty and engagement.

### Specific Objectives

#### Attraction and Expansion:

- Expand the customer base by offering personalized products according to needs and preferences.
- Improve customer satisfaction through tailored solutions.

#### Conversion and Retention:

- Convert interested prospects into customers through a personalized user experience.
- Foster customer retention thanks to support guided by an AI-powered avatar.

#### Claim Reporting and Tracking:

- Facilitate efficient claim reporting.
- Offer detailed tracking of the status of each claim.

### Users

- End users: Insurance buyers who access the platform to manage their protection needs.
- Administrator: Person responsible for managing the platform and the offered products.

## Description

(Frontend)

## SetUp (Dev Environment)

# Clone the repository
- https://github.com/TheRocketCodeMX/wikiFront.git

# Access the project directory
- cd wiki

## Dependencies
This project uses the following dependencies:

- React
- Next.js
- TypeScript
- @emotion/react: ^11.13.5
- @emotion/styled: ^11.13.5
- @mui/icons-material: ^6.1.9
- @mui/material: ^6.1.9
- next: 15.0.3
- react: ^18.3.1
- react-dom: ^18.3.1

## Development Tools

This project uses the following development tools:

- Prettier
- Jest
- @types/node: ^20
- @types/react: ^18.3.12
- @types/react-dom: ^18
- eslint: ^8
- eslint-config-next: 15.0.3
- postcss: ^8
- tailwindcss: ^3.4.1
- typescript: ^5

## Node.js Version

> **important!**  
> This project requires Node.js version 20.16.0

Follow the next steps to run the project:

1. Create an env file where you set the following variables in your environment:

2. Install all dependencies

    ```bash
    # via npm
    $ npm install
    ```

## Getting Started

```bash
npm run dev
# or
yarn dev
```

## Back Project Structure

```
└── 📁src
    └── 📁app
        └── 📁fonts
            └── fonts.ts
        └── globals.css
        └── layout.tsx
        └── page.tsx
    └── 📁application
    └── 📁domain
    └── 📁infrastructure
        └── 📁components
            └── 📁home
                └── Banner.tsx
                └── ButtonFullWidth.tsx
                └── MainFooter.tsx
                └── MobileBanner.tsx
                └── MobileFooter.tsx
        └── 📁data
            └── home.data.ts
        └── 📁models
            └── home.model.ts
        └── 📁styles
            └── home.css
```

## Contribution

To contribute to this project, follow these steps:

1. Fork the repository.
2. Create a branch for your feature or bug fix:
    ```bash
    git checkout -b branch-name
    ```
3. Make sure your branch is up to date with the main branch:
    ```bash
    git pull origin main
    ```
4. Make your changes and commit them:
    ```bash
    git commit -m "Description of the change"
    ```
5. Submit a pull request.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
