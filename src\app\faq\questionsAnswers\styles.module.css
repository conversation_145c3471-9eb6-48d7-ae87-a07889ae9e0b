.paymentInfo,
.serviceGrid {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.paymentInfo {
  padding: 0 10px;
  gap: 10px;
  font-size: 18px;
  color: #7d7d7d;
}
.cmoIngresoA {
  align-self: stretch;
  position: relative;
  font-weight: 600;
}
.cmoIngresoAMiCuentaParent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  word-break: break-word;
  overflow-wrap: break-word;
  padding: 0 10px;
  gap: 10px;
  font-size: 18px;
  color: #7d7d7d;
}
.question {
  font-size: 16px;
  font-family: Poppins;
  font-weight: 600;
}
.answer {
  color: #333333;
  font-size: 15px;
  padding: 6px;
  line-height: 1.6;
  font-family: Poppins;
}

@media screen and (max-width: 450px) {
  .question {
    font-size: 15px;
  }
}
