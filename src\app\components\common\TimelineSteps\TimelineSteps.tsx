import React from 'react';
import IconWrapper from '../IconWrapper';
import { TimelineStep } from '../../../../types/reports';
import * as S from './TimelineSteps.styles';
import { DocumentIcon, FiniquitoIcon, PagoIcon, FinalizadoIcon } from '../Icons';

interface TimelineStepsProps {
  steps: TimelineStep[];
  onStepClick?: (step: TimelineStep) => void;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'Documentos':
      return <DocumentIcon />;
    case 'Finiquito':
      return <FiniquitoIcon />;
    case 'Finalizado':
      return <FinalizadoIcon />;
    case 'Pago':
      return <PagoIcon />;
    default:
      return <DocumentIcon />;
  }
};

const TimelineSteps: React.FC<TimelineStepsProps> = ({ steps, onStepClick }) => {
  return (
    <S.Container>
      {steps.map((step, index) => {
        const isDisabled = !step.isActive && !step.isCompleted;
        const isLineEnabled = step.isCompleted;
        
        return (
          <React.Fragment key={step.id}>
            <S.StepContainer>
              <S.StepWrapper>
                <IconWrapper
                  icon={getStatusIcon(step.status)}
                  onClick={() => !isDisabled && onStepClick?.(step)}
                  title={`${step.label}`}
                  isDisabled={isDisabled}
                />
                <S.StepLabel $isDisabled={isDisabled}>
                  {step.label}
                </S.StepLabel>
              </S.StepWrapper>
            </S.StepContainer>
            {index < steps.length - 1 && (
              <S.ConnectorLine $isEnabled={isLineEnabled} />
            )}
          </React.Fragment>
        );
      })}
    </S.Container>
  );
};

export default TimelineSteps;
