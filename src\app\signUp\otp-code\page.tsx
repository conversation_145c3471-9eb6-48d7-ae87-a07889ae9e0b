"use client";
import type { NextPage } from "next";
import { <PERSON>ton, Typography } from "@mui/material";
import Image from "next/image";
import MessageOtpIcon from "../../../../public/web/img/verification/MessageOtpIcon.svg";
import OTPInput from "./components/OtpInput";
import { useOtpCode } from "@/store/signUp/otpCode.store";
import { useSendOtpCode } from "@/hooks/signUp/useSendOtpCode";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { OtpCodeEnum } from "@/enums/signUp/otp-code.enum";
import * as S from "./otp-code.styles"; // Importa los styled-components refactorizados
import useTemp from "@/hooks/useTemp";
import { useIsLogged } from "@/app/LoginProvider";
import { AuthService } from "@/infrastructure/services/authService";

const OtpCodePage: NextPage = () => {
  const router = useRouter();
  const { name, nickname, phone, sendBy, email } = useOtpCode((state) => state);
  const [otpSent, setOtpSent] = useState<string>("");
  const [errors, setErrors] = useState<boolean>(false);
  const [showResentMessage, setShowResentMessage] = useState(false);

  const { verifyOtpCode, otpCode } = useSendOtpCode();
  const { setLogged, setUser } = useIsLogged();
  const navigate = useRouter();
  const authService = new AuthService();

  const handleVerifyOtpCode = async () => {
    const selectedMethod = JSON.parse(
      localStorage.getItem("otp-code-storage") || "{}"
    )?.state?.sendBy;

    // Build parameters: use email if that method was chosen, otherwise phone
    const params = {
      ...(selectedMethod?.email ? { email } : { phone }),
      acction: OtpCodeEnum.REGISTER,
    };

    const response = await verifyOtpCode(params, otpSent);

    if (response) {
      // Si la respuesta incluye accessToken, establecer la sesión
      if (response.accessToken) {
        try {
          // Obtener información completa del usuario usando el token
          const userInfo = await authService.getUserInfo();
          console.log("Información del usuario después de la verificación OTP:", userInfo);

          if (userInfo) {
            // Usar la información completa del usuario desde el backend
            localStorage.setItem("user", JSON.stringify(userInfo));
            setUser(userInfo);
          } else {
            // Fallback: usar información básica si no se puede obtener la completa
            const basicUser = {
              email: email || phone,
              firstName: name || "Usuario",
              nickname: nickname || "Usuario"
            };
            localStorage.setItem("user", JSON.stringify(basicUser));
            setUser(basicUser);
          }

          // Marcar al usuario como loggeado
          setLogged(true);

          // Dar tiempo para que el estado se propague antes de navegar
          setTimeout(() => {
            const purchaseProduct = localStorage.getItem("purchaseProduct");
            if (purchaseProduct) {
              navigate.push(purchaseProduct);
              localStorage.removeItem("purchaseProduct");
            } else {
              router.push("/");
            }
          }, 100);
          return;

        } catch (error) {
          console.error("Error getting user info after OTP verification:", error);

          // En caso de error, usar información básica como fallback
          const basicUser = {
            email: email || phone,
            firstName: name || "Usuario",
            nickname: nickname || "Usuario"
          };
          localStorage.setItem("user", JSON.stringify(basicUser));
          setUser(basicUser);
          setLogged(true);

          setTimeout(() => {
            const purchaseProduct = localStorage.getItem("purchaseProduct");
            if (purchaseProduct) {
              navigate.push(purchaseProduct);
              localStorage.removeItem("purchaseProduct");
            } else {
              router.push("/");
            }
          }, 100);
          return;
        }
      }

      const purchaseProduct = localStorage.getItem("purchaseProduct");
      if (purchaseProduct) {
        navigate.push(purchaseProduct);
        localStorage.removeItem("purchaseProduct");
      } else {
        router.push("/");
      }
      return;
    }
    setErrors(true);
  };

  const { time, disable, restart } = useTemp();
  const handleSendOtp = async () => {
    restart();
    const selectedMethod = JSON.parse(
      localStorage.getItem("otp-code-storage") || "{}"
    )?.state?.sendBy;

    // Build parameters: use email if that method was chosen, otherwise phone
    const params = {
      ...(selectedMethod?.email ? { email } : { phone }),
      acction: OtpCodeEnum.REGISTER,
    };
    const response = await otpCode(params);

    if (response) {
      router.push("/signUp/otp-code");
      setShowResentMessage(true);
      setTimeout(() => {
        setShowResentMessage(false);
      }, 5000);
    }
    return;
  };

  const meansOfSendingText = Object.entries(sendBy).find(([, value]) => value);

  return (
    <S.SectionOtpCode>
      <S.SectionPlanes>
        <S.Text>
          <S.HolaSoyTikiSolo>¡Muchas gracias!</S.HolaSoyTikiSolo>
          <S.HolaSoyTikiSolo1>
            {nickname || name}, te he enviado un código a tu{" "}
            {meansOfSendingText ? meansOfSendingText[0] : ""} para verificar que
            eres tú.
          </S.HolaSoyTikiSolo1>
          <S.HolaSoyTikiSolo1>Por favor introdúcelo aquí :)</S.HolaSoyTikiSolo1>
        </S.Text>
        <S.CardDigitsContainer>
          <S.CardDigits>
            <S.IconSmsWrapper>
              <S.IconSms>
                <Image
                  src={MessageOtpIcon}
                  width={48}
                  height={48}
                  alt=""
                  // Puedes agregar "fill" o ajustar según sea necesario
                />
              </S.IconSms>
            </S.IconSmsWrapper>
            <S.Enter4DigitsCodeParent>
              <S.Enter4Digits>
                Introduce los 6 dígitos que recibiste
              </S.Enter4Digits>
              <S.Grupo16070>
                <OTPInput
                  setOtpSent={setOtpSent}
                  errors={errors}
                  setErrors={setErrors}
                />
              </S.Grupo16070>
            </S.Enter4DigitsCodeParent>
            <S.FrameParent>
              <S.ButtonParent>
                <Button onClick={handleSendOtp} disabled={!disable}>
                  <S.ButtonText>Reenviar</S.ButtonText>
                </Button>
                <Typography
                  variant="inherit"
                  component="b"
                  sx={{ fontSize: "18px", fontWeight: "700" }}
                >
                  <S.B>{time}</S.B>
                </Typography>
              </S.ButtonParent>
              <S.Buttons>
                <Button onClick={handleVerifyOtpCode}>
                  <S.Button1 disabled={disable}>Continuar</S.Button1>
                </Button>
              </S.Buttons>
            </S.FrameParent>
          </S.CardDigits>
          {showResentMessage && (
            <S.MessageResent>
              Se reenvió el código de verificación
            </S.MessageResent>
          )}
        </S.CardDigitsContainer>
      </S.SectionPlanes>
    </S.SectionOtpCode>
  );
};

export default OtpCodePage;
