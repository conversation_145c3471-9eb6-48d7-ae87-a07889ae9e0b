import styled from "styled-components";

export const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow: hidden;
  overscroll-behavior: none;
  touch-action: none;
  -webkit-overflow-scrolling: touch;
  pointer-events: all;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  /* Bloquear scroll del body */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }
`;

export const CustomModal = styled.div`
  background: white;
  border-radius: 24px;
  padding: 32px;
  max-width: 1200px; /* Aumenta el máximo para pantallas grandes */
  width: 80vw; /* Adaptable a 80vw */
  min-width: 320px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: none;
  pointer-events: all;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  position: relative;
  margin: 20px;

  /* Responsive adjustments */
  @media (max-width: 1024px) {
    max-width: 95vw;
    width: 95vw;
    padding: 24px;
    border-radius: 16px;
    margin: 10px;
    max-height: 85vh;
  }

  @media (max-width: 600px) {
    width: 98vw;
    max-width: 98vw;
    padding: 12px;
    border-radius: 10px;
    margin: 2px;
    max-height: 95vh;
  }

  @media (max-height: 600px) {
    max-height: 95vh;
    padding: 20px;
  }
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
`;

export const ModalContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  gap: 16px;
`;

export const CloseButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  color: #666;

  &:hover {
    background-color: #f5f5f5;
    color: #333;
  }

  svg {
    width: 24px;
    height: 24px;
  }
`;

export const IconContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
`;

export const Title = styled.h2`
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 600;
  color: #10265f;
  margin: 0;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 24px;
  }

  @media (max-width: 480px) {
    font-size: 22px;
  }
`;

export const Subtitle = styled.p`
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin: 0;
  line-height: 1.5;
  max-width: 400px;
`;

export const DataContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  max-width: 400px;
  margin: 0;

  @media (max-width: 480px) {
    max-width: 100%;
  }
`;

export const DataRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 12px;
  border-radius: 8px;
  gap: 8px;

  @media (max-width: 480px) {
    padding: 6px 10px;
    gap: 6px;
    flex-wrap: wrap;
  }
`;

export const DataLabel = styled.span`
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  min-width: 70px;
`;

export const DataValue = styled.span`
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  flex: 1;
  text-align: left;
  margin-right: 8px;
`;

export const StatusBadge = styled.span<{ $isExisting: boolean }>`
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 20px;
  color: white;
  background-color: ${props => props.$isExisting ? '#ef4444' : '#10b981'};
  min-width: 80px;
  text-align: center;
`;

export const FooterText = styled.p`
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin: 8px 0 0 0;
  line-height: 1.5;
`;

export const ActionButton = styled.button`
  background: #10265f;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 24px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  margin-top: 4px;

  &:hover {
    background: #0d1d4f;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 38, 95, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  @media (max-width: 480px) {
    padding: 10px 20px;
    font-size: 14px;
    min-width: 140px;
  }
`;
