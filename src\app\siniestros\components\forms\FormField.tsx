import React from "react";

interface FormFieldProps {
  label: string;
  error?: boolean;
  errorMessage?: string;
  children: React.ReactNode;
}

const FormField = ({ label, error, errorMessage, children }: FormFieldProps) => {
  return (
    <label
      style={{
        display: "flex",
        flexDirection: "column",
        marginBottom: "8px",
        width: "100%",
        gap: "5px",
        fontWeight: 600,
        marginTop: "1rem",
      }}
    >
      {label}
      {children}
      {error && errorMessage && (
        <span style={{ color: "#ff0000", fontSize: "0.8rem" }}>
          {errorMessage}
        </span>
      )}
    </label>
  );
};

export default FormField;