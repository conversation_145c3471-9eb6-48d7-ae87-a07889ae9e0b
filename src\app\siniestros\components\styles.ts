export const screenOverlayStyle = {
  position: 'fixed' as const,
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1000,
};

export const screenContentStyle = {
  display: 'flex',
  flexDirection: 'column' as const,
  backgroundColor: '#fff',
  padding: '3rem',
  gap: '2rem',
  width: '100vw',
  height: '100vh',
  overflow: 'auto',
  position: 'relative' as const,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
};

export const btnStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: "#10265F",
  borderRadius: "10px",
  height: "40px",
  minHeight: '40px',
  width: "40px",
  textTransform: "none",
  fontSize: "0.9rem",
  fontWeight: 500,
  "&:hover": {
    backgroundColor: "#51519b",
  },  
}