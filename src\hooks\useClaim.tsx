
import { useState, useEffect, useCallback } from "react";
import { ClaimService } from "@/infrastructure/services/claimService";

export interface Claim {
  id: number
  name: string
  description: string
  image: string
  coberturas: Cobertura[]
  poliza: string,
  planId: number,
  montoPlan: string
}

export interface Cobertura {
  id: number
  name: string
  description: string
  icon: Icon
  documentos: Documento[]
}

export interface Icon {
  name: string
  url: string
}

export interface Documento {
  id: number
  nombreDocumento: string
  descripcion: string
  obligatorio: boolean
}


export interface Banner {
  id: number;
  url_banner: string;
  linkUrl: string;
  order: number;
  isActive?: boolean;
}

export const useClaims = () => {
  const [claims, setClaims] = useState<Claim[]>([]);
  const [remainingCoverage, setRemainingCoverage] = useState<number>(0);
  const [loading, setLoading] = useState(true); // Iniciar en true para mostrar loader inmediatamente
  const [error, setError] = useState<unknown>(null);

  const fetchClaims = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const service = new ClaimService();

      // Agregar timeout para evitar carga infinita
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: La carga de reclamos está tomando demasiado tiempo')), 10000)
      );

      const claimsPromise = service.getDocuments();

      const list = await Promise.race([claimsPromise, timeoutPromise]) as Claim[];
      setClaims(list);
    } catch (err) {
      setError(err);
      console.error('Error fetching reclamos:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchRemainingCoverage = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const service = new ClaimService();

      // Agregar timeout para evitar carga infinita
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: La carga de cobertura restante está tomando demasiado tiempo')), 10000)
      );

      const remainingCoveragePromise = service.remainingCoverage(id);

      const coverage = await Promise.race([remainingCoveragePromise, timeoutPromise]) as {idPoliza: number, montoAsegurado: number};
      setRemainingCoverage(coverage.montoAsegurado);
    } catch (err) {
      setError(err);
      console.error('Error fetching cobertura restante:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchClaims();
  }, [fetchClaims]);

  return { claims, loading, error, refetch: fetchClaims, fetchRemainingCoverage, remainingCoverage };
};
