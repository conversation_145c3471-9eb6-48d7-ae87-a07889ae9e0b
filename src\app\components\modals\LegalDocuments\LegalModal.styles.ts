import styled from "styled-components";

export const Overlay = styled.div`
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
`;

export const Modal = styled.div`
  background: white;
  max-width: 1294px;
  display: flex;
  flex-direction: column;
  width: 90%;
  gap: 32px;
  border-radius: 12px;
  padding: 40px 80px;
  position: relative;
  overflow-y: auto;
  max-height: 90vh;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  @media (max-width: 768px) {
    padding: 20px;
    width: 100%;
    max-width: 100%;
    gap: 16px;
  }
`;

export const CloseButton = styled.button`
  position: absolute;
  top: 16px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
`;

export const Title = styled.h2`
  color: #10265f;
  text-align: center;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;

export const SectionTitle = styled.h3`
  color: #10265f;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 32px;
  text-align: left;
  @media (max-width: 768px) {
    font-size: 16px;
    line-height: 24px;
  }
`;

export const Content = styled.div`
  color: #333;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  text-align: justify;
  white-space: pre-line;
  @media (max-width: 768px) {
    font-size: 14px;
    line-height: 24px;
  }

  ul {
    margin-left: 16px;
    list-style: disc;
  }
`;

export const FooterButton = styled.button`
  background: none;
  border: none;
  color: #10265f;
  font-family: Poppins, sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-align: center;
  text-decoration: underline;
  cursor: pointer;
  margin: 0 0 0 auto;
  display: block;

  &:hover {
    color: #1d3570;
  }
`;
