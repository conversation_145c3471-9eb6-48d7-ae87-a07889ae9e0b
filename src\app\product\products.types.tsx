interface CoverageFeature {
  concepto: string;
  sumaAsegurada: string;
  incluido: string;
  icon?: string;
}

export interface CoverageType {
  id: number;
  deducible: string;
  precio: string;
  suma: string;
  mesesSinIntereses: boolean;
  features: CoverageFeature[];
}

export type Feature = {
  icon?: { nombre: string; url: string };
  title: string;
  description: string;
};

export type Product = {
  title: string;
  color: string;
  image: string;
  secondaryImage: string;
  type: string;
  description: string;
  features: Feature[];
  subtitle: string;
  subtitleDescription: string;
  CovergaeOptions: CoverageType[];
  id: string;
};
