import { Upload, FileText, Trash2 } from "lucide-react";

interface DocumentFieldProps {
  label: string;
  description?: string;
  fieldName: string;
  file: File | null;
  onFileUpload: (fieldName: string, file: File | null) => void;
  onRemoveFile: (fieldName: string) => void;
  hasError?: boolean;
  errorMessage?: string;
  required?: boolean;
}

const DocumentField = ({
  label,
  description,
  fieldName,
  file,
  onFileUpload,
  onRemoveFile,
  hasError,
  errorMessage,
  // required = true
}: DocumentFieldProps) => {
  const inputId = `${fieldName}-input`;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      onFileUpload(fieldName, selectedFile);
      e.target.value = '';
    }
  };

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRemoveFile(fieldName);
  };

  return (
    <div 
      style={{ 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1rem'
      }} 
      data-field={fieldName}
    >
      <div style={{ flex: '1', minWidth: '200px' }}>
        <label style={{ 
          fontWeight: 600, 
          fontSize: '1rem', 
          display: 'block', 
          color: hasError ? '#ff0000' : 'inherit' 
        }}>
          {label}
        </label>
        {description && (
          <span style={{ fontSize: '0.8rem', fontWeight: 400, color: '#6b7280' }}>
            {description}
          </span>
        )}
        {hasError && errorMessage && (
          <span style={{ 
            color: '#ff0000', 
            fontSize: '0.8rem', 
            display: 'block', 
            marginTop: '4px' 
          }}>
            {errorMessage}
          </span>
        )}
      </div>
      
      <div style={{
        border: '2px dashed #51519b',
        borderRadius: '12px',
        padding: '0.8rem 1rem',
        textAlign: 'center',
        backgroundColor: file ? '#f8fffe' : '#fafbff',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        minHeight: '60px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '0.3rem',
        minWidth: '200px',
        maxWidth: '250px'
      }}
      onClick={() => document.getElementById(inputId)?.click()}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = file ? '#f0fffe' : '#f0f2ff';
        e.currentTarget.style.borderColor = '#51519b';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = file ? '#f8fffe' : '#fafbff';
        e.currentTarget.style.borderColor = '#51519b';
      }}>
        <input 
          id={inputId}
          type="file" 
          accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
          onChange={handleFileChange}
          style={{ display: 'none' }}
        />
        
        {file ? (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '1rem', 
            width: '100%', 
            justifyContent: 'space-between' 
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <FileText size={20} color="#10b981" />
              <span style={{ 
                color: '#374151', 
                fontSize: '0.9rem', 
                fontWeight: 500,
                maxWidth: '120px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {file.name}
              </span>
            </div>
            <button
              type="button"
              onClick={handleRemoveClick}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '0.2rem',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#fee2e2';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <Trash2 size={16} color="#ef4444" />
            </button>
          </div>
        ) : (
          <>
            <Upload size={20} color="#51519b" />
            <div style={{ color: '#6b7280', fontSize: '0.8rem' }}>
              Subir archivo
            </div>
            <div style={{ color: '#9ca3af', fontSize: '0.7rem' }}>
              PDF, JPG, PNG, DOC
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DocumentField;