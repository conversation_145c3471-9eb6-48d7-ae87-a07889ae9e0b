'use client';

import { useState } from "react";
import { Claim, useClaims } from "@/hooks/useClaim";
import { Button } from "@mui/material";
import { ArrowLeft } from 'lucide-react'
import { useRouter } from "next/navigation";
import ModalClaimRequirements from "../components/ClaimRequirements";
import Image from "next/image";
import { screenContentStyle, screenOverlayStyle, btnStyles } from "../components/styles";

const ChooseClaim = () => {
  const router = useRouter();
  const { claims } = useClaims()
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);

  const openModal = (claim: Claim) => {
    setSelectedClaim(claim);
  }

  const closeModal = () => {
    setSelectedClaim(null);
  }

  const headerStyle = {
    display: 'block',
    fontSize: '1.6rem',
    fontWeight: "bold",
  };

  return (
    <div style={screenOverlayStyle} >
      <div style={screenContentStyle}>
        <Button
          variant="contained"
          onClick={() => router.back()}
          sx={btnStyles}
        >
          <ArrowLeft />
        </Button>
        
        <section style={{
          display: 'flex',
          gap: '2rem',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          marginTop: '2rem',
          marginBottom: '2rem',
        }}>
          <div>
            <label htmlFor="title" style={headerStyle}>¡Descuida!</label>
            <strong style={{ fontSize: '1.7rem' }}>Te ayudaremos a resolver esto</strong>
            <p style={{ fontSize: '1.2rem', fontWeight: 500 }}>Para comenzar, selecciona una póliza</p>
          </div>

        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          maxWidth: '900px', // Aumentado para acomodar mayor separación entre columnas
          width: '100%',
          columnGap: '4rem', // Separación entre columnas
          rowGap: '2rem',    // Separación entre filas (mantiene el original)
          marginTop: '3rem',
          justifyItems: 'center'
        }}>
          {claims.map((claim: Claim) => (
            <div key={claim.id} onClick={() => openModal(claim)} style={{ 
              cursor: 'pointer', 
              textAlign: 'center', 
              width: '200px', 
              display: 'flex', 
              flexDirection:'column', 
              alignItems: 'center',
              maxWidth: '200px'
            }}>
              <Image src={claim.image} alt={claim.name} width={200} height={200} />
              <h3>{claim.name}</h3>
              <h4 style={{ color: "gray", fontWeight: 500 }}>${claim.montoPlan}</h4>
              <p>{claim.description}</p>
            </div>
          ))}
        </div>

        {selectedClaim && (
          <ModalClaimRequirements
            setOpen={closeModal}
            coberturas={{ coberturas: selectedClaim.coberturas, image: selectedClaim.image, claimId: selectedClaim.id }}
            open={!!selectedClaim}
          />
        )}
        </section>
      </div>
    </div>
  )
}

export default ChooseClaim;
