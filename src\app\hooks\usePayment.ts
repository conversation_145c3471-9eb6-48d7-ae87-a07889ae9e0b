import { useState } from 'react';
import { PaymentService, PaymentRequest, PaymentResponse } from '@/infrastructure/services/paymentService';

/**
 * Hook para manejar el proceso de pago real con loader y petición al backend.
 * @param onSuccess función a ejecutar cuando el pago es exitoso
 * @param onError función a ejecutar cuando el pago falla
 */
export function usePayment(onSuccess?: (res: PaymentResponse) => void, onError?: (err: unknown) => void) {
  const [loading, setLoading] = useState(false);
  const paymentService = new PaymentService();

  const handlePayment = async (data: PaymentRequest) => {
    setLoading(true);
    try {
      const res = await paymentService.makePayment(data);
      // Siempre ejecuta onSuccess con la respuesta del backend, la UI decide qué mostrar
      if (onSuccess) onSuccess(res);
    } catch (err) {
      if (onError) onError(err);
    } finally {
      setLoading(false);
    }
  };

  return { loading, handlePayment };
}
