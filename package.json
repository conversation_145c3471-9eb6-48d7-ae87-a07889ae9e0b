{"name": "wiki-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.1", "@mui/material": "^6.2.1", "@mui/material-nextjs": "^6.2.0", "axios": "^1.7.9", "formik": "^2.4.6", "gsap": "^3.12.5", "lucide-react": "^0.468.0", "next": "15.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "styled-components": "^6.1.13", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18", "@types/styled-components": "^5.1.34", "babel-plugin-styled-components": "^2.1.4", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}