name: WikiFront CI/CD
on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.17.0]

    steps:
      - uses: actions/checkout@v4
      - name: Clean folder
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: app
          key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'
          script: |
            rm -Rf /home/<USER>/wikiFront
            mkdir /home/<USER>/wikiFront
            chmod -Rf 777 /home/<USER>/wikiFront
      - name: rsync deployments
        uses: burnett01/rsync-deployments@5.1
        with:
          switches: -avzr --delete
          path: ./*
          remote_path: /home/<USER>/wikiFront/
          remote_host: *************
          remote_user: app
          remote_key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'

      - name: deploy to server and application start
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: app
          key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'
          script: |
            export PATH=$HOME/.npm-global/bin:$PATH
            cd /home/<USER>/wikiFront/ && npm install
            echo ${{ vars.DEV_ENV }} | base64 --decode --ignore-garbage > .env
            cd /home/<USER>/wikiFront/ && npm run build
            pm2 resurrect
            cd /home/<USER>/wikiFront/ && pm2 stop wikiFront && pm2 delete wikiFront && pm2 start npm --name "wikiFront" -- start
