"use client";
import React, { useState } from "react";
import PolicyCard from "./PolicyCard";
import { usePolicies, useDownloadPolicyPDF } from "@/hooks/usePolicies";
import { EmptyState } from "@/app/components/common";
import styled from "styled-components";

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: auto;
  align-items: start;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const MyPolicies = () => {
  const { policies, loading } = usePolicies();
  const { downloadPolicyPDF } = useDownloadPolicyPDF();
  const [expandedIndexes, setExpandedIndexes] = useState<number[]>([]);

  const handleExpand = (index: number) => {
    if (expandedIndexes.includes(index)) {
      // Si ya está expandida, la removemos del array
      setExpandedIndexes(expandedIndexes.filter((i) => i !== index));
    } else {
      // Si no está expandida, la agregamos al array
      setExpandedIndexes([...expandedIndexes, index]);
    }
  };

  return (
    <div style={{ padding: "24px" }}>
      <h2
        style={{
          fontSize: "34px",
          fontWeight: "600",
          marginBottom: "24px",
          color: "#10265f",
        }}
      >
        Mis pólizas
      </h2>

      {loading ? (
        <p>Cargando...</p>
      ) : policies.length === 0 ? (
        <EmptyState message="No se encontraron pólizas disponibles." />
      ) : (
        <GridContainer>
          {policies.map((policy, index) => (
            <PolicyCard
              key={`${policy.numeroPoliza}-${
                policy.idProducto?.name || ""
              }-${index}`}
              policy={policy}
              onDownload={downloadPolicyPDF}
              onEdit={() => console.log("Editar póliza", policy.numeroPoliza)}
              isExpanded={expandedIndexes.includes(index)}
              onToggleExpand={() => handleExpand(index)}
            />
          ))}
        </GridContainer>
      )}
    </div>
  );
};

export default MyPolicies;
