import axiosInstance from '@/core/axios';
import { Document, DocumentsByProduct } from '@/types';

export class DocumentValidationService {
  private baseUrl = '/api/document-validation';

  /**
   * Obtiene todos los documentos de una reclamación
   */
  async getDocumentsByReport(numeroPoliza: string): Promise<DocumentsByProduct> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/reports/${numeroPoliza}/documents`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener documentos:', error);
      throw new Error('Error al cargar los documentos');
    }
  }

  /**
   * Descarga un documento
   */
  async downloadDocument(documentId: string): Promise<Blob> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/documents/${documentId}/download`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error al descargar documento:', error);
      throw new Error('Error al descargar el documento');
    }
  }

  /**
   * Obtiene la URL pública de un documento para descarga directa
   */
  async getDocumentDownloadUrl(documentId: string): Promise<string> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/documents/${documentId}/download-url`);
      return response.data.url;
    } catch (error) {
      console.error('Error al obtener URL de descarga:', error);
      throw new Error('Error al obtener el enlace de descarga');
    }
  }

  /**
   * Sube un nuevo documento o reemplaza uno existente
   */
  async uploadDocument(documentId: string, file: File): Promise<Document> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentId', documentId);

      const response = await axiosInstance.post(`${this.baseUrl}/documents/${documentId}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error al subir documento:', error);
      throw new Error('Error al subir el documento');
    }
  }

  /**
   * Elimina un documento
   */
  async deleteDocument(documentId: string): Promise<void> {
    try {
      await axiosInstance.delete(`${this.baseUrl}/documents/${documentId}`);
    } catch (error) {
      console.error('Error al eliminar documento:', error);
      throw new Error('Error al eliminar el documento');
    }
  }

  /**
   * Obtiene el estado de validación de todos los documentos de una reclamación
   */
  async getValidationStatus(numeroPoliza: string): Promise<{
    total: number;
    approved: number;
    rejected: number;
    pending: number;
    canProceed: boolean;
  }> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/reports/${numeroPoliza}/validation-status`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener estado de validación:', error);
      throw new Error('Error al obtener el estado de validación');
    }
  }

  /**
   * Valida el formato y tamaño de un archivo antes de subirlo
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSizeMB = 5;
    const allowedFormats = ['pdf', 'jpg', 'jpeg', 'png'];
    
    // Validar tamaño
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        isValid: false,
        error: `El archivo es demasiado grande. Tamaño máximo: ${maxSizeMB}MB`
      };
    }

    // Validar formato
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !allowedFormats.includes(fileExtension)) {
      return {
        isValid: false,
        error: `Formato no válido. Formatos permitidos: ${allowedFormats.join(', ').toUpperCase()}`
      };
    }

    return { isValid: true };
  }
}
