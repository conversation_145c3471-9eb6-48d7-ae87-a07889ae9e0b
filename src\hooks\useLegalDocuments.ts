import { useEffect, useState } from "react"
import { LegalDocumentsService } from "@/infrastructure/services/legalDocumentsService"
import { useSnackbar } from "@/context/SnackbarContext"

const service = new LegalDocumentsService()

export const useLegalDocuments = (enabled = true) => {
  const [privacySections, setPrivacySections] = useState([])
  const [termsSections, setTermsSections] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { showError } = useSnackbar()

  const fetchDocuments = async () => {
    try {
      setLoading(true)
      const [privacy, terms] = await Promise.all([
        service.getPrivacyPolicy(),
        service.getTermsAndConditions(),
      ])
      setPrivacySections(privacy)
      setTermsSections(terms)
      setError(null)
    } catch (err) {
      if (err instanceof Error) {
        setError(err)
        console.error("Error al cargar documentos legales:", err.message)
        showError("No se pudieron cargar los documentos legales.")
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (enabled) {
      fetchDocuments()
    }
  }, [enabled])

  return {
    privacySections,
    termsSections,
    loading,
    error,
  }
}
