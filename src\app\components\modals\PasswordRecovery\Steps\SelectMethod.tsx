import Image from "next/image";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tos<PERSON>ontainer,
  EmailButton,
  Icon,
  SMSButton,
  StyledSubtitle,
  StyledTitle,
} from "../Styles/RecoveryPassword.styles";

const SelectMethod = ({
  handleNextStep,
}: {
  handleNextStep: (
    step: "resetPassword" | "selectMethod" | "sms" | "mail" | "verify"
  ) => void;
}) => {
  return (
    <>
      <Image
        src="/web/img/cardsProducts/wikiHackeo.png"
        alt="Logo"
        width={195}
        height={180}
      />

      <StyledTitle>Recuperar Contraseña</StyledTitle>
      <StyledSubtitle>
        Por favor, selecciona el método por el cual quieres seguir la
        recuperación.
      </StyledSubtitle>
      <DatosContainer>
        <StyledTitle>¿Por dónde deseas recibirlo?</StyledTitle>
        <ButtonContainer>
          <SMSButton onClick={() => handleNextStep("sms")}>
            <Icon
              src="/web/img/verification/message.svg"
              alt=""
              width={21}
              height={21}
            />
            SMS
          </SMSButton>
          <EmailButton onClick={() => handleNextStep("mail")}>
            <Icon
              src="/web/img/verification/email.svg"
              alt=""
              width={21}
              height={21}
            />
            Email
          </EmailButton>
        </ButtonContainer>
      </DatosContainer>
    </>
  );
};

export default SelectMethod;
