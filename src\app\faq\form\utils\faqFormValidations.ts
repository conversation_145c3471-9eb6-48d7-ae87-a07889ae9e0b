/**
 * Valida si un correo electrónico tiene un formato válido.
 * @param {string} email - El correo electrónico a validar.
 * @returns {boolean} - `true` si el correo es válido, `false` en caso contrario.
 */
const validateEmail = (email: string) => {
  // Expresión regular para validar el formato del correo electrónico
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Valida un campo específico del formulario según su tipo.
 * @param {string} field - El nombre del campo a validar (por ejemplo, "name", "email").
 * @param {string} value - El valor ingresado en el campo.
 * @returns {string} - Un mensaje de error si la validación falla, o una cadena vacía si es válida.
 */
export const validateField = (field: string, value: string) => {
  switch (field) {
    case "name":
      if (!value.trim()) {
        return "El nombre no puede estar vacío";
      }
      if (value.length > 50) {
        return "El nombre no puede exceder 50 caracteres";
      }
      return "";
    case "email":
      return validateEmail(value) ? "" : "El correo electrónico no es válido";
    case "phone":
      return value.length === 10
        ? ""
        : "El número de teléfono debe tener 10 caracteres";
    case "message":
      return value.length > 300
        ? "El mensaje no puede exceder 300 caracteres"
        : "";
    default:
      return "";
  }
};
