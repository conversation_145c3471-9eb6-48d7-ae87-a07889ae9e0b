import styled from "styled-components";
import Image from "next/image";
import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";

export const Overlay = styled.div`
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
`;

export const CustomModal = styled.div`
  background: #fff;
  border-radius: 5.4px;
  border: 0.9px solid #10265F;
  border-radius: 12px;
  padding: 40px 32px;
  max-width: 925px;
  width: 100%;
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  gap: 24px;
  @media (max-width: 1200px) {
    width: 80%;
  }
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
`;

export const ModalContent = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
  align-items: center;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
`;

export const FormWrapper = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const Input = styled.input`
  padding: 12px 16px;
  border-radius: 8px;
  border: 1.5px solid #10265f;
  font-size: 16px;
  font-family: Poppins, sans-serif;
  width: 100%;

  &::placeholder {
    color: #aaa;
  }

  &:focus {
    border-color: #10265f;
    outline: none;
  }
`;

export const Button = styled(MuiButton)`
  && {
    background: #10265f;
    color: white;
    border-radius: 24px;
    text-transform: none;
    font-family: Poppins;
    font-weight: 500;
    font-size: 16px;
    padding: 12px;
    width: 30%;
    margin: 0 auto;

    &:hover {
      background: #1d3570;
    }

    @media (max-width: 450px) {
      width: 50%;
    }

    @media (max-width: 350px) {
      width: 70%;
    }
  }
`;

export const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #10265f;
  margin-bottom: 8px;
`;

export const ErrorText = styled.p`
  font-size: 12.6px;
  font-style: normal;
  font-weight: 400;
  line-height: 19.8px;
  letter-spacing: 0.09px;
  color: rgb(211, 47, 47);
  margin-top: -14px;
`;

export const PasswordReset = styled.p`
  font-size: 14px;
  color: #7d7d7d;
  text-align: center;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 600;
  &:hover {
    color: #684e79;
  }
`;

export const Disclaimer = styled.p`
  font-size: 12px;
  color: #6d6d6d;
  text-align: center;
  width: 60%;
  margin: 0 auto;
  a,
  button {
    color: #10265f;
    text-decoration: underline;
    background: none;
    border: none;
    font: inherit;
    padding: 0;
    cursor: pointer;
  }

  button:hover {
    text-decoration: underline;
    color: #1d3570;
  }
`;

export const ImageWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 1200px) {
    display: none;
  }
`;

export const StyledImage = styled(Image)`
  width: 180px !important;
  height: auto !important;
`;

export const Footer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
`;

export const WhiteLine = styled.hr`
  border: none;
  background: #6D6D6D;
  height: 1px;
  width: 85%;
  @media (max-width: 768px) {
    width: 90%;
  }
`;

export const FooterText = styled.p`
  font-size: 14px;
  color: #6d6d6d;
`;

export const FooterButton = styled(MuiButton)`
  && {
    border: 1.5px solid #10265f;
    background: #fff;
    color: #10265f;
    font-family: Poppins;
    font-size: 14px;
    padding: 10px 20px;
    border-radius: 24px;
    text-transform: none;

    &:hover {
      background: #f5f5f5;
    }
  }
`;

export const StyledSubmitButton = styled(Button)`
  && {
    display: flex;
    min-width: 188px;
    padding: 16px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    background-color: #10265f;
    color: #ffffff;
    border-radius: 200px;
    font-family: Poppins;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    text-transform: none;
    margin: 0 auto;

    &:hover {
      background-color: #1d3570;
      color: #e2e1e1;
    }
  }
`;