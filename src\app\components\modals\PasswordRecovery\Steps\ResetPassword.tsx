import { useResetPassword } from "@/hooks/PasswordRecovery/useResetPassword";
import { updatePasswordDto } from "@/infrastructure/services/dtos/passwordRecovery";
import ResetPasswordImage from "/public/web/img/reset_password.png";
import { CircularProgress } from "@mui/material";
import { ErrorMessage, Field, Formik } from "formik";
import Image from "next/image";

import { useState } from "react";
import styled from "styled-components";
import * as Yup from "yup";
import { StyledSubmitButton } from "../../login/Login.styles";
import { Eye, EyeClosed } from "lucide-react";

const ResetPassword = ({
  uuid,
  handleClose,
}: {
  uuid: number;
  handleClose: () => void;
}) => {
  const { resetPassword, loading, error } = useResetPassword();
  const [success, setSuccess] = useState(false);
  const [seePassword, setSeePassword] = useState(false);
  const [seeConfirmPassword, setSeeConfirmPassword] = useState(false);

  const handleResetPassword = async (values: updatePasswordDto) => {
    // console.log("values", values);
    try {
      const response = await resetPassword(values);
      if (response) {
        setSuccess(true);
      }

      return response;
    } catch (error) {
      if (!(error instanceof Error)) return;
      console.error(
        "responseee reset password Failed to reset password",
        error
      );
      return error;
    }
  };

  const phoneValidationSchema = Yup.object().shape({
    password: Yup.string()
      .min(8, "La contraseña debe tener al menos 8 caracteres")
      .required("Por favor, ingresa una contraseña")
      .max(20, "La contraseña no puede tener más de ${max} caracteres")
      .matches(
        /^(?=.*[a-zñ])(?=.*[A-ZÑ])(?=.*\d)(?=.*[@$!%*?&\.])[a-zA-ZñÑ\d@$!%*?&\.]{8,20}$/,
        "La contraseña debe tener al menos una letra mayúscula, una letra minúscula, un número y un carácter especial"
      ),
    confirmpassword: Yup.string()
      .oneOf(
        [Yup.ref("password"), undefined],
        "Las contraseñas no coinciden, por favor verifica."
      )
      .required("Las contraseñas no coinciden, por favor verifica."),
  });

  if (loading)
    return (
      <>
        <CircularProgress />
      </>
    );

  return (
    <>
      {success ? (
        <>
          <StyledTitle>Contraseña reestablecida correctamente</StyledTitle>
          <Image
            src={ResetPasswordImage}// Cambia la ruta si tu imagen está en otra ubicación
            alt="Éxito"
            width={180}
            height={180}
            style={{ display: "block", margin: "24px auto" }}
            priority
          />
          <StyledSubmitButton onClick={handleClose}>
            ¡Listo! Continuar
          </StyledSubmitButton>
        </>
      ) : (
        <MainContainer>
          <FlexContainer>
            <FormContainer>
              <Formik
                initialValues={{ password: "", confirmpassword: "" }}
                validationSchema={phoneValidationSchema}
                validateOnMount
                onSubmit={(values) => {
                  handleResetPassword({
                    uuid: uuid,
                    newPassword: values.password,
                  });
                }}
              >
                {({ errors, touched, isValid, handleSubmit }) => (
                  <>
                    <StyledTitle>Restablece tu contraseña</StyledTitle>
                    <StyledForm onSubmit={handleSubmit}>
                      <InputWithIcon>
                        <Field
                          name="password"
                          type={seePassword ? "text" : "password"}
                          placeholder="Nueva contraseña"
                        />
                        {seePassword ? (
                          <Eye onClick={() => setSeePassword(!seePassword)} />
                        ) : (
                          <EyeClosed
                            onClick={() => setSeePassword(!seePassword)}
                          />
                        )}
                      </InputWithIcon>
                      {errors.password && touched.password && (
                        <ErrorText>
                          <ErrorMessage name="password" />
                        </ErrorText>
                      )}
                      <InputWithIcon>
                        <Field
                          name="confirmpassword"
                          type={seeConfirmPassword ? "text" : "password"}
                          placeholder="Repite tu Nueva contraseña"
                        />
                        {seeConfirmPassword ? (
                          <Eye
                            onClick={() =>
                              setSeeConfirmPassword(!seeConfirmPassword)
                            }
                          />
                        ) : (
                          <EyeClosed
                            onClick={() =>
                              setSeeConfirmPassword(!seeConfirmPassword)
                            }
                          />
                        )}
                      </InputWithIcon>
                      {errors.confirmpassword && touched.confirmpassword && (
                        <ErrorText>
                          <ErrorMessage name="confirmpassword" />
                        </ErrorText>
                      )}
                      {error && (
                        <ErrorText>
                          Error al cambiar contraseña. Intentalo nuevamente
                        </ErrorText>
                      )}
                      <Sendbutton
                        type="submit"
                        disabled={!isValid}
                        style={{ width: "100%" }}
                      >
                        Actualizar
                      </Sendbutton>
                    </StyledForm>
                  </>
                )}
              </Formik>
            </FormContainer>
            <ImageContainer>
              <StyledImage
                src="/web/img/cardsProducts/wikiHackeo.png"
                alt="Imagen de recuperación"
                width={300}
                height={300}
              />
            </ImageContainer>
          </FlexContainer>
          <StyledText>
            Elige una nueva contraseña con mínimo 8 caracteres, que incluya al
            menos una mayúscula, un número y un carácter especial.
          </StyledText>
        </MainContainer>
      )}
    </>
  );
};

export default ResetPassword;

const StyledForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  align-items: center;
  justify-content: center;
  max-width: 700px;
  input {
    display: flex;
    padding: 20px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 20px;
    border: 1.5px solid var(--Primary-Navy, #10265f);
    background: var(--Blanco, #fff);
  }
`;

const ErrorText = styled.div`
  color: red;
  font-size: 14px;
  font-family: Poppins;
`;

const StyledButton = styled.button`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  width: 256px;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  text-transform: capitalize;
  color: #ffffff;
  font-weight: 400;
  font-size: 16px;
`;

const Sendbutton = styled(StyledButton)`
  background-color: #10265f;
  &:disabled {
    background: #ebebeb;
    cursor: not-allowed;
    color: #828282;
  }
`;

const StyledTitle = styled.p`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
`;

const MainContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: 32px;
  @media screen and (max-width: 732px) {
    padding: 16px;
  }
`;

const FlexContainer = styled.div`
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 100%;
  @media (max-width: 732px) {
    flex-direction: column;
  }
`;

const FormContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 46px;
  @media (max-width: 732px) {
    gap: 32px;
  }
`;

const ImageContainer = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  @media (max-width: 732px) {
    order: -1;
    margin-bottom: 16px;
  }
`;

const StyledText = styled.p`
  color: var(--Gris-Obscuro, #333);
  margin-top: 32px;
  /* Wiki/Common/Legal */
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
`;
const StyledImage = styled(Image)`
  width: 300px;
  height: 300px;
  @media (max-width: 732px) {
    width: 120px;
    height: auto;
  }
`;

const InputWithIcon = styled.div`
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;

  svg {
    color: #10265f;
  }

  input {
    width: 100%;
  }
`;
