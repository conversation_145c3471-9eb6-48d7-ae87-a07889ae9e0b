"use client";

import {
  createContext,
  useContext,
  useState,
  useRef,
  ReactNode,
  useEffect,
} from "react";
import useLoginModal from "./components/modals/login/useLoginModal";

// Tipo del contexto con estado loading
interface LoginContextType {
  logged: boolean;
  setLogged: (newValue: boolean) => void;
  loading: boolean;
  isModalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  user: { email: string; firstName: string; nickname: string } | null;
  logout: () => void;
  setUser: (
    user: {
      email: string;
      firstName: string;
      nickname: string;
    } | null
  ) => void;
  nextAction: (() => void) | null;
  setNextAction: (action: (() => void) | null) => void;
}

// Crear contexto
const LoginContext = createContext<LoginContextType | undefined>(undefined);

// Hook para acceder al contexto
export const useIsLogged = () => {
  const context = useContext(LoginContext);
  if (!context) {
    throw new Error("useIsLogged debe usarse dentro de LoginProvider");
  }
  return context;
};

// Proveedor, inicialización perezosa
export const LoginProvider = ({ children }: { children: ReactNode }) => {
  const [logged, setLogged] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [user, setUser] = useState<{
    email: string;
    firstName: string;
    nickname: string;
  } | null>(null);
  const nextActionRef = useRef<(() => void) | null>(null);

  const logout = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");
    setUser(null);
    setLogged(false);
  };

  useEffect(() => {
    // Se accede a localStorage solo en el cliente
    const token = localStorage.getItem("accessToken");
    const userData = localStorage.getItem("user");
    let parsedUserData = null;
    if (userData) {
      try {
        parsedUserData = JSON.parse(userData);
      } catch (error) {
        console.error("Error parsing user data from localStorage:", error);
      }
    }
    if (token && token !== "undefined") {
      setLogged(true);
      setUser(parsedUserData);
    }
    setLoading(false);
  }, []);

  const { isModalOpen, openModal, closeModal } = useLoginModal();

  useEffect(() => {
    // Limpiar acción pendiente si se cierra modal sin ejecutar nextAction
    if (!isModalOpen && nextActionRef.current) {
      nextActionRef.current = null;
    }
  }, [isModalOpen]);

  // useEffect(() => {
  //   console.log("nextAction", nextAction);
  // }, [nextAction]);

  return (
    <LoginContext.Provider
      value={{
        logged,
        setLogged,
        loading,
        isModalOpen,
        openModal,
        closeModal,
        user,
        setUser,
        logout,
        nextAction: nextActionRef.current,
        setNextAction: (action) => {
          nextActionRef.current = action;
        },
      }}
    >
      {children}
    </LoginContext.Provider>
  );
};

export const useLoginModalContext = (): LoginContextType => {
  const context = useContext(LoginContext);
  if (!context) {
    throw new Error(
      "useLoginModalContext must be used within a LoginModalProvider"
    );
  }
  return context;
};
