// Objetos de productos.
export interface ProductsCards {
  dataCards: {
    id: string;
    name: string;
    description: string;
    descriptionMobile: string;
    price?: string;
    image: string;
    imageHover: string;
    path: string;
    color: string;
    boxShadow?: string;
  }[];
}
export interface product {
  id: string;
  name: string;
  description: string;
  descriptionMobile: string;
  price?: string;
  image: string;
  imageHover: string;
  path: string;
  color: string;
  boxShadow?: string;
}
/* Interfaz de lista de productos en la sección de navBar */
/**
 * Interfaz de lista de productos.
 * @property {Array<{id: number, title: string}>} products - Lista de productos con ID y título.
 */
export interface ListProdcuts {
  products: {
    id: number;
    title: string;
    path: string;
  }[];
}
