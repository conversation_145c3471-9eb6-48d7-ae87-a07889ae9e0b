export type DocumentStatus = 'Aprobado' | 'Rechazado' | 'Pendiente';

export interface Document {
  id: string;
  producto: string;
  categoria: string;
  documento: string;
  estado: DocumentStatus;
  motivoRechazo?: string;
  fechaCarga?: string;
  archivo?: File | string;
  url?: string;
}

export interface DocumentsByProduct {
  [key: string]: {
    generales: Document[];
    coberturas: {
      [cobertura: string]: Document[];
    };
  };
}

export interface ProductDocumentValidation {
  formato: string[];
  maxSize: number; // en MB
}

// Configuración de documentos por producto según la tabla de la HU
export const PRODUCT_DOCUMENTS_CONFIG = {
  'Wiki Gadget': {
    generales: [
      'Identificación Oficial',
      'Comprobante de Domicilio',
      'Estado de cuenta',
      'Ticket de compra del gadget'
    ],
    coberturas: {
      'Robo con violencia': ['Acta del MP'],
      'Daño accidental': ['Ticket/factura de reparación']
    }
  },
  'Wiki Rodada': {
    generales: [
      'Factura del vehículo',
      'Identificación Oficial',
      'Comprobante de Domicilio',
      'Estado de cuenta'
    ],
    coberturas: {
      'Daño accidental': ['Ticket/factura de reparación'],
      'Robo total': ['Acta del MP']
    }
  },
  'Wiki Phishing': {
    generales: [
      'Identificación Oficial',
      'Comprobante de Domicilio',
      'Estado de cuenta'
    ],
    coberturas: {
      'Phishing': ['Estado de cuenta', 'Folio bancario'],
      'Robo de identidad': ['Tickets/facturas notariales'],
      'Robo en cajero': ['Ticket de retiro']
    }
  },
  'Wiki Asalto': {
    generales: [
      'Identificación Oficial',
      'Comprobante de Domicilio',
      'Estado de cuenta',
      'Acta del MP'
    ],
    coberturas: {
      'Dinero en efectivo': ['Ticket de retiro'],
      'Equipo portátil': ['Factura del equipo'],
      'Llaves/Documentos personales': ['Ticket de reposición']
    }
  }
};

export const DOCUMENT_VALIDATION: ProductDocumentValidation = {
  formato: ['PDF', 'JPG', 'JPEG', 'PNG'],
  maxSize: 5 // 5MB
};
