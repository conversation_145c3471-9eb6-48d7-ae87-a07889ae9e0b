import { useMediaQuery } from "@mui/material";
import { useClaimForm } from './hooks/useClaimForm';
import FormField from '../components/forms/FormField';
import DocumentField from './components/DocumentField';

interface BeforeEndFormProps {
  onValidateForm?: (isValid: boolean) => void;
}

const BeforeEndForm = ({ onValidateForm }: BeforeEndFormProps) => {
  const isSmallScreen = useMediaQuery("(max-width: 700px)");
  
  const {
    formData,
    errors,
    handleInputChange,
    handleFileUpload,
    removeFile,
    validateBeforeEnd,
    // isBeforeEndValid
  } = useClaimForm({
    onValidateForm: (section, isValid) => {
      if (section === 'beforeEnd' && onValidateForm) {
        onValidateForm(isValid);
      }
    }
  });

  // Wrapper functions para compatibilidad con DocumentField
  const handleFileUploadWrapper = (fieldName: string, file: File | null) => {
    handleFileUpload(fieldName as keyof typeof formData, file);
  };

  const removeFileWrapper = (fieldName: string) => {
    removeFile(fieldName as keyof typeof formData);
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateBeforeEnd()) {
      console.log('Formulario válido', formData);
    } else {
      const firstErrorField = Object.keys(errors).find(key => errors[key]);
      if (firstErrorField) {
        const element = document.querySelector(`[data-field="${firstErrorField}"]`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <strong style={{ fontSize: '1.7rem' }}>Antes de finalizar, un poco sobre tí</strong>
      <p style={{ fontSize: '1.2rem', fontWeight: 500 }}>Necesitamos la siguiente información para concluir con el proceso de reclamación</p>

      <div style={{ display: 'flex', gap: '1rem', marginTop: '3rem', justifyContent: 'space-between', flexDirection: 'column'}}>
        <FormField
          label="Ocupación, profesión, actividad o giro del negocio a la que te dedicas:"
          error={errors.ocupacion}
          errorMessage="Este campo es requerido"
        >
          <input 
            placeholder="Ocupación" 
            type="text" 
            value={formData.ocupacion}
            onChange={(e) => handleInputChange('ocupacion', e.target.value)}
            style={{ 
              border: `3px solid ${errors.ocupacion ? '#ff0000' : '#51519b'}`, 
              borderRadius: '15px', 
              padding: '8px' 
            }} 
          />
        </FormField>
        <FormField
          label="Nombre de la empresa en que labora:"
          error={errors.empresa}
          errorMessage="Este campo es requerido"
        >
          <input 
            placeholder="nombre" 
            type="text" 
            value={formData.empresa}
            onChange={(e) => handleInputChange('empresa', e.target.value)}
            style={{ 
              border: `3px solid ${errors.empresa ? '#ff0000' : '#51519b'}`, 
              borderRadius: '15px', 
              padding: '8px' 
            }} 
          />
        </FormField>
        <FormField
          label="Número de serie del Certificado Digital para la firma Electrónica Avanzada (En caso de contar con EFIRMA)"
        >
          <input 
            placeholder="Número de serie" 
            type="text" 
            value={formData.certificadoDigital}
            onChange={(e) => handleInputChange('certificadoDigital', e.target.value)}
            style={{ border: '3px solid #51519b', borderRadius: '15px', padding: '8px' }} 
          />
        </FormField>
      </div>
      <div style={{ display: 'flex', gap: isSmallScreen ? '1rem' : '5rem', marginTop: '1rem', justifyContent: 'space-between', flexDirection: isSmallScreen ? 'column' : 'row' }}>
        <FormField
          label="CURP"
          error={errors.curp}
          errorMessage="Este campo es requerido"
        >
          <input 
            type="text" 
            value={formData.curp}
            onChange={(e) => handleInputChange('curp', e.target.value)}
            style={{ 
              border: `3px solid ${errors.curp ? '#ff0000' : '#51519b'}`, 
              borderRadius: '15px', 
              padding: '8px' 
            }} 
          />
        </FormField>
        <FormField
          label="RFC"
          error={errors.rfc}
          errorMessage="Este campo es requerido"
        >
          <input 
            type="text" 
            value={formData.rfc}
            onChange={(e) => handleInputChange('rfc', e.target.value)}
            style={{ 
              border: `3px solid ${errors.rfc ? '#ff0000' : '#51519b'}`, 
              borderRadius: '15px', 
              padding: '8px' 
            }} 
          />
        </FormField>
        <FormField
          label="CLABE de cuenta bancaria"
          error={errors.clabe}
          errorMessage="Este campo es requerido"
        >
          <input 
            type="text" 
            value={formData.clabe}
            onChange={(e) => handleInputChange('clabe', e.target.value)}
            style={{ 
              border: `3px solid ${errors.clabe ? '#ff0000' : '#51519b'}`, 
              borderRadius: '15px', 
              padding: '8px' 
            }} 
          />
        </FormField>
      </div>

      {/* Nuevos campos agregados */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', marginTop: '2rem' }}>
        
        {/* Género */}
        {/* <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', fontWeight: 600 }} data-field="genero">
          <label style={{ color: errors.genero ? '#ff0000' : 'inherit' }}>Género</label>
          <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="genero" 
                value="femenino"
                checked={formData.genero === 'femenino'}
                onChange={(e) => handleInputChange('genero', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              Femenino
            </label>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="genero" 
                value="masculino"
                checked={formData.genero === 'masculino'}
                onChange={(e) => handleInputChange('genero', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              Masculino
            </label>
          </div>
          {errors.genero && <span style={{ color: '#ff0000', fontSize: '0.8rem' }}>Selecciona una opción</span>}
        </div> */}

        {/* Funcionario de gobierno */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', fontWeight: 600 }} data-field="funcionarioGobierno">
          <label style={{ color: errors.funcionarioGobierno ? '#ff0000' : 'inherit' }}>¿Ha sido durante el último año, funcionario de gobierno (nacional o extranjero) de alta jerarquía o miembro importante de un partido político?</label>
          <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="funcionarioGobierno" 
                value="si"
                checked={formData.funcionarioGobierno === 'si'}
                onChange={(e) => handleInputChange('funcionarioGobierno', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              Sí
            </label>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="funcionarioGobierno" 
                value="no"
                checked={formData.funcionarioGobierno === 'no'}
                onChange={(e) => handleInputChange('funcionarioGobierno', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              No
            </label>
          </div>
          {errors.funcionarioGobierno && <span style={{ color: '#ff0000', fontSize: '0.8rem' }}>Selecciona una opción</span>}
        </div>

        {/* Cónyuge */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', fontWeight: 600 }} data-field="conyuge">
          <label style={{ color: errors.conyuge ? '#ff0000' : 'inherit' }}>¿Es o ha sido cónyuge o concubina, padre o dependiente económico de alguno de los mencionados en la pregunta anterior?</label>
          <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="conyuge" 
                value="si"
                checked={formData.conyuge === 'si'}
                onChange={(e) => handleInputChange('conyuge', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              Sí
            </label>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="conyuge" 
                value="no"
                checked={formData.conyuge === 'no'}
                onChange={(e) => handleInputChange('conyuge', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              No
            </label>
          </div>
          {errors.conyuge && <span style={{ color: '#ff0000', fontSize: '0.8rem' }}>Selecciona una opción</span>}
        </div>

        {/* Negocio propio */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', fontWeight: 600 }} data-field="negocioPropio">
          <label style={{ color: errors.negocioPropio ? '#ff0000' : 'inherit' }}>En caso de haber contestado afirmativamente alguna de las preguntas anteriores: ¿Tiene un negocio propio o es accionista de alguna sociedad?</label>
          <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="negocioPropio" 
                value="si"
                checked={formData.negocioPropio === 'si'}
                onChange={(e) => handleInputChange('negocioPropio', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              Sí
            </label>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 400, cursor: 'pointer' }}>
              <input 
                type="radio" 
                name="negocioPropio" 
                value="no"
                checked={formData.negocioPropio === 'no'}
                onChange={(e) => handleInputChange('negocioPropio', e.target.value)}
                style={{ margin: 0, transform: 'scale(1.2)' }}
              />
              No
            </label>
          </div>
          {errors.negocioPropio && <span style={{ color: '#ff0000', fontSize: '0.8rem' }}>Selecciona una opción</span>}
        </div>

        {/* Actúa a nombre propio - Checkbox */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', fontWeight: 600 }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '12px', fontWeight: 600, cursor: 'pointer' }}>
            <input 
              type="checkbox" 
              checked={formData.actaNombrePropio}
              onChange={(e) => handleInputChange('actaNombrePropio', e.target.checked)}
              style={{ margin: 0, transform: 'scale(1.3)' }}
            />
            ¿Actúa a nombre y por cuenta propia?
          </label>
        </div>

      </div>

      {/* Documentos requeridos */}
      <div style={{ marginTop: '3rem' }}>
        <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1.5rem' }}>
          Documentos requeridos
        </h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
          
          <DocumentField
            label="Identificación Oficial"
            description="(INE, Pasaporte, Cédula Profesional)"
            fieldName="identificacionOficial"
            file={formData.identificacionOficial}
            onFileUpload={handleFileUploadWrapper}
            onRemoveFile={removeFileWrapper}
            hasError={errors.identificacionOficial}
            errorMessage="Este documento es requerido"
          />

          <DocumentField
            label="Comprobante de domicilio"
            description="(no mayor a 3 meses)"
            fieldName="comprobanteDomicilio"
            file={formData.comprobanteDomicilio}
            onFileUpload={handleFileUploadWrapper}
            onRemoveFile={removeFileWrapper}
            hasError={errors.comprobanteDomicilio}
            errorMessage="Este documento es requerido"
          />

          <DocumentField
            label="Constancia de CURP"
            fieldName="constanciaCurp"
            file={formData.constanciaCurp}
            onFileUpload={handleFileUploadWrapper}
            onRemoveFile={removeFileWrapper}
            hasError={errors.constanciaCurp}
            errorMessage="Este documento es requerido"
          />

          <DocumentField
            label="Constancia de situación fiscal"
            description="(En caso de contar con ella)"
            fieldName="constanciaSituacionFiscal"
            file={formData.constanciaSituacionFiscal}
            onFileUpload={handleFileUploadWrapper}
            onRemoveFile={removeFileWrapper}
            required={false}
          />

          <DocumentField
            label="Carátula bancaria"
            fieldName="caratulaBancaria"
            file={formData.caratulaBancaria}
            onFileUpload={handleFileUploadWrapper}
            onRemoveFile={removeFileWrapper}
            hasError={errors.caratulaBancaria}
            errorMessage="Este documento es requerido"
          />
          
        </div>
      </div>
    </form>
  )
}

export default BeforeEndForm;