import { useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Registrar ScrollTrigger
gsap.registerPlugin(ScrollTrigger);

interface UseScrollAnimationOptions {
  trigger?: string; // Selector del elemento que activa el trigger
  start?: string; // Posición inicial (por defecto: "top 80%")
  end?: string; // Posición final (por defecto: "bottom 20%")
  toggleActions?: string; // Acciones de ScrollTrigger (por defecto: "play none none none")
  animationProps?: gsap.TweenVars; // Propiedades de animación de GSAP
  once?: boolean; // Disparar la animación solo una vez
}

export const useScrollAnimation = (
  selector: string,
  options?: UseScrollAnimationOptions
) => {
  useEffect(() => {
    const elements = document.querySelectorAll(selector);

    if (elements.length === 0) return;

    elements.forEach((el) => {
      gsap.fromTo(
        el,
        {
          opacity: 0,
          y: 50, // Posición inicial
          ...(options?.animationProps?.from || {}),
        },
        {
          opacity: 1,
          y: 0, // Posición final
          duration: 1,
          ease: "power2.out",
          ...(options?.animationProps?.to || {}),
          scrollTrigger: {
            trigger: options?.trigger || el,
            start: options?.start || "top 80%",
            end: options?.end || "bottom 20%",
            toggleActions: options?.toggleActions || "play none none none",
            once: options?.once || false,
          },
        }
      );
    });

    // Limpiar triggers al desmontar
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, [selector, options]);
};
