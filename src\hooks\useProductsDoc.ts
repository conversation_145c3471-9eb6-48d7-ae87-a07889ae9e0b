import { useState, useCallback } from "react";
import { ProductsDocService } from "@/infrastructure/services/productsDocService";
import { useSnackbar } from "@/context/SnackbarContext";

export function useProductsDoc() {
  const [loading, setLoading] = useState(false);
  const { showError, showSuccess, showLoading, hide } = useSnackbar();

  /**
   * @param productId ID del producto
   * @param productTitle Título del producto, usado para nombrar el archivo descargado
   */
  const downloadDocument = useCallback(
    async (productId: string | number, productTitle: string) => {
      try {
        setLoading(true);
        showLoading("Descargando documento...");

        const service = new ProductsDocService();

        // 1) Obtener URL pública
        const documentUrl = await service.getPublicDocumentUrl(productId);

        // 2) Crear enlace de descarga directa (evita problemas de CORS)
        const link = document.createElement("a");
        link.href = documentUrl;
        link.download = `detalles-poliza-${productTitle}.pdf`;
        link.target = "_blank";
        link.rel = "noopener noreferrer";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        hide();
        showSuccess("Documento descargado correctamente");
      } catch (error) {
        hide();
        console.error("Error al descargar el documento:", error);
        showError("Error al descargar el documento. Inténtalo de nuevo.");
      } finally {
        setLoading(false);
      }
    },
    [showLoading, hide, showSuccess, showError]
  );

  return { downloadDocument, loading };
}
