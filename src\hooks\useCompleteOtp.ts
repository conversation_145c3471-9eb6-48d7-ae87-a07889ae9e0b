import { AuthService } from "@/infrastructure/services/authService";
import { CompleteOtpDto } from "@/infrastructure/services/dtos/register-dto";
import { useState } from "react";

export const useCompleteOtp = () => {
  const authService = new AuthService();
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);

  const completeOtp = async (params: CompleteOtpDto) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await authService.completeOtp(params);
      return response;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Error al completar OTP');
      setError(err);
      console.error("Failed to complete OTP:", error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { completeOtp, error, loading, setError };
};
