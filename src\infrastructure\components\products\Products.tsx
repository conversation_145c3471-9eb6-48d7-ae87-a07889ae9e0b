"use client";
import React from "react";
import { Box, Typography } from "@mui/material";
// import { dataProducts } from "@/infrastructure/data/products.data";
import { product } from "@/infrastructure/models/products.model";
import styles from "../../styles/products.module.css";
import { ProductCard } from "./ProductCard";
import styled from "styled-components";
// import { fetchAndAdaptProducts } from "@/infrastructure/data/products.data";
import { useProducts } from "@/app/ProductsContextProvider";

export const Products: React.FC = () => {
  const { products } = useProducts();

  const data = products;

  return (
    <Box className={`${styles.contentCards} scroll-animate`}>
      <Typography className={styles.secctionTitleProduct}>
        Nuestros Productos
      </Typography>

      <Typography className={styles.secctionSubtitleProduct}>
        Con nuestros seguros, tienes cobertura 24/7 contra robo a tus
        pertenecias, robo de identidad y más.
      </Typography>

      {/* Contenedor con scroll horizontal solo en móvil */}

      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <ProductsContainer>
          {data &&
            data.dataCards?.map((product: product) => (
              <ProductCard product={product} key={product.name} />
            ))}
        </ProductsContainer>
      </div>
    </Box>
  );
};

const ProductsContainer = styled.section`
  grid-template-columns: repeat(4, 1fr);
  display: grid;
  justify-items: center;

  @media (max-width: 1439px) {
    margin: 0 auto;
    grid-template-columns: repeat(2, 1fr);
    align-items: center;
  }

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
`;
