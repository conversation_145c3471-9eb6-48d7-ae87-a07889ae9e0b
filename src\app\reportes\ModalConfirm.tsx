"use client";
import { Modal } from "@mui/material";
import styled from "styled-components";
import imgSrc from "/public/web/img/Confirm01.png";
import Image from "next/image";
import { Close } from "@mui/icons-material";
import { FunctionComponent } from "react";

type ModalConfirmProps = {
  onClose: () => void;
};

const ModalConfirm: FunctionComponent<ModalConfirmProps> = ({ onClose }) => {
  return (
    <>
      <Modal open>
        <Layout>
          <ModalContainer>
            <Close
              style={{
                position: "absolute",
                top: "20px",
                right: "20px",
                cursor: "pointer",
              }}
              onClick={onClose}
            />
            <Image src={imgSrc} alt="" width={218} height={210} />
            <StyledTitle>
              Para que el proceso sea más rápido, ten a la mano los siguientes
              documentos antes de comenzar tu reporte.
            </StyledTitle>
            <StyledUl>
              <li>
                Acta de Denuncia al MP (detallando lo que perdiste y su valor)
              </li>
              <li>Identificación Oficial</li>
              <li>Comprobante de Domicilio (no mayor a 3 meses)</li>
              <li>Carátula de Estado de Cuenta Bancario</li>
            </StyledUl>
            <StyledSubtitle>
              Si tu reporte incluye alguna de las coberturas enlistadas a
              continuación, necesitarás presentar los siguientes documentos
              adicionales:
            </StyledSubtitle>
            <StyledUl>
              <li>
                <span>Reposición de Gastos por Documentos:</span> Comprobante de
                Gastos por Reemplazo de Documentos
              </li>
              <li>
                <span> Duplicado de Llaves:</span> Comprobante de Gastos por
                Duplicado de Llaves
              </li>
              <li>
                <span> Equipo Portátil:</span> Factura o Ticket de Compra del
                Dispositivo Reportado
              </li>
              <li>
                <span> Efectivo: </span>Comprobante de Retiro de Dinero (no
                mayor a 7 días)
              </li>
            </StyledUl>
            <StyledButton onClick={onClose}>De acuerdo</StyledButton>
          </ModalContainer>
        </Layout>
      </Modal>
    </>
  );
};

export default ModalConfirm;

const ModalContainer = styled.div`
  display: inline-flex;
  padding: 50px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
  border-radius: 6px;
  background: var(--Background-White, #fff);
  max-width: 824px;
  width: 100%;
  height: auto;
  position: relative;
  @media (max-width: 1023px) {
    padding: 20px;
  }
`;
const Layout = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 1rem;
`;

const StyledTitle = styled.p`
  color: var(--Text-Black, #000);

  /* Wiki/Common/Title */
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  width: 100%;
  text-align: left;
`;

const StyledUl = styled.ul`
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  padding: 0 20px;
  width: 100%;
  text-align: left;
  li {
    color: var(--Text-Black, #000);

    /* Wiki/Common/Body */
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    span {
      font-weight: 700;
    }
  }
`;

const StyledSubtitle = styled.h2`
  color: var(--Text-Black, #000);
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  width: 100%;
  text-align: left;
`;

const StyledButton = styled.button`
  color: var(--Text-Navy, #10265f);
  text-align: right;
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  cursor: pointer;
  margin: 0 0 0 auto;
  border: none;
  background: none;
  padding: 0;
  width: fit-content;
`;
