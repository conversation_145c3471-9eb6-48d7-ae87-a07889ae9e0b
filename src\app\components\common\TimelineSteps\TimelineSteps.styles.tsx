import styled from 'styled-components';

export const Container = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
  margin: 20px 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
`;

export const StepContainer = styled.div`
  display: flex;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

export const StepWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  min-width: 120px;
`;

export const ConnectorLine = styled.div<{ $isEnabled?: boolean }>`
  width: 100px;
  height: 5.646px;
  border-radius: 3px;
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
  
  ${({ $isEnabled }) =>
    $isEnabled
      ? `
    background-color: #AF8CC0;
  `
      : `
    background-color: #F1DEFA;
  `}
  
  @media (max-width: 768px) {
    width: 2px;
    height: 30px;
    margin: 10px 0;
  }
`;

export const StepLabel = styled.span<{ $isDisabled?: boolean }>`
  margin-top: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: ${({ $isDisabled }) => ($isDisabled ? '#9CA3AF' : '#374151')};
  transition: color 0.2s ease-in-out;
  max-width: 100px;
  word-wrap: break-word;
`;
