import styled from "styled-components";

export const Verificacion2FactoresValiChild = styled.div`
  position: absolute;
  top: 0px;
  left: 0px;
  background-color: rgba(87, 90, 111, 0.5);
  width: 1440px;
  height: 1080px;
`;

export const HolaSoyTikiSolo = styled.div`
  width: 1248px;
  position: relative;
  font-size: 22px;
  font-weight: 600;
  color: #5959a3;
  display: none;
`;

export const HolaSoyTikiSolo1 = styled.div`
  align-self: stretch;
  position: relative;
`;

export const Text = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
`;

export const Icons = styled.img`
  width: 48px;
  position: relative;
  height: 48px;
`;

export const IconSms = styled.div`
  border-radius: 56.6px;
  background-color: #fff;
  border: 8px solid #f3fafd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
`;

export const IconSmsWrapper = styled.div`
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
`;

export const Enter4Digits = styled.div`
  position: relative;
`;

export const Digit = styled.input`
  border: none;
  outline: none;
  font-weight: 500;
  font-family: Poppins;
  font-size: 24px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  color: #000;
  width: 100%;
  max-width: 48px;
  min-width: 41px;
  height: 52px;
  text-align: center;
`;

export const Grupo16070 = styled.div`
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16px;
  min-height: 52px;
`;

export const Enter4DigitsCodeParent = styled.div`
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
`;

export const ButtonText = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: capitalize;
  color: #af8cc0;
  font-size: 16px;
  font-family: Poppins;
  font-weight: 400;
`;

export const B = styled.span`
  position: relative;
  color: #828282;
`;

export const ButtonParent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

export const Icons3 = styled.img`
  width: 21.3px;
  position: relative;
  max-height: 100%;
  object-fit: cover;
  display: none;
`;

export const Cotizar1 = styled.div`
  width: 76px;
  position: relative;
  font-size: 16px;
  font-family: Poppins;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  flex-shrink: 0;
`;

export const Icons4 = styled.img`
  width: 21px;
  position: relative;
  max-height: 100%;
  object-fit: cover;
  display: none;
`;

export const Button1 = styled.button`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  background-color: #10265f;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  color: #fff;
  text-transform: capitalize;
  font-family: Poppins;
`;

export const Cotizar2 = styled.div`
  flex: 1;
  position: relative;
  font-size: 16px;
  font-family: Poppins;
  color: #10265f;
  text-align: center;
`;

export const Button2 = styled.button`
  cursor: pointer;
  border: 2px solid #10265f;
  padding: 16px 24px;
  background-color: #fff;
  border-radius: 200px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 188px;
  color: #10265f;
  text-transform: capitalize;
  font-family: Poppins;
`;

export const Buttons = styled.div`
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  align-content: center;
  gap: 8px 24px;
`;

export const FrameParent = styled.div`
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  color: #af8cc0;
`;

export const CardDigitsContainer = styled.div`
  display: flex;
  padding: 24px 32px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  border-radius: 24px;
  width: 100%;
  height: auto;
`;

export const CardDigits = styled.div`
  max-width: 612px;
  width: 100%;
  border-radius: 24px;
  background-color: #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 32px;
  box-sizing: border-box;
  gap: 24px;
  color: #7d7d7d;
`;

export const SectionPlanes = styled.div`
  position: relative;
  border-radius: 18px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 60px 60px;
  gap: 64px;
  text-align: center;
  font-size: 16px;
  color: #828282;
  font-family: Poppins;
  width: 100%;
  max-width: 932px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
`;

export const Verificacion2FactoresVali = styled.div`
  width: 100%;
  position: relative;
  background-color: #fff;
  height: 1024px;
  overflow: hidden;
`;

export const SectionOtpCode = styled.section`
  display: flex;
  width: 100%;
  padding: 0px 24px;
  justify-content: center;
  align-items: center;
  min-height: 90vh;
`;

export const ContainerInputs = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 8px;
`;

export const ErrorDigit = styled.div`
  border: none;
  outline: none;
  font-weight: 500;
  font-family: Poppins;
  font-size: 24px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  color: #000;
  width: 100%;
  max-width: 48px;
  min-width: 41px;
  height: 52px;
  text-align: center;
  border: 1px solid red;
`;

export const MessageResent = styled.div`
  display: flex;
  height: 34px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  background: rgba(16, 38, 95, 0.08);

  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 120%; /* 16.8px */
`;
