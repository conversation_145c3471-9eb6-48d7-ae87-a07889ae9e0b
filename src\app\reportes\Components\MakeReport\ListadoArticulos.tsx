import {
  FileUploader,
  FilterContainer,
  StyledInput,
} from "@/Styles/General.styles";
import { Grid2 } from "@mui/material";
import styled from "styled-components";

const MockData: {
  id: number;
  name: string;
  max: number | null;
  ticket: string;
  value: string;
  description: string;
  ticketNote?: string;
}[] = [
  {
    id: 1,
    name: "Phishing",
    max: null,
    ticket: "",
    value: "",
    description:
      "¿Cuánto dinero fue usado sin tu voluntad? (si no sabes, pon 0)",
    ticketNote: "Comprobante de Retiro de Dinero (no mayor a 7 días)",
  },
  {
    id: 2,
    name: "Gastos notariales o judiciales",
    max: 5,
    ticket: "Ticket de compra",
    value: "",
    description:
      "¿Cuánto dinero fue usado sin tu voluntad? (si no sabes, pon 0)",
  },
];

const ListadoArticulos = () => {
  return (
    <>
      <Grid2 container spacing={2} sx={{ marginTop: "20px", width: "100%" }}>
        <Grid2 container size={12}>
          <Grid2 size={5}>
            <ColumnTitles>Artículos</ColumnTitles>
          </Grid2>
          <Grid2 size={1}>{/*Spacer*/}</Grid2>
          <Grid2 size={3}>
            <ColumnTitles>Ticket de compra</ColumnTitles>
          </Grid2>
          <Grid2 size={3}>
            <ColumnTitles>Valor Aproximado</ColumnTitles>
          </Grid2>
        </Grid2>
        {/* ------------------------------------------------------- */}
        {MockData &&
          MockData.map((item) => (
            <Grid2 container size={12} key={item.id}>
              <Grid2
                size={5}
                sx={{
                  height: "auto",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "flex-end",
                }}
              >
                <FilterContainer name={item.name}>
                  <StyledInput
                    placeholder={item.description}
                    id={item.name.toLowerCase().replace(/\s/g, "")}
                    name="name"
                  />
                </FilterContainer>
              </Grid2>
              <Grid2
                size={1}
                sx={{
                  height: "auto",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "flex-end",
                }}
              >
                {/*Spacer*/}
              </Grid2>
              <Grid2
                size={3}
                sx={{
                  height: "auto",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "flex-end",
                }}
              >
                <ColumnTitles>
                  <FileUploader
                    onFileSelect={() => console.log("file here")}
                    accept=".pdf,.jpg"
                  />
                </ColumnTitles>
              </Grid2>
              <Grid2
                size={3}
                sx={{
                  height: "auto",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "flex-end",
                }}
              >
                <ColumnTitles>
                  <FilterContainer>
                    <StyledInput
                      placeholder={"Valor aproximado"}
                      id={`projectName-${item.id}`}
                      name="name"
                    />
                  </FilterContainer>
                </ColumnTitles>
              </Grid2>
            </Grid2>
          ))}
      </Grid2>
    </>
  );
};

export default ListadoArticulos;

const ColumnTitles = styled.h5`
  color: var(--Gris-Obscuro, #333);
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
`;
