"use client";
import type { NextPage } from "next";
import { Box, TextField, useMediaQuery } from "@mui/material";
import styles from "../styles.module.css";

/**
 * Props que recibe el componente InputForm
 * @property {string} label - Etiqueta que describe el campo de entrada.
 * @property {string} placeholder - Texto de marcador de posición dentro del campo.
 * @property {string} value - Valor actual del campo.
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} onChange - Función que se ejecuta al cambiar el valor del campo.
 * @property {() => void} [onBlur] - Función opcional que se ejecuta al perder el foco del campo.
 * @property {boolean} [error] - Indica si el campo tiene un error.
 * @property {string} [helperText] - Texto de ayuda o mensaje de error.
 */
type InputFormType = {
  label: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: () => void;
  error?: boolean;
  helperText?: string;
  maxLength?: number;
};

/**
 * Componente InputForm
 * Renderiza un campo de entrada reutilizable con estilos personalizados.
 * @param {string} label - Etiqueta del campo.
 * @param {string} placeholder - Texto de marcador de posición.
 * @param {string} value - Valor actual del campo.
 * @param {(e: React.ChangeEvent<HTMLInputElement>) => void} onChange - Función que maneja cambios en el campo.
 * @param {() => void} [onBlur] - Función opcional que se ejecuta al perder el foco.
 * @param {boolean} [error] - Indica si el campo tiene un error.
 * @param {string} [helperText] - Texto de ayuda o mensaje de error.
 */
const InputForm: NextPage<InputFormType> = ({
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  maxLength,
}) => {
  const isSmallScreen = useMediaQuery("(max-width: 450px)");
  return (
    <Box className={styles.textParent}>
      <Box className={styles.label1}>{label}</Box>
      <TextField
        className={styles.input}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        error={error}
        helperText={helperText}
        variant="outlined"
        inputProps={{
          maxLength,
          style: {
            paddingLeft: isSmallScreen ? "10px" : "20px",
            fontFamily: "Poppins",
          },
        }}
        sx={{
          "& fieldset": { borderColor: "#10265f" },
          "& .MuiInputBase-root": {
            height: "53px",
            backgroundColor: "#fff",
            borderRadius: "12px",
            fontSize: "14px",
          },
          "& .MuiInputBase-input": {
            color: "black",
          },
        }}
      />
    </Box>
  );
};

export default InputForm;
