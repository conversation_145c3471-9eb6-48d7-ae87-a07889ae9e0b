import styled from "styled-components";

export const Container = styled.div`
  width: 100%;
  margin-bottom: 24px;
`;

export const TitleContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
`;

export const BackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  border: 0.91px solid #10265F;
  background: #10265F;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #fff;
  
  &:hover {
    background: #10265F;
    border: 0.91px solid #10265F;
    color: #fff;
  }

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
`;

export const TitleContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const Title = styled.h1`
  font-size: 28px;
  font-weight: 700;
  color: #10265F;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 24px;
    gap: 8px;
  }

  @media (max-width: 480px) {
    font-size: 20px;
    gap: 6px;
  }
`;

export const Subtitle = styled.p`
  font-size: 16px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
  max-width: 800px;

  @media (max-width: 768px) {
    font-size: 15px;
  }

  @media (max-width: 480px) {
    font-size: 14px;
  }
`;

export const AdditionalInfoContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 16px;
  padding: 16px;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-left: 56px;

  @media (max-width: 768px) {
    margin-left: 52px;
    padding: 14px;
    gap: 10px;
  }

  @media (max-width: 480px) {
    margin-left: 0;
    padding: 12px;
    gap: 8px;
  }
`;

export const InfoIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #3b82f6;
  color: white;
  border-radius: 8px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
    border-radius: 6px;

    svg {
      font-size: 16px !important;
    }
  }
`;

export const AdditionalInfoText = styled.p`
  font-size: 14px;
  color: #475569;
  margin: 0;
  line-height: 1.5;
  flex: 1;

  @media (max-width: 768px) {
    font-size: 13px;
  }

  @media (max-width: 480px) {
    font-size: 12px;
  }
`;
