import { Cobertura } from '@/hooks/useClaim';
import { Button } from '@mui/material';
import { XIcon } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { btnStyles, screenContentStyle, screenOverlayStyle } from './styles';

interface ClaimRequirementsProps {
  open: boolean;
  coberturas: { coberturas: Cobertura[], image: string, claimId: number };
  setOpen: () => void;
}

const ClaimRequirements = ({ setOpen, open, coberturas }: ClaimRequirementsProps) => {
  const router = useRouter();

  if (!open) return null;

  const modalContentStyle: React.CSSProperties = {
    ...screenContentStyle,
    borderRadius: '10px',
    maxWidth: '600px',
    width: '100%',
    height: '95vh',
  };

  const headerStyle = {
    display: 'block',
    fontSize: '1.4rem',
    fontWeight: 'bold',
  };

  return (
    <div style={screenOverlayStyle} onClick={(e) => e.stopPropagation()}>
      <div style={modalContentStyle} onClick={(e) => e.stopPropagation()}>
        <button style={{ border: 'none', background: 'none', fontWeight: 'bold', alignSelf: 'flex-end', cursor: 'pointer' }} onClick={setOpen}>
          <XIcon />
        </button>

        <section
          style={{
            display: 'flex',
            gap: '1rem',
            flex: '1',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            marginTop: '2rem',
          }}
        >
          <Image src={coberturas.image} alt="Cobertura representacion del producto" width={200} height={200} />
          <h2 style={headerStyle}>Antes de comenzar. <br /> Ten a la mano los siguientes documentos</h2>
          <ul style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', textAlign: 'left', paddingLeft: '1rem' }}>
            <li>Identificacion oficial</li>
            <li>Comprobante de domicilio (no mayor a 3 meses)</li>
            <li>Caratura de estado de cuenta bancario</li>
            {coberturas.coberturas.map(cobertura => {
              if(cobertura.documentos) {
                return cobertura.documentos
              }
            }).flat().map(documento => {
              return <li key={documento?.id}>{`${documento?.nombreDocumento} (${documento?.descripcion})`}</li>;
            })}
          </ul>
        </section>
        <Button
          variant="contained"
          onClick={() => router.push(`/siniestros/form/?claim=${coberturas.claimId}`)}
          sx={{
            ...btnStyles,
            borderRadius: '30px',
            height: '50px',
            width: '100%',
          }}
        >
          Listo
        </Button>
      </div>
    </div>
  );
};

export default ClaimRequirements;
