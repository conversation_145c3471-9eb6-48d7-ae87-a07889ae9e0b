import OTPInput from "@/app/signUp/otp-code/components/OtpInput";
import {
  Button1,
  ButtonParent,
  ButtonsContainer,
  CardDigits,
  Enter4Digits,
  Enter4DigitsCodeParent,
  ErrorText,
  FrameParent,
  Grupo16070,
  HolaSoyTikiSolo1,
  IconSms,
  IconSmsWrapper,
  StyledImage,
  StyledTypography,
  ButtonText,
  Text,
  Title2,
  MessageResent,
} from "../Styles/RecoveryPassword.styles";
import { Dispatch, SetStateAction, useState } from "react";
import useTemp from "@/hooks/useTemp";
import { Button } from "@mui/material";

type Props = {
  handleSendVerificationCode: (params: { email: string }) => void;
  error: Error | null;
  setNextStep?: (step: string) => void;
  setOtpSent: Dispatch<SetStateAction<string>>;
  handleVerifyOtpCode: () => void;
  otpInfo: { email: string };
};

const Verify = ({
  handleSendVerificationCode,
  error,
  setOtpSent,
  handleVerifyOtpCode,
  otpInfo,
}: Props) => {
  const [errors, setErrors] = useState<boolean>(false);
  const { time, disable, restart } = useTemp();
  const [showResentMessage, setShowResentMessage] = useState(false);

  const handleResend = () => {
    handleSendVerificationCode(otpInfo);
    restart();

    setShowResentMessage(true);
    setTimeout(() => {
      setShowResentMessage(false);
    }, 5000);
  };

  return (
    <>
      <Text>
        <Title2>Revisa tu correo</Title2>
        <HolaSoyTikiSolo1>
          Te hemos enviado un código de verificación a tu correo.
        </HolaSoyTikiSolo1>
      </Text>
      <CardDigits>
        <IconSmsWrapper>
          <IconSms>
            <StyledImage
              width={48}
              height={48}
              alt="Ícono mensaje"
              src="/web/img/verification/message.svg"
            />
          </IconSms>
        </IconSmsWrapper>
        <Enter4DigitsCodeParent>
          <Enter4Digits>Introduce los 6 dígitos que recibiste</Enter4Digits>
          <Grupo16070>
            <OTPInput
              setOtpSent={setOtpSent}
              errors={errors}
              setErrors={setErrors}
            />
          </Grupo16070>
        </Enter4DigitsCodeParent>
        <FrameParent>
          <ButtonParent>
            <Button onClick={handleResend} disabled={!disable}>
              <ButtonText>Reenviar</ButtonText>
            </Button>
            <StyledTypography
              variant="inherit"
              // component="b"
              sx={{ fontSize: "18px", fontWeight: "700" }}
            >
              {time}
            </StyledTypography>
          </ButtonParent>
          {error && (
            <ErrorText>Error al enviar código. Inténtalo nuevamente.</ErrorText>
          )}
          <ButtonsContainer>
            <Button1 onClick={handleVerifyOtpCode} disabled={disable}>
              Continuar
            </Button1>
          </ButtonsContainer>
        </FrameParent>
      </CardDigits>
      {showResentMessage && (
        <MessageResent>Se reenvió el código de verificación</MessageResent>
      )}
    </>
  );
};

export default Verify;
