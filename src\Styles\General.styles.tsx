import styled from "styled-components";
import { styled as muiStyled } from "@mui/material/styles";
import { FormControl, TextField } from "@mui/material";
import { useRef } from "react";
import { Upload } from "@mui/icons-material";

type FilterContainerProps = {
  children: React.ReactNode;
  name?: string;
  style?: React.CSSProperties;
  fragmentAtTitle?: React.ReactNode;
};

export const StyledTitle = styled.h1`
  color: #10265f;
  font-family: Poppins;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  @media (max-width: 1023px) {
    font-size: 32px;
  }
`;
export const PrimaryButton = styled.button`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  width: 256px;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  text-transform: capitalize;
  color: #ffffff;
  font-weight: 400;
  font-size: 16px;
  background-color: #10265f;
  &:disabled {
    background-color: #c4c4c4;
    cursor: not-allowed;
  }
`;

interface InputsContainer2Props {
  columns?: number;
}

export const InputsContainer2 = styled.div<InputsContainer2Props>`
  display: grid;
  grid-template-columns: ${({ columns }) => `repeat(${columns || 3}, 1fr)`};
  width: 100%;
  gap: 24px;
  grid-auto-flow: dense;
  @media (max-width: 1240px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(1, 1fr);
  }
`;

const FilterContain = styled(FormControl)`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #6d6f71;
  height: 100%;
  justify-content: flex-end;
`;

export const FilterContainer: React.FC<FilterContainerProps> = ({
  children,
  name,
  style,
  fragmentAtTitle,
}) => {
  return (
    <FilterContain style={style}>
      <div
        style={{
          display: "flex",
          alignItems: "flex-end",
          gap: "8px",
          width: "100%",
          justifyContent: "space-between",
          fontFamily: "Poppins",
          color: "#333",
          fontSize: "16px",
          fontStyle: "normal",
          fontWeight: "400",
          lineHeight: "normal",
        }}
      >
        {name}
        {fragmentAtTitle}
      </div>

      {children}
    </FilterContain>
  );
};

export const StyledInput = muiStyled(TextField)(() =>
  // { $border }
  ({
    "& .MuiInputBase-root": {
      borderRadius: "8px",
      height: "42px",
      fontFamily: "Poppins",
      margin: "0",
      backgroundColor: "white",
      "&.Mui-disabled": {
        backgroundColor: "#e0e0e0",
      },
      "& input": {
        padding: "0 14px !important",
      },
    },
    "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
      borderColor: "#FDA29B !important",
    },

    // '& input[type="date"]::-webkit-calendar-picker-indicator': {
    //   background: `url(${IconDate}) no-repeat center`,
    //   backgroundSize: "contain",
    //   opacity: 1,
    //   justifySelf: "left",
    //   order: -1,
    //   marginRight: "1rem",
    // },
  })
);

const StyledInputFile = styled.button`
  display: flex;
  width: fit-content;
  padding: 8px 24px;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  border: 2px dashed var(--Primary-Navy, #10265f);
  background: var(--Background-White, #fff);
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  &:disabled {
    background-color: #e0e0e0;
    cursor: not-allowed;
    border: 2px dashed #c4c4c4;
  }
`;

interface FileUploaderProps {
  onFileSelect: (file: File) => void;
  label?: string;
  accept?: string;
  multiple?: boolean;
  disabled?: boolean;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onFileSelect,
  label,
  accept = "*",
  multiple = false,
  disabled = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    if (multiple) {
      Array.from(files).forEach((file) => onFileSelect(file));
    } else {
      onFileSelect(files[0]);
    }
  };

  return (
    <>
      <StyledInputFile onClick={handleClick} disabled={disabled}>
        {label ? (
          label
        ) : (
          <>
            <Upload /> Subir PDF o JPG
          </>
        )}
      </StyledInputFile>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: "none" }}
        onChange={handleChange}
        accept={accept}
        multiple={multiple}
      />
    </>
  );
};
