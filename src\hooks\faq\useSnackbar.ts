import { useState } from "react";
/**
 * Hook para manejar el estado y la lógica de un Snackbar.
 * Este hook encapsula toda la lógica necesaria para mostrar y cerrar un Snackbar,
 * incluyendo el mensaje, la severidad y el estado de visibilidad.
 *
 * @returns {Object} - Un objeto con las propiedades y funciones necesarias para manejar el Snackbar.
 *
 * @property {boolean} snackbarOpen - Indica si el Snackbar está abierto o cerrado.
 * @property {string} snackbarMessage - El mensaje que se mostrará en el Snackbar.
 * @property {"success" | "error"} snackbarSeverity - La severidad del Snackbar (puede ser "success" o "error").
 * @property {Function} showSnackbar - Función para mostrar el Snackbar con un mensaje y una severidad.
 * @property {Function} handleSnackbarClose - Función para cerrar el Snackbar.
 */
export const useSnackbar = () => {
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">(
    "success"
  );

  const showSnackbar = (message: string, severity: "success" | "error") => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return {
    snackbarOpen,
    snackbarMessage,
    snackbarSeverity,
    showSnackbar,
    handleSnackbarClose,
  };
};
