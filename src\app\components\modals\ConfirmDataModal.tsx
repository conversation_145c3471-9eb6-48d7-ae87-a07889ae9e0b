import { Modal } from "@mui/material";
import { FunctionComponent } from "react";
import {
  But<PERSON><PERSON><PERSON><PERSON>,
  StyledContainer,
  StyledLayout,
  StyledSubtitle,
  StyledTitle,
} from "./PasswordRecovery/Styles/RecoveryPassword.styles";
import styled from "styled-components";
import Image from "next/image";
import imgSrc from "/public/web/img/Confirm01.png";

type ConfirmDataModalProps = {
  open: boolean;
  onClose: () => void;
  email: string;
  phone: string;
  onConfirm: () => void;
};

const ConfirmDataModal: FunctionComponent<ConfirmDataModalProps> = ({
  open,
  onClose,
  email,
  phone,
  onConfirm,
}) => {
  return (
    <>
      <Modal open={open} onClose={onClose}>
        <StyledLayout>
          <StyledContainer>
            <Image src={imgSrc} alt="" width={218} height={210} />
            <StyledTitle> Confirma que tus datos son correctos</StyledTitle>
            <StyledSubtitle style={{ maxWidth: "400px" }}>
              Nos comunicaremos contigo a través de ellos
            </StyledSubtitle>
            <DataContainer>
              <div>
                <span>Email: </span> &nbsp;{email}
              </div>
              <div>
                <span>Número de celular:&nbsp;</span>
                {phone}
              </div>
            </DataContainer>
            <ButtonContainer>
              <PrimaryButton onClick={onConfirm}>Es correcto</PrimaryButton>
              <SecondaryButton onClick={onClose}>Corregir</SecondaryButton>
            </ButtonContainer>
            <br />
            <StyledSubtitle>
              Al continuar, acepta nuestros términos y condiciones.
            </StyledSubtitle>
          </StyledContainer>
        </StyledLayout>
      </Modal>
    </>
  );
};

export default ConfirmDataModal;

const PrimaryButton = styled.button`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  width: 256px;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  text-transform: capitalize;
  color: #ffffff;
  font-weight: 400;
  font-size: 16px;
  background-color: #10265f;
`;

const SecondaryButton = styled.button`
  cursor: pointer;
  border: none;
  padding: 16px 24px;
  width: 256px;
  border-radius: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  min-width: 188px;
  text-transform: capitalize;
  color: #10265f;
  font-weight: 400;
  font-size: 16px;
  outline: 2px solid #10265f;
  background-color: #fff;
`;

const DataContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 16px 24px;
  font-weight: 400;
  font-size: 16px;
  font-family: Poppins;
  div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    span {
      font-weight: 700;
    }
  }
`;
