import styled from "styled-components";

// Contenedor general de la sección
export const Container = styled.section`
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 30px 70px;
  width: 100%;
  background: #FFF;
`;

// Contenedor de cada bloque 
export const SettingBlock = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
`;

// Contenedor de descripción
export const TextBlock = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
`;

// Título 
export const SectionTitle = styled.p`
  margin: 0;
  color: #6D6D6D;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: left;
`;

// Descripción 
export const SectionDescription = styled.p`
  margin: 0;
  color: #6D6D6D;
  align-self: stretch;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

// Enlace de acción 
export const ActionLink = styled.button`
  background: none;
  border: none;
  padding: 0;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: #10265F;
  cursor: pointer;
  text-decoration: underline;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: 6%; /* 0.96px */
  text-underline-offset: 16%; /* 2.56px */
  text-underline-position: from-font;

  &:hover {
    text-decoration: none;
  }
`;
