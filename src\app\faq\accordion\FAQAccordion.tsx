"use client";
import type { NextPage } from "next";
import { useState } from "react";
import { Box } from "@mui/material";
import Image from "next/image";
import styles from "./styles.module.css";
import QuestionsAnswers from "../questionsAnswers/QuestionsAnswers";
import { FAQAccordionProps } from "@/infrastructure/models/faq.model";

/**
 * Componente FAQAccordion
 * Representa un acordeón para una categoría de preguntas frecuentes.
 * @param {string} title - Título de la categoría.
 * @param {FAQItem[]} data - Lista de preguntas y respuestas asociadas.
 */
const FAQAccordion: NextPage<FAQAccordionProps> = ({ title, data }) => {
  // Estado para manejar si el acordeón está abierto o cerrado
  const [isOpen, setIsOpen] = useState(false);

  // Estado para manejar si el acordeón está en hover
  const [isHovered, setIsHovered] = useState(false);
  return (
    <Box
      className={styles.serviceItemWrapper}
      onClick={() => setIsOpen(!isOpen)}
      onMouseEnter={() => setIsHovered(true)} // Detectar hover
      onMouseLeave={() => setIsHovered(false)} // Detectar salida del hover
      style={{ cursor: "pointer" }}
    >
      <Box className={styles.serviceItem}>
        <Box
          className={styles.accesoYGestinDeLaCuentaParent}
          style={{
            color: "#10265f",
          }}
        >
          <Box className={styles.dropDownHeader}>{title}</Box>
          <Image
            className={styles.icons}
            width={18}
            height={18}
            alt="Toggle dropdown"
            src={isOpen ? "/web/img/x.svg" : "/web/img/plus.svg"}
          />
        </Box>
        {/* Preguntas y respuestas */}
        <QuestionsAnswers arrFAQ={data} isOpen={isOpen} isHovered={isHovered} />
      </Box>
    </Box>
  );
};

export default FAQAccordion;
