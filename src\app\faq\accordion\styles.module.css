.serviceItemWrapper {
  align-self: stretch;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.35);
  border-radius: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 40px 20px;
  border: 2px solid transparent; /* Borde inicial transparente */
  transition: border-color 0.8s ease; /* Transición para el color del borde */
}
/* Cambiar el borde al hacer hover */
.serviceItemWrapper:hover {
  border-color: #000000; /* Cambia el borde a negro */
}
.serviceItem {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 10px;
}
.accesoYGestinDeLaCuentaParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 0;
}
/* Subrayado animado para el título */
.dropDownHeader {
  position: relative;
  font-weight: 600;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  transition: color 0.3s ease; /* Transición para el color */
}
.dropDownHeader::after {
  content: "";
  position: absolute;
  bottom: -1px; /* Ubicación del subrayado */
  left: 0;
  width: 0;
  height: 1px;
  background-color: #10265f; /* Color del subrayado */
  transition: width 0.3s ease; /* Transición para la animación */
}
.serviceItemWrapper:hover .dropDownHeader::after {
  width: 100%; /* Expande el subrayado al 100% */
}
.icons {
  height: 18px;
  width: 18px;
  position: relative;
}
@media screen and (max-width: 450px) {
  .serviceItemWrapper {
    padding: 20px 21px 20px;
  }
  .dropDownHeader {
    font-size: 16px;
  }
  .icons {
    height: 16px;
    width: 16px;
  }
}
