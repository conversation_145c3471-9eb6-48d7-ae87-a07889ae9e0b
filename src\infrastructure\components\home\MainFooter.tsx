"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  ContentTextFooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON>Footer<PERSON>ontainer,
  <PERSON><PERSON>ontFooter,
} from "@/Styles/MainFooter.styles";
import { Divider } from "@mui/material";
import Logo from "/public/web/img/wiki_seguros_footer.svg";
import Playstore from "/public/web/img/googleplay.svg";
import Appstore from "/public/web/img/appstore.svg";
import Instagram from "/public/web/img/instagram.svg";
import Facebook from "/public/web/img/facebook.svg";
import LinkedIn from "/public/web/img/in.svg";
import TikTok from "/public/web/img/tiktok.svg";
import Link from "next/link";
import PrivacyModal from "@/app/components/modals/LegalDocuments/PrivacyModal";
import TermsModal from "@/app/components/modals/LegalDocuments/TermsModal";

const FooterSection = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => (
  <ContentTextFooter>
    <h4 style={{ fontWeight: 600 }}>{title}</h4>
    {children}
  </ContentTextFooter>
);

const FooterLink = ({
  href,
  onClick,
  children,
}: {
  href?: string;
  onClick?: () => void;
  children: React.ReactNode;
}) => {
  if (onClick) {
    return (
      <LinkFontFooter as="button" onClick={onClick}>
        {children}
      </LinkFontFooter>
    );
  }

  return (
    <LinkFontFooter href={href} target="_blank">
      {children}
    </LinkFontFooter>
  );
};

const SocialMediaIcon = ({
  src,
  alt,
  width,
  href = "#",
}: {
  src: string;
  alt: string;
  width: number;
  href?: string;
}) => (
  <Link
    target="_blank"
    href={href}
    style={{
      display: "flex",
      alignItems: "center",
      fontSize: "16px",
      fontStyle: "normal",
      fontWeight: "400",
    }}
  >
    <Image src={src} alt={alt} width={width} height={38} />
  </Link>
);

/**
 * Componente `MainFooter` que representa el pie de página principal de la aplicación.
 *
 * Este componente incluye:
 * - Un logo de Wiki Seguros.
 * - Un mensaje para descargar la aplicación.
 * - Enlaces a Google Play y App Store.
 * - Información de redes sociales.
 * - Secciones de enlaces adicionales como "Acerca de Wiki" y "Legales".
 *
 * @component
 * @example
 * return (
 *   <MainFooter />
 * )
 */
const MainFooter = () => {
  const [showPrivacy, setShowPrivacy] = useState(false);
  const [showTerms, setShowTerms] = useState(false);

  return (
    <MainFooterContainer>
      <div className="logo-section">
        <Image src={Logo} alt="Wiki Seguros" width={136} height={65} />

        <div className="app-section">
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "16px",
            }}
          >
            <PFontFooter style={{ fontSize: "20px" }}>
              DESCARGA LA APP
            </PFontFooter>
          </div>
          <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
            <Image src={Playstore} alt="Google Play" width={130} height={46} />
            <Image src={Appstore} alt="App Store" width={130} height={46} />
          </div>
        </div>
      </div>
      <DividerContainer>
        <Divider flexItem />
      </DividerContainer>
      <FooterSection title="Acerca de Wiki">
        <FooterLink href="/faq">Preguntas frecuentes</FooterLink>
        <FooterLink href="/terms">
          Términos y condiciones
        </FooterLink>
        <FooterLink href="/privacy">
          Aviso de privacidad
        </FooterLink>
      </FooterSection>

      <DividerContainer>
        <Divider flexItem />
      </DividerContainer>
      <FooterSection title="Contáctanos">
        <FooterLink href="tel:5591264600"> 55 9126 4600 </FooterLink>
        <FooterLink href="mailto:<EMAIL>">
          {" "}
          <EMAIL>{" "}
        </FooterLink>
        <div style={{ display: "flex", gap: "25px", alignContent: "center" }}>
          <SocialMediaIcon
            src={Instagram}
            alt="Instagram"
            width={35}
            href="https://www.instagram.com/wikiproteccion"
          />

          <SocialMediaIcon
            src={Facebook}
            alt="Facebook"
            width={18}
            href="https://www.facebook.com/profile.php?id=61572685483301&rdid=VXu9auijaEGsrlHw&share_url=https%3A%2F%2Fwww.facebook.com%2Fshare%2F1CCvyYnyDS%2F"
          />
          <SocialMediaIcon
            src={LinkedIn}
            alt="LinkedIn"
            width={34}
            href="https://www.linkedin.com/company/wikiproteccion/"
          />
          <SocialMediaIcon
            src={TikTok}
            alt="TikTok"
            width={31}
            href="https://www.tiktok.com/@wikiproteccion"
          />
        </div>
      </FooterSection>
      <PrivacyModal
        isOpen={showPrivacy}
        onClose={() => setShowPrivacy(false)}
      />
      <TermsModal isOpen={showTerms} onClose={() => setShowTerms(false)} />
    </MainFooterContainer>
  );
};

export default MainFooter;
