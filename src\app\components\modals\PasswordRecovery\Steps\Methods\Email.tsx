import Image from "next/image";
import {
  ErrorText,
  <PERSON><PERSON><PERSON>,
  StyledForm,
  StyledSubtitle,
  StyledTitle,
} from "../../Styles/RecoveryPassword.styles";
import { Formik, Field, ErrorMessage } from "formik";
import * as Yup from "yup";

const emailValidationSchema = Yup.object().shape({
  email: Yup.string()
    .required("El número de teléfono es obligatorio")
    .email("Introduce un correo válido"),
});

type Props = {
  handleSendVerificationCode: (params: { email: string }) => void;
  error: Error | null;
};

const SMS = ({ handleSendVerificationCode, error }: Props) => {
  return (
    <>
      <Image
        src="/web/img/cardsProducts/wikiHackeo.png"
        alt="Logo"
        width={195}
        height={180}
      />
      <StyledTitle>Recuperar Contraseña</StyledTitle>
      <StyledSubtitle>
        Por favor, ingresa el correo con el cual te registraste para recibir un
        código de recuperación.
      </StyledSubtitle>
      <Formik
        initialValues={{ email: "" }}
        validationSchema={emailValidationSchema}
        onSubmit={(values) => {
          handleSendVerificationCode({
            email: values.email,
          });
        }}
      >
        {({ errors, touched, handleSubmit }) => (
          <StyledForm onSubmit={handleSubmit}>
            <Field name="email" type="text" placeholder="Correo Electrónico" />
            {errors.email && touched.email && (
              <ErrorText>
                <ErrorMessage name="email" />
              </ErrorText>
            )}
            {error && (
              <ErrorText>Error al enviar. Intentalo nuevamente</ErrorText>
            )}
            <Sendbutton
              type="submit"
              disabled={!!errors.email && touched.email}
            >
              Enviar
            </Sendbutton>
          </StyledForm>
        )}
      </Formik>
    </>
  );
};

export default SMS;
