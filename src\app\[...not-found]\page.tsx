"use client";

import Link from "next/link";
import styled from "styled-components";
import { gsap } from "gsap";
import { useEffect, useRef } from "react";
import { StyledSubmitButton } from "../components/modals/login/Login.styles";
import Image from "next/image";

const Page404 = () => {
  const containerRef = useRef(null);

  useEffect(() => {
    if (containerRef.current) {
      gsap.from(containerRef.current, {
        opacity: 0,
        y: -50,
        duration: 1,
        ease: "power3.out",
      });
      gsap.to(containerRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out",
      });
    }
  }, []);

  return (
    <StyledContainer ref={containerRef}>
      <Image src="/web/img/404.png" alt="404" width={"100"} height={"70"} />

      <h2>Parece que la página que buscas no existe :(</h2>
      <Link href="/" style={{ textDecoration: "none", color: "inherit" }}>
        <StyledSubmitButton>Volver a la página principal</StyledSubmitButton>
      </Link>
    </StyledContainer>
  );
};
export default Page404;

const StyledContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: calc(100dvh - 370px);
  padding: 2rem;
  background-color: #f0f0f0;
  width: 100%;
  gap: 1rem;

  h2 {
    font-size: clamp(1rem, 4vw, 2rem);
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
  img {
    width: 100%;
    height: auto;
    max-width: 400px;
    /* background-color: red; */
  }
`;
