"use client";
import { ProtectedLayout } from "@/core/layouts/ProtectedLayout";
import SelectReporte from "./Components/SelectReporte";
import { useState } from "react";
import MakeReport from "./Components/MakeReport";

const Page = () => {
  const [step, setStep] = useState<
    "select" | "makereport" | "about" | "identify"
  >("select");

  const handleFinishSelect = () => {
    setStep("makereport");
  };
  return (
    <>
      <ProtectedLayout>
        {step === "select" && <SelectReporte onFinish={handleFinishSelect} />}
        {step === "makereport" && <MakeReport />}
        {step === "about" && <div>about</div>}
        {step === "identify" && <div>identify</div>}
      </ProtectedLayout>
    </>
  );
};
export default Page;
