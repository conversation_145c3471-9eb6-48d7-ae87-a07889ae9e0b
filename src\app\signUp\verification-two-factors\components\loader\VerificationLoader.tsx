import { Box, Typography } from "@mui/material";
import Image from "next/image";
import styles from "./verification-loader.module.css";

import CargaImg from "../../../../../../public/web/img/verification/carga.svg";

const VerificationLoader = () => {
	return (
		<section className={styles.sectionVerifiLoader}>
			<Box className={styles.cardContainer}>
				<Box display={"flex"} alignItems={"center"} textAlign={"center"}>
					<Typography className={styles.title}>
						Estamos procesando tu solicitud
					</Typography>
				</Box>
				<Box className={styles.sectionPlanesInner}>
					<Image width={237} height={237} alt="" src={CargaImg} />
					<Typography className={styles.subTitle}>PROCESANDO</Typography>
				</Box>
			</Box>
		</section>
	);
};

export default VerificationLoader;
