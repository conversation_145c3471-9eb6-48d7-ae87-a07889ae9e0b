import { CoverageType, Product } from "@/app/product/products.types";
import { create, StateCreator } from "zustand";
import { devtools, persist } from "zustand/middleware";

export type PurchaseStep =
  | "productSelection"
  | "coverageConfirmation"
  | "dataVerification"
  | "purchase";

interface PurchaseState {
  selectedProduct: CoverageType | null;
  currentStep: PurchaseStep;
  isUserLoggedIn: boolean;
  modalOpen: boolean;
  currentProduct: string | null;
  // Acciones
  selectProduct: (product: CoverageType | Product) => void;
  goToCoverageView: () => void;
  verifyUserLogin: () => void;
  completeLogin: () => void;
  goToDataVerification: () => void;
  completePurchase: () => void;
  resetPurchase: () => void;
  openModal: () => void;
  closeModal: () => void;
  confirmStep: () => void;
  setCurrentProduct: (id: string) => void;
}

export const resetPurchaseStore = () => {
  usePurchaseStore.getState().resetPurchase();
};

export const usePurchaseStore = create<PurchaseState>(
  devtools(
    persist(
      (set) => ({
        selectedProduct: null,
        currentStep: "productSelection",
        isUserLoggedIn: false,
        modalOpen: false,
        currentProduct: null,

        // Al seleccionar un producto se guarda y se pasa al siguiente paso: confirmación (coverageView)
        selectProduct: (product: CoverageType | Product) =>
          set(() => ({
            selectedProduct: product,
            currentStep: "coverageConfirmation",
          })),

        setCurrentProduct: (id: string) =>
          set(() => ({
            currentProduct: id,
          })),

        confirmStep: () =>
          set(() => ({
            currentStep: "coverageConfirmation",
          })),
        // Para pasar a la vista de coverage (confirmación)
        goToCoverageView: () =>
          set(() => ({
            currentStep: "coverageConfirmation",
          })),

        // Verifica si el usuario está loggeado. Si lo está, pasa al siguiente paso, sino abre el modal.
        verifyUserLogin: () =>
          set((state: PurchaseState) => {
            if (state.isUserLoggedIn) {
              return { currentStep: "dataVerification" };
            } else {
              // Se activa el modal para login; tú te encargas de mostrarlo.
              return { modalOpen: true, currentStep: "login" };
            }
          }),

        // Una vez completado el login, se marca al usuario como loggeado y se procede a verificación de datos.
        completeLogin: () =>
          set(() => ({
            isUserLoggedIn: true,
            modalOpen: false,
            currentStep: "dataVerification",
          })),

        // Cambia el estado a verificación de datos
        goToDataVerification: () =>
          set(() => ({
            currentStep: "dataVerification",
          })),

        // Finaliza la compra
        completePurchase: () =>
          set(() => ({
            currentStep: "purchase",
          })),

        // Reinicia el proceso de compra
        resetPurchase: () =>
          set(() => ({
            selectedProduct: null,
            currentStep: "productSelection",
            isUserLoggedIn: false,
            modalOpen: false,
          })),
      }),
      { name: "PurchaseStore" }
    ),
    {
      name: "purchase-storage",
    }
  ) as unknown as StateCreator<PurchaseState, [], []>
);
