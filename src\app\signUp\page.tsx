"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  FormControlLabel,
  Checkbox,
  Typography,
  Modal,
  CircularProgress,
  MenuItem,
} from "@mui/material";
import * as S from "./signUp.styles";
import useAuth from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { ProtectedLayout } from "@/core/layouts/ProtectedLayout";
import { useOtpCode } from "@/store/signUp/otpCode.store";
import { Eye, EyeClosed } from "lucide-react";
import { AuthService } from "@/infrastructure/services/authService";
import { RegisterErrorResponse } from "@/infrastructure/services/dtos/register-dto";
import ConfirmDataModal from "../components/modals/ConfirmDataModal";
import ExistingAccountModal from "../components/modals/ExistingAccountModal";
import TermsModal from "../components/modals/LegalDocuments/TermsModal";
import PrivacyModal from "../components/modals/LegalDocuments/PrivacyModal";
import { useSnackbar } from "@/context/SnackbarContext";

type DataType = {
  estado: string;
  estado_abreviatura: string;
  municipio: string;
  centro_reparto: string;
  codigo_postal: string;
  colonias: string[];
};

export default function SignUp() {
  const { register, loading } = useAuth();
  const { showLoading, showSuccess, showError, hide } = useSnackbar();
  const router = useRouter();
  const setOtpCode = useOtpCode((state) => state.setOtpCode);
  const [seePassword, setSeePassword] = useState(false);
  const [seeConfirmPassword, setSeeConfirmPassword] = useState(false);
  const [disabledInputs, setDisabledInputs] = useState(true);
  const [openConfirmDataModal, setOpenConfirmDataModal] = useState(false);
  const [openExistingAccountModal, setOpenExistingAccountModal] = useState(false);
  const [existingAccountError, setExistingAccountError] = useState<string>("");
  const [openTermsModal, setOpenTermsModal] = useState(false);
  const [openPrivacyModal, setOpenPrivacyModal] = useState(false);

  const authService = new AuthService();

  // Función para manejar el registro real después de la confirmación
  const handleConfirmRegistration = () => {
    const {
      confirmPassword,
      confirmEmail,
      confirmPhone,
      termsAndConditions,
      ...dataToSend
    } = formik.values;

    // Evitar warnings de variables no usadas
    void confirmPassword;
    void confirmEmail;
    void confirmPhone;
    void termsAndConditions;

    setOtpCode({
      email: formik.values.email,
      name: formik.values.firstName,
      phone: formik.values.phone,
      nickname: formik.values.nickname,
    });

    setOpenConfirmDataModal(false);
    showLoading("Creando tu cuenta…");

    register({ ...dataToSend })
      .then((response) => {
        hide();
        showSuccess("¡Cuenta creada!");
        console.log("Respuesta del registro:", response);
        router.push("/signUp/verification-two-factors");
      })
      .catch((error) => {
        hide();
        const responseData = error?.response?.data;

        // Verificar si es un error de datos duplicados (statusCode 400)
        if (error?.response?.status === 400 && responseData?.inUse) {
          // Nueva estructura de respuesta con array de mensajes e inUse
          const errorResponse = responseData as RegisterErrorResponse;
          const messages = Array.isArray(errorResponse.message)
            ? errorResponse.message
            : [errorResponse.message];

          // Crear un objeto con la información de conflicto
          const conflictInfo = {
            messages,
            inUse: errorResponse.inUse
          };

          setExistingAccountError(JSON.stringify(conflictInfo));
          setOpenExistingAccountModal(true);
        } else {
          // Manejar errores con formato anterior o errores generales
          let msg: string;

          if (Array.isArray(responseData?.message)) {
            msg = responseData.message.join(', ');
          } else if (typeof responseData?.message === 'string') {
            msg = responseData.message;
          } else if (typeof error?.message === 'string') {
            msg = error.message;
          } else {
            msg = "Ocurrió un error inesperado, intenta de nuevo";
          }

          // Verificar si es un error de datos duplicados con formato anterior
          if (error?.response?.status === 400 &&
              (msg.toLowerCase().includes('ya está registrado') ||
               msg.toLowerCase().includes('ya existe'))) {
            setExistingAccountError(msg);
            setOpenExistingAccountModal(true);
          } else {
            // Mostrar error normal
            showError(msg);
          }
        }

        console.error("Error en el registro:", error);
      });
  };

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastNamePaternal: "",
      lastNameMaternal: "",
      birthDate: "",
      nickname: "",
      gender: "",
      street: "",
      exteriorNumber: "",
      interiorNumber: "",
      postalCode: "",
      neighborhood: "",
      municipality: "",
      email: "",
      confirmEmail: "",
      phone: "",
      confirmPhone: "",
      password: "",
      confirmPassword: "",
      termsAndConditions: false,
    },
    validationSchema: Yup.object({
      firstName: Yup.string()
        .max(40, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa tu(s) nombre(s)."),
      lastNamePaternal: Yup.string()
        .max(40, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa tu apellido paterno."),
      lastNameMaternal: Yup.string()
        .max(40, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa tu apellido materno."),
      birthDate: Yup.date()
        .required("Por favor, selecciona tu fecha de nacimiento.")
        .test("age", "Debes tener al menos 18 años", (value) => {
          return (
            value &&
            new Date().getFullYear() - new Date(value).getFullYear() >= 18
          );
        })
        .test("age", "Debes tener menos de 99 años", (value) => {
          return (
            value &&
            new Date().getFullYear() - new Date(value).getFullYear() <= 99
          );
        }),
      nickname: Yup.string().max(
        20,
        "El texto no puede tener más de ${max} caracteres"
      ),
      gender: Yup.string().required(
        "Por favor, selecciona el género que aparece en tu identificación oficial."
      ),
      street: Yup.string()
        .max(50, "El texto no puede tener más de ${max} caracteres")
        .required("Por favor, ingresa el nombre de tu calle."),
      exteriorNumber: Yup.string()
        .matches(
          /^[\p{L}\p{N} ]+$/u,
          "Solo se permiten caracteres alfanuméricos, incluyendo ñ, espacios y acentos"
        )
        .required("Por favor, ingresa tu número exterior.")
        .max(16, "El texto no puede tener más de ${max} caracteres"),
      interiorNumber: Yup.string()
        .matches(
          /^[\p{L}\p{N} ]+$/u,
          "Solo se permiten caracteres alfanuméricos, incluyendo ñ, espacios y acentos"
        )
        .max(16, "El texto no puede tener más de ${max} caracteres"),
      postalCode: Yup.string()
        .matches(/^[0-9]+$/, "Solo se permiten números")
        .min(5, "Por favor, ingresa un código postal válido de 5 dígitos.")
        .max(5, "Por favor, ingresa un código postal válido de 5 dígitos.")
        .required("Por favor, ingresa un código postal válido de 5 dígitos."),
      neighborhood: Yup.string()
        .required("Por favor, ingresa el nombre de tu colonia.")
        .max(50, "El texto no puede tener más de ${max} caracteres"),
      municipality: Yup.string()
        .required("Por favor, ingresa el nombre de tu municipio.")
        .max(50, "El texto no puede tener más de ${max} caracteres"),
      email: Yup.string()
        .email("Por favor, ingresa un correo electrónico válido.")
        .required("Por favor, ingresa un correo electrónico válido."),
      confirmEmail: Yup.string()
        .oneOf(
          [Yup.ref("email"), undefined],
          "El correo electrónico no coincide, por favor verifica."
        )
        .required("El correo electrónico no coincide, por favor verifica."),
      phone: Yup.string()
        .matches(
          /^[0-9]{10}$/,
          "Por favor, ingresa un número de celular válido de 10 dígitos."
        )
        .required("Este campo es obligatorio"),
      confirmPhone: Yup.string()
        .oneOf(
          [Yup.ref("phone"), undefined],
          "El número de celular no coincide, por favor verifica."
        )
        .required("El número de celular no coincide, por favor verifica."),
      password: Yup.string()
        .min(8, "La contraseña debe tener al menos 8 caracteres")
        .required("Por favor, ingresa una contraseña")
        .max(20, "La contraseña no puede tener más de ${max} caracteres")
        .matches(
          /^(?=.*[a-zñ])(?=.*[A-ZÑ])(?=.*\d)(?=.*[.@$!%*?&])[a-zA-ZñÑ\d.@$!%*?&]{8,20}$/,
          "La contraseña debe tener al menos una letra mayúscula, una letra minúscula, un número y un carácter especial"
        ),
      confirmPassword: Yup.string()
        .oneOf(
          [Yup.ref("password"), undefined],
          "Las contraseñas no coinciden, por favor verifica."
        )
        .required("Las contraseñas no coinciden, por favor verifica."),
      termsAndConditions: Yup.boolean().oneOf(
        [true],
        "Debes aceptar los términos y condiciones para continuar"
      ),
    }),
    onSubmit: (values) => {
      console.log("Formulario enviado con:", values);
      // Abrir modal de confirmación en lugar de registrar directamente
      setOpenConfirmDataModal(true);
    },
  });

  const [data, setData] = useState<DataType | null>(null);

  useEffect(() => {
    if (formik.values.postalCode.length === 5) {
      formik.setFieldValue("neighborhood", null);
      try {
        authService
          .getPostalCodeData(formik.values.postalCode)
          .then((response) => {
            if (response) {
              setDisabledInputs(false);
              setData(response.codigo_postal);
              formik.setFieldValue(
                "municipality",
                response.codigo_postal.municipio
              );
              formik.setFieldTouched("municipality", true);
            }
          });
      } catch (error) {
        console.error("Error al obtener los datos del código postal:", error);
      }
    }
  }, [formik.values.postalCode]);

  const handleGenderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue("gender", event.target.value);
  };

  return (
    <ProtectedLayout out>
      <S.Container>
        <Modal open={loading}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100vw",
              height: "100vh",
              backdropFilter: "blur(5px)",
            }}
          >
            <CircularProgress size={100} sx={{ color: "#10265F" }} />
          </div>
        </Modal>
        <S.Greeting>
          <S.WelcomeMessage>¡Hola!</S.WelcomeMessage>
          <S.SubMessage>
            Para que estés bien asegurado necesito que me proporciones la
            siguiente información
          </S.SubMessage>
        </S.Greeting>
        <S.Form onSubmit={formik.handleSubmit}>
          <ConfirmDataModal
            open={openConfirmDataModal}
            onClose={() => setOpenConfirmDataModal(false)}
            onConfirm={handleConfirmRegistration}
            email={formik.values.email}
            phone={formik.values.phone}
          />
          <ExistingAccountModal
            isOpen={openExistingAccountModal}
            onClose={() => {setOpenExistingAccountModal(false); setExistingAccountError("")}}
            email={formik.values.email}
            phone={formik.values.phone}
            errorMessage={existingAccountError}
          />
          <S.LabelInput>¿Cómo te llamas?*</S.LabelInput>
          <S.InputsContainer>
            <div>
              <S.InputStyles
                type="text"
                placeholder="Nombres"
                required
                name="firstName"
                value={formik.values.firstName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.firstName && formik.errors.firstName && (
                <Typography color="error" variant="body2">
                  {formik.errors.firstName}
                </Typography>
              )}
            </div>
            <div>
              <S.InputStyles
                type="text"
                placeholder="Apellido Paterno"
                required
                name="lastNamePaternal"
                value={formik.values.lastNamePaternal}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.lastNamePaternal &&
                formik.errors.lastNamePaternal && (
                  <Typography color="error" variant="body2">
                    {formik.errors.lastNamePaternal}
                  </Typography>
                )}
            </div>
            <div>
              <S.InputStyles
                type="text"
                placeholder="Apellido Materno"
                required
                name="lastNameMaternal"
                value={formik.values.lastNameMaternal}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.lastNameMaternal &&
                formik.errors.lastNameMaternal && (
                  <Typography color="error" variant="body2">
                    {formik.errors.lastNameMaternal}
                  </Typography>
                )}
            </div>
          </S.InputsContainer>
          <S.InputsContainer>
            <div>
              <S.LabelInput>Fecha de Nacimiento*</S.LabelInput>
              <S.DatePicker>
                <S.InputDate
                  type="date"
                  name="birthDate"
                  value={formik.values.birthDate}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  required
                />
              </S.DatePicker>
              {formik.touched.birthDate && formik.errors.birthDate && (
                <Typography color="error" variant="body2">
                  {formik.errors.birthDate}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>¿Cómo te gusta que te digan?</S.LabelInput>
              <S.InputStyles
                type="text"
                placeholder="Diminutivo / Apodo"
                name="nickname"
                value={formik.values.nickname}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.nickname && formik.errors.nickname && (
                <Typography color="error" variant="body2">
                  {formik.errors.nickname}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.InputsContainer>
            <div>
              <S.LabelInput>
                ¿Qué género aparece en tu Identificación Oficial?*
              </S.LabelInput>
              <S.CheckboxContainer>
                <FormControlLabel
                  control={
                    <Checkbox
                      value="femenino"
                      checked={formik.values.gender === "femenino"}
                      onChange={handleGenderChange}
                      sx={{
                        color: "#10265F",
                        "&.Mui-checked": { color: "#10265F" },
                      }}
                    />
                  }
                  label="Femenino"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      value="masculino"
                      checked={formik.values.gender === "masculino"}
                      onChange={handleGenderChange}
                      sx={{
                        color: "#10265F",
                        "&.Mui-checked": { color: "#10265F" },
                      }}
                    />
                  }
                  label="Masculino"
                />
              </S.CheckboxContainer>
              {formik.touched.gender && formik.errors.gender && (
                <Typography color="error" variant="body2">
                  {formik.errors.gender}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.InputsContainer>
            <div>
              <S.LabelInput>¿Cuál es tu Calle?*</S.LabelInput>
              <S.InputStyles
                type="text"
                placeholder="Calle"
                required
                name="street"
                value={formik.values.street}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.street && formik.errors.street && (
                <Typography color="error" variant="body2">
                  {formik.errors.street}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>¿Cuál es tu número exterior?*</S.LabelInput>
              <S.InputStyles
                type="text"
                maxLength={16}
                placeholder="Número exterior"
                required
                name="exteriorNumber"
                value={formik.values.exteriorNumber}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  let value = e.target.value;
                  if (value.length > 16) {
                    value = value.slice(0, 16);
                  }
                  formik.setFieldValue(e.target.name, value);
                }}
                onBlur={formik.handleBlur}
              />
              {formik.touched.exteriorNumber &&
                formik.errors.exteriorNumber && (
                  <Typography color="error" variant="body2">
                    {formik.errors.exteriorNumber}
                  </Typography>
                )}
            </div>
          </S.InputsContainer>
          <S.InputsContainer>
            <div>
              <S.LabelInput>¿Cuál es tu número interior?</S.LabelInput>
              <S.InputStyles
                type="text"
                maxLength={16}
                placeholder="Número interior"
                name="interiorNumber"
                value={formik.values.interiorNumber}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  let value = e.target.value;
                  if (value.length > 16) {
                    value = value.slice(0, 16);
                  }
                  formik.setFieldValue(e.target.name, value);
                }}
                onBlur={formik.handleBlur}
              />
              {formik.touched.interiorNumber &&
                formik.errors.interiorNumber && (
                  <Typography color="error" variant="body2">
                    {formik.errors.interiorNumber}
                  </Typography>
                )}
            </div>
            <div>
              <S.LabelInput>¿Cuál es tu código postal?*</S.LabelInput>
              <S.InputStyles
                type="text"
                placeholder="Código postal"
                required
                name="postalCode"
                value={formik.values.postalCode}
                onChange={(e) => {
                  formik.handleChange(e);
                  console.log("pruebaaaaaaaaaaaa");
                }}
                onBlur={formik.handleBlur}
              />
              {formik.touched.postalCode && formik.errors.postalCode && (
                <Typography color="error" variant="body2">
                  {formik.errors.postalCode}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.InputsContainer>
            <div>
              <S.LabelInput>¿Cuál es tu colonia?*</S.LabelInput>
              <S.SelectStyles
                required
                name="neighborhood"
                value={formik.values.neighborhood}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                disabled={disabledInputs}
                defaultValue=""
                aria-placeholder="Selecciona una colonia"
                displayEmpty
                renderValue={(selected) =>
                  selected ? (
                    (selected as React.ReactNode)
                  ) : (
                    <span className="placeholder">Selecciona tu colonia</span>
                  )
                }
              >
                {formik.values.postalCode &&
                  formik.values.postalCode.length === 5 &&
                  data &&
                  data?.colonias?.map((item) => (
                    <MenuItem key={item} value={item}>
                      {item}
                    </MenuItem>
                  ))}
              </S.SelectStyles>
              {formik.touched.neighborhood && formik.errors.neighborhood && (
                <Typography color="error" variant="body2">
                  {formik.errors.neighborhood}
                </Typography>
              )}
            </div>
            <div>
              <S.LabelInput>¿Cuál es tu municipio?*</S.LabelInput>
              <S.InputStyles
                type="text"
                placeholder="Municipio"
                onChange={(e) => {
                  formik.handleChange(e);
                }}
                name="municipality"
                value={data?.municipio || ""}
                disabled
              />
              {formik.touched.municipality && formik.errors.municipality && (
                <Typography color="error" variant="body2">
                  {formik.errors.municipality}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.LabelInput>¿Cuál es tu email?*</S.LabelInput>
          <S.SmallLabel>
            Lo usaré para enviarte la información de tus seguros y otros
            detalles importantes.
          </S.SmallLabel>
          <S.InputsContainer>
            <div>
              <S.InputStyles
                type="email"
                placeholder="<EMAIL>"
                required
                name="email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.email && formik.errors.email && (
                <Typography color="error" variant="body2">
                  {formik.errors.email}
                </Typography>
              )}
            </div>
            <div>
              <S.InputStyles
                type="email"
                placeholder="Confirma tu email"
                required
                name="confirmEmail"
                value={formik.values.confirmEmail}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.confirmEmail && formik.errors.confirmEmail && (
                <Typography color="error" variant="body2">
                  {formik.errors.confirmEmail}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.LabelInput>Contraseña*</S.LabelInput>
          <S.InputsContainer>
            <div>
              <S.PasswordInput>
                <input
                  type={seePassword ? "text" : "password"}
                  placeholder="Contraseña"
                  required
                  name="password"
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {seePassword ? (
                  <Eye onClick={() => setSeePassword(!seePassword)} />
                ) : (
                  <EyeClosed onClick={() => setSeePassword(!seePassword)} />
                )}
              </S.PasswordInput>
              {formik.values.password.length > 0 && formik.errors.password && (
                <Typography color="error" variant="body2">
                  {formik.errors.password}
                </Typography>
              )}
            </div>
            <div>
              <S.PasswordInput>
                <input
                  type={seeConfirmPassword ? "text" : "password"}
                  placeholder="Confirma tu contraseña"
                  required
                  name="confirmPassword"
                  value={formik.values.confirmPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {seeConfirmPassword ? (
                  <Eye
                    onClick={() => setSeeConfirmPassword(!seeConfirmPassword)}
                  />
                ) : (
                  <EyeClosed
                    onClick={() => setSeeConfirmPassword(!seeConfirmPassword)}
                  />
                )}
              </S.PasswordInput>
              {formik.values.confirmPassword.length > 0 &&
                formik.errors.confirmPassword && (
                  <Typography color="error" variant="body2">
                    {formik.errors.confirmPassword}
                  </Typography>
                )}
            </div>
          </S.InputsContainer>
          <S.LabelInput>¿Cuál es tu número celular?*</S.LabelInput>
          <S.SmallLabel>
            Prometo no molestarte con mensajes innecesarios, sólo lo usaré para
            cosas importantes.
          </S.SmallLabel>
          <S.InputsContainer>
            <div>
              <S.InputPhoneStyles
                type="tel"
                placeholder="Número de celular"
                required
                name="phone"
                value={formik.values.phone}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.phone && formik.errors.phone && (
                <Typography color="error" variant="body2">
                  {formik.errors.phone}
                </Typography>
              )}
            </div>
            <div>
              <S.InputPhoneStyles
                type="tel"
                placeholder="Confirmar número de celular"
                required
                name="confirmPhone"
                value={formik.values.confirmPhone}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.confirmPhone && formik.errors.confirmPhone && (
                <Typography color="error" variant="body2">
                  {formik.errors.confirmPhone}
                </Typography>
              )}
            </div>
          </S.InputsContainer>
          <S.Container>
            <S.CheckboxContainer style={{ width: "50%", gap: "8px" }}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="termsAndConditions"
                    checked={formik.values.termsAndConditions}
                    onChange={formik.handleChange}
                    sx={{
                      color: "#10265F",
                      "&.Mui-checked": { color: "#10265F" },
                    }}
                  />
                }
                label={
                  <Typography fontSize={14} fontFamily="Poppins">
                    Acepto las{" "}
                    <span
                      style={{
                        color: "#10265F",
                        fontWeight: "bold",
                        cursor: "pointer",
                        textDecoration: "underline",
                      }}
                      onClick={() => setOpenTermsModal(true)}
                    >
                      Condiciones de uso
                    </span>{" "}
                    y el{" "}
                    <span
                      style={{
                        color: "#10265F",
                        fontWeight: "bold",
                        cursor: "pointer",
                        textDecoration: "underline",
                      }}
                      onClick={() => setOpenPrivacyModal(true)}
                    >
                      Aviso de privacidad
                    </span>{" "}
                    de Wiki Protección.
                  </Typography>
                }
              />
            </S.CheckboxContainer>

            {formik.touched.termsAndConditions &&
              formik.errors.termsAndConditions && (
                <Typography color="error" variant="body2">
                  {formik.errors.termsAndConditions}
                </Typography>
              )}

            <S.SubmitButton
              type="submit"
              disabled={!formik.isValid || !formik.dirty}
            >
              Continuar
            </S.SubmitButton>

            <S.SmallTextInfo>
              Completa toda la información con * para continuar
            </S.SmallTextInfo>
          </S.Container>
        </S.Form>
      </S.Container>
      <TermsModal
        isOpen={openTermsModal}
        onClose={() => setOpenTermsModal(false)}
      />
      <PrivacyModal
        isOpen={openPrivacyModal}
        onClose={() => setOpenPrivacyModal(false)}
      />
    </ProtectedLayout>
  );
}
