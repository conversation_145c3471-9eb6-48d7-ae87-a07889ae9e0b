import React from "react";
import * as S from "./security.styles";
import Link from "next/link";
const SecurityViewer = () => {
  return (
    <S.Container>
      <S.SettingBlock>
        <S.SectionTitle>Contraseña</S.SectionTitle>
        <S.TextBlock>
          <S.SectionDescription>
            Protege tu cuenta con una contraseña segura.
          </S.SectionDescription>
          <Link href="/profile/change-password">
            <S.ActionLink>Cambiar contraseña</S.ActionLink>
          </Link>
        </S.TextBlock>
      </S.SettingBlock>
      <S.SettingBlock>
        <S.SectionTitle>Eliminar cuenta</S.SectionTitle>
        <S.TextBlock>
          <S.SectionDescription>
            Eliminar cuenta de forma definitiva.
          </S.SectionDescription>
          <S.ActionLink>Eliminar cuenta</S.ActionLink>
        </S.TextBlock>
      </S.SettingBlock>
    </S.Container>
  );
};

export default SecurityViewer;
