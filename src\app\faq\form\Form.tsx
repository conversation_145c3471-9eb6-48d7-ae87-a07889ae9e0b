"use client";
import type { NextPage } from "next";
import { Box, Button } from "@mui/material";
import { useEffect, useState } from "react";
import styles from "./styles.module.css";
import InputForm from "./components/InputForm";
import { ContainerContacto } from "../contacto/ContainerContacto";
import { useIsLogged } from "@/app/LoginProvider";
import useAuth from "@/hooks/useAuth";
import { validateField } from "./utils/faqFormValidations";
import { sendFormData } from "@/infrastructure/services/faqService";
import CustomSnackbar from "./components/CustomSnackbar";
import { useSnackbar } from "@/hooks/faq/useSnackbar";

/**
 * Componente Form
 * Representa un formulario de contacto para que los usuarios envíen consultas o problemas.
 * Incluye validaciones de campos y soporte para usuarios autenticados y no autenticados.
 */
const Form: NextPage = () => {
  // Hook para verificar si el usuario está autenticado
  const { logged } = useIsLogged();
  // Hook para obtener información del usuario autenticado
  const { fetchUserInfo } = useAuth();
  // Hook para manejar el snackbar
  const {
    snackbarOpen,
    snackbarMessage,
    snackbarSeverity,
    showSnackbar,
    handleSnackbarClose,
  } = useSnackbar();

  // Estados para almacenar los valores de los campos del formulario
  const [name, setName] = useState("");
  const [firstName, setFirstName] = useState("");
  const [nickName, setNickName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [message, setMessage] = useState("");
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  });

  // Llama a fetchUserInfo solo si logged es true y actualiza los estados locales
  useEffect(() => {
    const fetchData = async () => {
      if (logged) {
        try {
          const userData = await fetchUserInfo(); // Obtener los datos del usuario
          const nameAndLastName =
            userData.firstName + " " + userData.lastNamePaternal;
          setName(nameAndLastName || ""); // Asegurar que los valores no sean undefined
          setEmail(userData.email || "");
          setPhone(userData.phone || "");
          setFirstName(userData.firstName || "");
          setNickName(userData.nickName || "");
        } catch (error) {
          console.error("Error al obtener los datos del usuario:", error);
        }
      }
    };

    fetchData();
  }, [logged]);

  /**
   * Maneja el evento de pérdida de foco en un campo del formulario.
   * Valida el campo y actualiza el estado de errores.
   * @param {string} field - Nombre del campo.
   * @param {string} value - Valor del campo.
   */
  const handleBlur = (field: string, value: string) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: validateField(field, value),
    }));
  };

  /**
   * Maneja el cambio de valor en un campo del formulario.
   * Actualiza el estado del campo correspondiente y valida el valor ingresado.
   * @param {string} field - Nombre del campo.
   * @param {string} value - Valor del campo.
   */
  const handleChange = (field: string, value: string) => {
    switch (field) {
      case "name":
        setName(value);
        break;
      case "email":
        setEmail(value);
        break;
      case "phone":
        const numericValue = value.replace(/[^0-9]/g, ""); // Filtra caracteres no numéricos
        setPhone(numericValue);
        break;
      case "message":
        setMessage(value);
        break;
    }

    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: validateField(field, value),
    }));
  };

  /**
   * Verifica si el formulario es válido.
   * @returns {boolean} - `true` si el formulario es válido, `false` en caso contrario.
   */
  const isFormValid = () => {
    return (
      name.trim() !== "" &&
      email.trim() !== "" &&
      phone.trim() !== "" &&
      message.trim() !== "" &&
      !errors.name &&
      !errors.email &&
      !errors.phone &&
      !errors.message
    );
  };

  /**
   * Maneja el envío del formulario.
   * Valida todos los campos y, si no hay errores, envía los datos.
   */
  const handleSubmit = async () => {
    const newErrors = {
      name: validateField("name", name),
      email: validateField("email", email),
      phone: validateField("phone", phone),
      message: validateField("message", message),
    };
    setErrors(newErrors);

    const hasErrors = Object.values(newErrors).some((error) => error !== "");
    if (!hasErrors) {
      const formData = {
        name,
        email,
        phone,
        message,
      };
      try {
        // Llama a sendFormData con el estado de autenticación
        await sendFormData(formData, logged);

        // Mostrar mensaje de éxito
        if (logged) {
          const nameOrNickName = nickName || firstName;
          showSnackbar(
            `Listo ${nameOrNickName} hemos recibido tus dudas, pronto nos comunicaremos contigo.`,
            "success"
          );
        } else {
          showSnackbar(
            `Recibimos tu consulta, pronto nos comunicaremos contigo.`,
            "success"
          );
        }

        // Si no esta logueado se borra name, email y phone
        if (!logged) {
          setName("");
          setEmail("");
          setPhone("");
        }
        setMessage("");
      } catch (error) {
        console.log("Error al enviar el formulario:", error);
        showSnackbar("Hubo un error al enviar el formulario.", "error");
      }
    }
  };

  return (
    <Box className={styles.formParent}>
      <Box className={styles.form}>
        <Box>
          <Box className={styles.label}>Contáctanos</Box>
        </Box>
        <Box className={styles.formFields}>
          <Box className={styles.formRow}>
            {!logged && (
              <>
                <InputForm
                  label="Nombre"
                  placeholder="Ingresa tu nombre"
                  value={name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  onBlur={() => handleBlur("name", name)}
                  error={!!errors.name}
                  helperText={errors.name}
                />
                <InputForm
                  label="Correo Electrónico"
                  placeholder="Ingresa tu correo electrónico"
                  value={email}
                  onChange={(e) => handleChange("email", e.target.value)}
                  onBlur={() => handleBlur("email", email)}
                  error={!!errors.email}
                  helperText={errors.email}
                />
                <InputForm
                  label="Número Celular"
                  placeholder="Ingresa tu número de teléfono"
                  value={phone}
                  onChange={(e) => handleChange("phone", e.target.value)}
                  onBlur={() => handleBlur("phone", phone)}
                  error={!!errors.phone}
                  helperText={errors.phone}
                  maxLength={10}
                />
              </>
            )}
            <Box className={styles.row}>
              <Box className={styles.containerTextArea}>
                <Box className={styles.label1}>
                  ¿Cuál es tu consulta
                  {logged ? ` ${nickName ? nickName : firstName}` : ""}?
                </Box>
                <textarea
                  className={styles.descripcion}
                  placeholder="Escribe tu consulta o problema"
                  rows={8}
                  value={message}
                  onChange={(e) => handleChange("message", e.target.value)}
                  onBlur={() => handleBlur("message", message)}
                />
              </Box>
              {errors.message && (
                <p className={styles.errorText}>{errors.message}</p>
              )}
            </Box>
          </Box>
          <Button
            className={styles.buttonContainer}
            disableElevation
            variant="contained"
            onClick={handleSubmit}
            disabled={!isFormValid()}
            sx={{
              textTransform: "none",
              color: "#fff",
              fontSize: "16",
              background: !isFormValid() ? "#ccc" : "#10265f",
              borderRadius: "20px",
              transition: "all 0.3s ease",
              "&:hover": {
                backgroundColor: !isFormValid() ? "#ccc" : "#9878b3",
                cursor: !isFormValid() ? "not-allowed" : "pointer",
              },
            }}
          >
            Enviar
          </Button>
          <CustomSnackbar
            open={snackbarOpen}
            message={snackbarMessage}
            severity={snackbarSeverity}
            onClose={handleSnackbarClose}
          />
        </Box>
      </Box>
      {/* Modulo de ayuda, enviar wahtsapp o llamada */}
      <ContainerContacto isLogged={logged} />
    </Box>
  );
};

export default Form;
