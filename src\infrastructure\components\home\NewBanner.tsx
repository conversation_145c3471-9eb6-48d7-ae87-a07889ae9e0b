'use client';
import { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { gsap } from 'gsap';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useBanners } from '@/hooks/useBanners';

const BannerContainer = styled.div`
  position: relative;
  width: 100%;
  height: 40vw;
  min-height: 150px;
  max-height: 450px;

  border-radius: 24px;
  overflow: hidden;

  cursor: pointer;
  background: #fff;
`;

const StyledImage = styled(Image).attrs({ fill: true })`
  object-fit: contain;
  object-position: center;
`;

const Paginator = styled.div`
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
`;

const Dot = styled.div<{ $active: boolean }>`
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: ${({ $active }) => ($active ? '#10265F' : '#ccc')};
  cursor: pointer;
  transition: background-color 0.3s ease;

  @media (max-width: 768px) {
    width: 12px;
    height: 12px;
  }
  @media (max-width: 450px) {
    width: 10px;
    height: 10px;
  }
`;
const NewBanner = () => {
  const { banners, loading, error } = useBanners();
  const [index, setIndex] = useState(0);
  const imgRefs = useRef<(HTMLImageElement | null)[]>([]);
  const router = useRouter();

  useEffect(() => {
    if (banners.length === 0) return;

    const fadeImages = () => {
      gsap.to(imgRefs.current[index], {
        opacity: 0,
        duration: 1,
        ease: 'power2.out',
      });

      const nextIndex = (index + 1) % banners.length;

      gsap.fromTo(imgRefs.current[nextIndex], { opacity: 0 }, { opacity: 1, duration: 1, ease: 'power2.out' });

      setIndex(nextIndex);
    };

    const interval = setInterval(fadeImages, 6000);
    return () => clearInterval(interval);
  }, [index, banners.length]);

  const handleDotClick = (i: number) => {
    if (i === index) return;

    gsap.to(imgRefs.current[index], {
      opacity: 0,
      duration: 1,
      ease: 'power2.out',
    });
    gsap.fromTo(imgRefs.current[i], { opacity: 0 }, { opacity: 1, duration: 1, ease: 'power2.out' });
    setIndex(i);
  };

  if (loading || banners.length === 0) {
    return (
      <BannerContainer>
        {/* Placeholder mientras se cargan los banners */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
            borderRadius: '24px',
          }}
        >
          <div
            style={{
              color: '#9e9e9e',
              fontSize: '16px',
              fontFamily: 'Poppins, sans-serif',
              textAlign: 'center',
            }}
          >
            {loading ? 'Cargando banners...' : error ? 'Error al cargar banners. Intenta recargar la página.' : 'No hay banners disponibles'}
          </div>
        </div>
      </BannerContainer>
    );
  }

  return (
    <BannerContainer onClick={() => router.push(banners[index].linkUrl ?? '/')}>
      {banners.map((banner, i) => (
        <StyledImage
          key={banner.id}
          ref={(el) => {
            imgRefs.current[i] = el;
          }}
          src={banner.url_banner}
          alt={`Banner ${i + 1}`}
          priority={i === 0}
          fill
          style={{ opacity: i === 0 ? 1 : 0 }}
        />
      ))}

      <Paginator>
        {banners.map((_, i) => (
          <Dot
            key={i}
            $active={i === index}
            onClick={(e) => {
              e.stopPropagation();
              handleDotClick(i);
            }}
          />
        ))}
      </Paginator>
    </BannerContainer>
  );
};

export default NewBanner;
