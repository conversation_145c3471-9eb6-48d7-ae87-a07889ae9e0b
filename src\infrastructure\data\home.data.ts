'use client'
import { bannerItem, MobileFooterProps, MainInformationProps } from '../models/home.model';
import prueba1 from "@/../public/web/img/pruebaBanner/prueba1.jpg";
import prueba2 from "@/../public/web/img/pruebaBanner/prueba2.jpg";
import prueba3 from "@/../public/web/img/pruebaBanner/prueba3.jpg";
import prueba4 from "@/../public/web/img/pruebaBanner/prueba4.jpg";


export const navigationItems: MobileFooterProps = {
  navigationItems: [
    { label: "Home", icon: "home", selectedIcon: "selectedHome" },
    {
      label: "Siniestros",
      icon: "siniestros",
      selectedIcon: "selectedSiniestros",
    },
    { label: "Productos", icon: "productos", selectedIcon: "selectedProducts" },
    { label: "Mi cuenta", icon: "cuenta", selectedIcon: "selectedAccount" },
  ],
};


export const slides: bannerItem = {
  item: [
    {
      title: "Los hackers no duermen... pero nosotros tampoco.",
      description:
        "Con nuestro seguro, tienes cobertura 24/7 contra hackeos, robo de identidad y más.",
      image: prueba1,
      link: {
        text: "Wiki Hackeo",
        url: "#",
        more: "Leer más",
      },
    },
    {
      title: "Protección continua para tu negocio",
      description:
        "Monitoreo en tiempo real y respuesta inmediata ante amenazas cibernéticas.",
        image: prueba2,
      link: {
        text: "Servicios",
        url: "#",
        more: "Leer más",
      },
    },
    {
      title: "Seguridad de próxima generación",
      description: "Tecnología avanzada para mantener tus datos seguros 24/7.",
      image: prueba3,
      link: {
        text: "Tecnología",
        url: "#",
        more: "Leer más",
      },
    }, {
      title: "Seguridad de próxima generación",
      description: "Tecnología avanzada para mantener tus datos seguros 24/7.",
      image: prueba4,
      link: {
        text: "Tecnología",
        url: "#",
        more: "Leer más",
      },
    },
  
    
  ]

};

export const informationItems: MainInformationProps = {
  informationItems: [
    {
      title: "Selecciona tu seguro",
      text: "Desde tu cel hasta esa bici que amas. Aquí tú mandas",
      icon: "select",
      backIcon: "leftPalm",
    },
    {
      title: "Elige tu plan",
      text: "¿Algo tranquis o full cobertura? Tú sabes qué plan vibra más contigo",
      icon: "choose",
      backIcon: "tree",
    },
    {
      title: "Emite y paga",
      text: "¡Ya quedo! Nómas dale pagar, y tu póliza vuela directo a tu correo",
      icon: "pay",
      backIcon: "rightPalm",
    },
  ],
};
