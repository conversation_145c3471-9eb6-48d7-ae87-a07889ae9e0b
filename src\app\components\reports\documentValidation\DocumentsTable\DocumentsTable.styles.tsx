import styled from "styled-components";

export const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  background: white;
  border-radius: 0 0 12px 12px;
`;

export const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

export const TableHeader = styled.thead`
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
`;

export const HeaderRow = styled.tr`
  border-bottom: 1px solid #e5e7eb;
`;

export const HeaderCell = styled.th`
  padding: 16px 24px;
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
  font-family: Poppins, sans-serif;

  &:first-child {
    width: 15%;
  }

  &:nth-child(2) {
    width: 35%;
  }

  &:nth-child(3) {
    width: 20%;
  }

  &:nth-child(4) {
    width: 30%;
  }
`;

export const TableBody = styled.tbody`
  background: white;
`;

export const DataRow = styled.tr`
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8fafc;
  }

  &:last-child {
    border-bottom: none;
  }
`;

export const DataCell = styled.td`
  padding: 20px 24px;
  vertical-align: middle;
  font-family: Poppins, sans-serif;
  font-size: 14px;
  color: #374151;
`;

export const ProductName = styled.div`
  font-weight: 500;
  color: #1e293b;
`;

export const DocumentName = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const DocumentIcon = styled.div<{ $status: string }>`
  display: flex;
  align-items: center;
  justify-content: center;

  ${(props) => {
    switch (props.$status) {
      case "Aprobado":
        return `color: #3BBE30;`;
      case "Rechazado":
        return `color: #D54747;`;
      default:
        return `color: #E08E12;`;
    }
  }}
`;

export const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
`;

export const ActionButton = styled.button<{
  $variant?: "secondary" | "danger";
}>`
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-family: Poppins, sans-serif;
  background: white;

  ${(props) => {
    switch (props.$variant) {
      case "danger":
        return `
          background-color: rgba(213, 71, 71, 0.1);
          color: #D54747;
          border: none;
          &:hover {
            background-color: #fef2f2;
          }
        `;
      default:
        return `
          background-color: white;
          color: #374151;
          border: 1px solid;
          border-color: #d1d5db;
          
          &:hover {
            background-color: #f9fafb;
          }
        `;
    }
  }}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export const ExpandedRow = styled.tr`
  background-color: #fef2f2;
`;

export const ExpandedCell = styled.td`
  padding: 0;
  border-bottom: 1px solid #f1f5f9;
`;

export const RejectReasonCard = styled.div`
  background-color: #fef2f2;
  margin: 16px 24px;
  border-radius: 8px;
`;

export const RejectReasonHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
`;

export const RejectReasonIcon = styled.div`
  color: #dc2626;
  display: flex;
  align-items: center;
`;

export const RejectReasonTitle = styled.div`
  font-weight: 600;
  color: #991b1b;
  font-size: 14px;
  font-family: Poppins, sans-serif;
`;

export const RejectReasonText = styled.div`
  color: #dc2626;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  font-family: Poppins, sans-serif;
`;

export const UploadSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #fecaca;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
`;

export const UploadText = styled.div`
  font-size: 12px;
  color: #6b7280;
  font-family: Poppins, sans-serif;
`;

export const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: white;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: Poppins, sans-serif;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 18px rgba(0, 0, 0, 0.12);
  }
`;

export const EmptyState = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background: white;
  border-radius: 0 0 12px 12px;
  color: #6b7280;
  font-family: Poppins, sans-serif;
`;
