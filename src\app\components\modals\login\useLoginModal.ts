import { useState, useCallback } from "react";

const useLoginModal = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = useCallback(() => {
    setIsModalOpen(true);
  }, []);
  const closeModal = useCallback(() => setIsModalOpen(false), []);

  return {
    isModalOpen,
    openModal,
    closeModal,
    setIsModalOpen, // En caso de necesitar acceso directo
  };
};

export default useLoginModal;
