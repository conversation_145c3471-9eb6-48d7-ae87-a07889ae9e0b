'use client';
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from '@/core/axios';
import { APIProduct } from './product/apiProducts.types';
import { usePathname } from 'next/navigation';

export interface ProductsCards {
  dataCards: {
    id: string;
    name: string;
    description: string;
    descriptionMobile: string;
    price?: string;
    image: string;
    imageHover: string;
    path: string;
    color: string;
    boxShadow?: string;
  }[];
}

interface ProductList {
  id: number;
  title: string;
}

// Definimos el tipo de nuestro contexto
interface ProductsContextType {
  products: ProductsCards | null;
  productList: ProductList[] | null;
  loading: boolean;
  error: string | null;
  getProductById: (id: number) => APIProduct | undefined;
  fetchProducts: () => void;
}

// Creamos el contexto
const ProductsContext = createContext<ProductsContextType | undefined>(undefined);

// Proveedor del estado global
export const ProductsProvider = ({ children }: { children: ReactNode }) => {
  const [rawProducts, setRawProducts] = useState<APIProduct[]>([]);
  const [products, setProducts] = useState<ProductsCards | null>(null);
  const [productList, setProductList] = useState<ProductList[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await axios.get('v1/products/products/active');
      const apiResponse = Array.isArray(response.data) ? response.data : [];
      setRawProducts(apiResponse);
      // Adaptamos los datos
      const productsAdapted: ProductsCards = {
        dataCards: apiResponse.map((product: APIProduct) => ({
          id: product.id.toString(),
          name: product.name,
          description: product.coverageDescription || product.generalCoverage,
          descriptionMobile: product.subtitle || product.generalCoverage || '',
          image: product.mainImage || '',
          imageHover: product.secondaryImage || '',
          path: `/products/${product.id}`,
          color: product.color || '#10265f',
        })),
      };

      const productListAdapted = apiResponse.map((product: APIProduct) => ({
        id: product.id,
        title: product.name,
      }));

      setProducts(productsAdapted);
      setProductList(productListAdapted);
    } catch (err) {
      setError('Error al cargar productos');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const path = usePathname();
  useEffect(() => {
    fetchProducts();
  }, [path]);

  useEffect(() => {
    console.log(loading, 'loading');
  }, [loading]);

  const getProductById = (id: number) => {
    const product = rawProducts?.find((product) => product.id === Number(id));
    return product;
  };

  return (
    <ProductsContext.Provider
      value={{
        products,
        productList,
        loading,
        error,
        getProductById,
        fetchProducts,
      }}
    >
      {children}
    </ProductsContext.Provider>
  );
};

// Hook para usar el contexto en cualquier parte de la app
export const useProducts = () => {
  const context = useContext(ProductsContext);
  if (!context) {
    throw new Error('useProducts debe usarse dentro de ProductsProvider');
  }
  return context;
};
