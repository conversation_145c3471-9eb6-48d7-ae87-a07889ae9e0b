.claimsInfo,
.serviceContent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.label,
.label1 {
  position: relative;
  font-weight: 600;
}
.label {
  align-self: stretch;
}
.label1 {
  width: 353px;
  display: inline-block;
}
.input {
  border: 0;
  background-color: transparent;
  align-self: stretch;
  font-family: Poppins;
  font-size: 14px;
  color: #afafaf;
}
.form1,
.formParent,
.serviceContentParent {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  max-width: 100%;
}
.formParent {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  font-size: 24px;
  height: fit-content;
}
.form,
.formFields {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}
.formFields {
  align-self: stretch;
  justify-content: flex-start;
  font-size: 18px;
}
.form {
  width: 563px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #fff;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}
.containerTextArea {
  flex: 1;
  flex-direction: column;
  gap: 10px;
}

.containerTextArea,
.descripcion,
.formRow,
.row {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.row {
  align-self: stretch;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: flex-start;
}
.descripcion,
.formRow {
  flex-direction: column;
}
.descripcion {
  border: 1px solid #10265f;
  background-color: #fff;
  height: 168px;
  width: auto;
  outline: 0;
  align-self: stretch;
  border-radius: 20px;
  box-sizing: border-box;
  padding: 25px 30px;
  font-family: Poppins;
  font-size: 15px;
  color: black;
  resize: none;
  overflow-y: scroll;
  scrollbar-width: none;
}
.descripcion::-webkit-scrollbar {
  display: none;
}
.descripcion:focus {
  border: 2px solid #0068cf;
}
.errorText {
  font-family: "Roboto", Helvetica, Arial, sans-serif, serif;
  font-weight: 400;
  color: #d32f2f;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  text-align: left;
}
.formRow {
  padding: 0 10px;
  gap: 20px;
}
.buttonContainer,
.formRow {
  align-self: stretch;
  padding: 13px;
  font-family: Poppins;
}
.buttonContainer {
  font-size: 15px;
  font-weight: 300;
}
.text1,
.textParent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
}
.textParent {
  align-items: flex-start;
  justify-content: flex-start;
  gap: 10px;
}
@media screen and (max-width: 1200px) {
  .formParent {
    flex: 1;
  }
  .serviceContentParent {
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 750px) {
  .formParent {
    min-width: 100%;
  }
  .form {
    width: 100%;
    padding: 15px;
  }
  .formFields {
    width: 100%;
  }
  .formRow {
    flex-direction: column;
    width: 100%;
  }
  .input {
    width: 100%;
  }
  .buttonContainer {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

@media screen and (max-width: 450px) {
  .label {
    font-size: 19px;
  }
  .label1 {
    width: 100%;
    font-size: 16px;
  }
  .input {
    font-size: 12px;
  }
  .buttonContainer {
    padding: 10px;
  }
  .form {
    padding: 10px;
  }
  .descripcion {
    padding: 15px 20px;
  }
}
