import styled from "styled-components";

export const TableWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  padding: 0 70px;
`;

export const Table = styled.table`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 6px;
  border: 1px solid #eee;
  border-radius: 10px;
  align-self: stretch;
  width: 100%;

  @media (max-width: 980px) {
    align-self: auto;
  }
`;

export const TableHead = styled.thead<{ $headerBgColor?: string; $headerText?: boolean; }>`
  background-color: ${props => props.$headerBgColor || '#5959a3'};
  color: ${props => props.$headerText ? '#6b7280' : 'white'};
  display: flex;
  align-items: flex-start;
  align-self: stretch;
  border-radius: ${props => props.$headerText ? '0px' : '10px 10px 0px 0px'};
  width: 100%;
`;

export const TableHeaderCell = styled.th<{ $headerBgColor?: string; $headerText?: boolean; $contentAlignment?: 'center' | 'left' | 'right' }>`
  display: flex;
  flex-direction: row;
  justify-content: ${props => {
    switch (props.$contentAlignment) {
      case 'left': return 'flex-start';
      case 'right': return 'flex-end';
      default: return 'center';
    }
  }};
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  padding: 10px 12px;
  border-radius: 10px;
  background: ${props => props.$headerBgColor || '#5959a3'};
  color: ${props => props.$headerText ? '#6b7280' : 'white'};
  text-align: ${props => props.$contentAlignment || 'center'};
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: ${props => props.$headerText ? '400' : '700'};
  line-height: normal;

  @media (max-width: 980px) {
    font-size: 14px;
  }
`;

export const TableBody = styled.tbody`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 100%;
`;

export const TableRow = styled.tr`
  display: flex;
  align-items: flex-start;
  align-self: stretch;
  color: #6d6d6d;
  width: 100%;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: rgba(89, 89, 163, 0.05);
  }
`;

export const TableCell = styled.td<{ $contentAlignment?: 'center' | 'left' | 'right' }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: ${props => {
    switch (props.$contentAlignment) {
      case 'left': return 'flex-start';
      case 'right': return 'flex-end';
      default: return 'center';
    }
  }};
  flex: 1 0 0;
  align-self: stretch;
  border-radius: 10px;
  padding: 10px 12px;
  color: #6d6d6d;
  text-align: ${props => props.$contentAlignment || 'center'};
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  @media (max-width: 980px) {
    font-size: 14px;
  }
`;
