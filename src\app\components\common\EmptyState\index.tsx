"use client"

import styled from "styled-components"
import React from "react"
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined"

interface Props {
  message: string
  iconSize?: number
}

const EmptyStateWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6d6d6d;
  text-align: center;
  padding: 32px 16px;
  gap: 12px;
`

const EmptyMessage = styled.p`
  font-size: 16px;
  font-weight: 500;
  max-width: 420px;
  line-height: 1.4;
`

const EmptyState: React.FC<Props> = ({ message, iconSize = 48 }) => {
  return (
    <EmptyStateWrapper>
      <InfoOutlinedIcon style={{ fontSize: iconSize, color: "#9e9e9e" }} />
      <EmptyMessage>{message}</EmptyMessage>
    </EmptyStateWrapper>
  )
}

export default EmptyState
