import { NavigationService } from "@/infrastructure/services/NavigationService";

/**
 * Clase que representa el caso de uso para navegar a una categoría específica.
 * 
 * @class
 * @implements {NavigateToCategory}
 * 
 * @example
 * const navigateToCategory = new NavigateToCategory(navigationService);
 * navigateToCategory.execute('electronics');
 * 
 * @param {NavigationService} navigationService - Servicio de navegación utilizado para realizar la navegación.
 */
export class NavigateToCategory {
    constructor(private readonly navigationService: NavigationService) {}
    execute(category: string): void {
        this.navigationService.navigate(`/products/${category}`);
    }

    
}
