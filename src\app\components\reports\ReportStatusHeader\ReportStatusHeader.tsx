"use client";
import React from "react";
import { ReportStatus } from "@/types";
import * as S from "./ReportStatusHeader.styles";
import {
  DocumentIcon,
  FiniquitoIcon,
  PagoIcon,
  FinalizadoIcon,
} from "../../common/Icons";
import { ArrowBack } from "@mui/icons-material";
import IconWrapper from "../../common/IconWrapper";

interface ReportStatusHeaderProps {
  status: ReportStatus;
  onBackClick: () => void;
}

interface StatusConfig {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
}

const getStatusConfig = (status: ReportStatus): StatusConfig => {
  switch (status) {
    case "Documentos":
      return {
        icon: <DocumentIcon />,
        title: "Documentos",
        subtitle:
          "Sigue el progreso de tu reclamación y gestiona los documentos necesarios",
      };
    case "Finiquito":
      return {
        icon: <FiniquitoIcon />,
        title: "Propuesta de Finiquito",
        subtitle: "Revisa y acepta la propuesta de finiquito de tu reclamación",
      };
    case "Pago":
      return {
        icon: <PagoIcon />,
        title: "Procesando Pago",
        subtitle:
          "Tu reclamación está siendo procesada para el pago correspondiente",
      };
    case "Finalizado":
      return {
        icon: <FinalizadoIcon />,
        title: "Fin de Reclamación",
        subtitle: "Tu reclamación ha sido completada exitosamente",
      };
    default:
      return {
        icon: <DocumentIcon />,
        title: "Documentos",
        subtitle:
          "Sigue el progreso de tu reclamación y gestiona los documentos necesarios",
      };
  }
};

const ReportStatusHeader: React.FC<ReportStatusHeaderProps> = ({
  status,
  onBackClick,
}) => {
  const statusConfig = getStatusConfig(status);

  return (
    <S.Container>
      <S.TitleContainer>
        <S.BackButton onClick={onBackClick}>
          <ArrowBack sx={{ fontSize: 20 }} />
        </S.BackButton>
        <S.TitleContent>
          <S.Title>
            <IconWrapper
              icon={statusConfig.icon}
              title={`${statusConfig.title}`}
              noBackground
            />
            {statusConfig.title}
          </S.Title>
          <S.Subtitle>{statusConfig.subtitle}</S.Subtitle>
        </S.TitleContent>
      </S.TitleContainer>
    </S.Container>
  );
};

export default ReportStatusHeader;
