export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  firstName: string;
  lastNamePaternal: string;
  lastNameMaternal: string;
  birthDate: string;
  nickname: string;
  gender: string;
  street: string;
  exteriorNumber: string;
  interiorNumber?: string;
  postalCode: string;
  neighborhood: string;
  municipality: string;
  phone: string;
}

export interface SendOtpCodeDto {
  email: string;
  phone: string;
  acction: string;
}

export interface UnverifiedAccountError {
  message: string;
  code: string;
  uuid?: string;
  userInfo: {
    email?: string;
    phone?: string;
    firstName?: string;
    nickname?: string;
  };
}

export interface CompleteOtpDto {
  uuid: string;
  channel: 'email' | 'sms';
}

export interface RegisterErrorResponse {
  message: string[];
  inUse: {
    email: boolean;
    phone: boolean;
  };
}
