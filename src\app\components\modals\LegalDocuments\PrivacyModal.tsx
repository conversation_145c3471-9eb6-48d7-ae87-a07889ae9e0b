"use client";
import React, { useEffect } from "react";
import {
  Overlay,
  Modal,
  CloseButton,
  Title,
  FooterButton,
} from "./LegalModal.styles";
import { useLegalDocuments } from "@/hooks/useLegalDocuments";
import PrivacyContent from "./PrivacyContent";

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

interface PrivacySection {
  id: string | number;
  tituloDeSeccion?: string;
  contenidoDeSeccion: string;
}

const PrivacyModal: React.FC<Props> = ({ isOpen, onClose }) => {
  const { privacySections = [], loading } = useLegalDocuments(isOpen) as {
    privacySections: PrivacySection[];
    loading: boolean;
  };

  // Bloquear scroll del fondo cuando el modal está abierto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <Overlay>
      <Modal>
        <CloseButton onClick={onClose}>×</CloseButton>
        <Title>Aviso de Privacidad</Title>

        <PrivacyContent privacySections={privacySections} loading={loading} />
        <FooterButton onClick={onClose}>De acuerdo</FooterButton>
      </Modal>
    </Overlay>
  );
};

export default PrivacyModal;
