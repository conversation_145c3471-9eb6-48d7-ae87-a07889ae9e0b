export const getEquivalentColor = (hex: string): string => {
  // Convertir hexadecimal a valores R, G, B
  if (hex) {
    let r = parseInt(hex.slice(1, 3), 16);
    let g = parseInt(hex.slice(3, 5), 16);
    let b = parseInt(hex.slice(5, 7), 16);

    // Aplicar la transformación
    r = Math.max(0, r - 35);
    g = Math.max(0, g - 35);
    b = Math.max(0, b - 35);

    // Convertir de vuelta a hexadecimal
    const newHex = `#${r.toString(16).padStart(2, "0")}${g
      .toString(16)
      .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;

    return newHex.toUpperCase();
  } else return "#000000";
};
