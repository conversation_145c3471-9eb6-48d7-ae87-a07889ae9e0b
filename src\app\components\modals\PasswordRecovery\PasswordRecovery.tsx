import { Modal } from "@mui/material";
import { FunctionComponent, useState } from "react";
import ResetPassword from "./Steps/ResetPassword";
import { useResetPassword } from "@/hooks/PasswordRecovery/useResetPassword";
import {
  StyledContainer,
  StyledLayout,
} from "./Styles/RecoveryPassword.styles";
import SelectMethod from "./Steps/SelectMethod";
import SMS from "./Steps/Methods/SMS";
import Email from "./Steps/Methods/Email";
import Verify from "./Steps/Verify";
import VerifyEmail from "./Steps/VerifyEmail";
import CloseIcon from "@mui/icons-material/Close";

type Props = {
  handleClose: () => void;
  open: boolean;
};

const PasswordRecovery: FunctionComponent<Props> = ({ handleClose, open }) => {
  const [nextStep, setNextStep] = useState<string | null>("selectMethod");
  const [otpInfo, setOtpInfo] = useState<{
    phone: string;
  }>({ phone: "" });
  const [otpEmailInfo, setOtpEmailInfo] = useState<{
    email: string;
  }>({ email: "" });
  const { otpCode, otpCodeEmail, verifyOtpCode, error, verifyEmailOtpCode } =
    useResetPassword();
  const [otpSent, setOtpSent] = useState<string>("");

  const [uuid, setUuid] = useState<number>(-1);

  const onClose = () => {
    handleClose();
  };

  const handleNextStep = (
    step:
      | "selectMethod"
      | "sms"
      | "mail"
      | "verify"
      | "verifyEmail"
      | "resetPassword"
  ) => {
    setNextStep(step);
  };

  // Esquema de validación para el número de teléfono

  const handleSendVerificationCode = async (params: { phone: string }) => {
    setOtpInfo(params);
    const response = await otpCode(params);
    if (response.message === "Código enviado correctamente") {
      setNextStep("verify");
      console.log("Código enviado correctamente", response);
    }
  };

  const handleSendVerificationCodeEmail = async (params: { email: string }) => {
    setOtpEmailInfo(params);
    const response = await otpCode(params);
    if (response.message === "Código enviado correctamente") {
      setNextStep("verify");
      console.log("Código enviado correctamente", response);
    }
  };

  const handleVerifyOtpCode = async () => {
    const response = await verifyOtpCode(otpInfo, otpSent);
    if (response.uuid) {
      console.log("Código verificado correctamente");
      console.log(response);
      setNextStep("resetPassword");
      setUuid(response.uuid);
    }
  };

  const handleVerifyEmailOtpCode = async () => {
    const response = await verifyEmailOtpCode(otpEmailInfo, otpSent);
    if (response.uuid) {
      console.log("Código verificado correctamente");
      console.log(response);
      setNextStep("resetPassword");
      setUuid(response.uuid);
    }
  };

  const handleSendVerificationCodeMail = async (params: { email: string }) => {
    setOtpEmailInfo(params);
    const response = await otpCodeEmail(params);
    console.log("response", response);
    if (response.message === "Codigo enviado correctamente") {
      setNextStep("verifyEmail");
      console.log("Código enviado correctamente", response);
    }
  };

  const RecoveryModalContent = () => {
    switch (nextStep) {
      case "selectMethod":
        return (
          <>
            <SelectMethod handleNextStep={handleNextStep} />
          </>
        );
      case "sms":
        return (
          <>
            <SMS
              handleSendVerificationCode={handleSendVerificationCode}
              error={error}
            />
          </>
        );
      case "mail":
        return (
          <>
            <Email
              handleSendVerificationCode={handleSendVerificationCodeMail}
              error={error}
            />
          </>
        );
      case "verify":
        return (
          <Verify
            handleSendVerificationCode={handleSendVerificationCode}
            error={error}
            setNextStep={setNextStep}
            setOtpSent={setOtpSent}
            handleVerifyOtpCode={handleVerifyOtpCode}
            otpInfo={otpInfo}
          />
        );

      case "verifyEmail":
        return (
          <VerifyEmail
            handleSendVerificationCode={handleSendVerificationCodeEmail}
            error={error}
            setNextStep={setNextStep}
            setOtpSent={setOtpSent}
            handleVerifyOtpCode={handleVerifyEmailOtpCode}
            otpInfo={otpEmailInfo}
          />
        );
      case "resetPassword":
        return (
          <>
            <ResetPassword uuid={uuid} handleClose={handleClose} />
          </>
        );
      default: {
        return <></>;
      }
    }
  };
  return (
    <>
      <Modal open={open} onClose={onClose}>
        <StyledLayout>
          <StyledContainer>
            <button onClick={onClose} style={{ cursor: "pointer" }}>
              <CloseIcon
                style={{ position: "absolute", top: "20px", right: "20px" }}
              />
            </button>
            {RecoveryModalContent()}
          </StyledContainer>
        </StyledLayout>
      </Modal>
    </>
  );
};

export default PasswordRecovery;
