"use client";
import styled from "styled-components";
import { keyframes } from "styled-components";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

const NotificationContent = () => {
  return (
    <>
      {/* //todo: agregar onClick open new tab to open de appstore */}
      <MarqueeContainer onClick={() => console.log("click")}>
        <StyledMarquee>
          <div>
            Estás a un clic de vivir tranquis
            <ArrowForwardIcon style={{ margin: "0 8px" }} />
            Descarga nuestra App ya.
          </div>
        </StyledMarquee>
      </MarqueeContainer>
    </>
  );
};
export default NotificationContent;

const slideIn = keyframes`
  0% {
    height: 0;
  }
  
  66% {
    height: 0;
  }
  100%{
    height: 52px;
  }
`;

const marquee = keyframes`
  from{
    transform: translateX(60%);
  }

  to{
    transform: translateX(-60%);
  }
`;

const StyledMarquee = styled.div`
  height: 52px;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  @media (max-width: 1024px) {
    display: inline-block;
    mask: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 1) 6%,
      rgba(0, 0, 0, 1) 94%,
      rgba(0, 0, 0, 0) 100%
    );
  }

  div {
    display: flex;
    justify-content: center;
    height: 100%;
    align-items: center;
    font-family: "Poppins", sans-serif;
    color: white;
    font-size: 1.5rem;
    width: fit-content;
    @media (max-width: 1024px) {
      animation: ${marquee} 10s linear infinite;
      justify-content: start;
      padding-right: 100%;
    }
  }
`;

const MarqueeContainer = styled.div`
  animation: ${slideIn} 1.5s forwards;
  background-color: rgba(134, 196, 152, 1);
`;
