"use client";

import styled from "styled-components";
import ReporteCard from "./ReporteCard";
import { useEffect, useState } from "react";
import useAuth from "@/hooks/useAuth";

type SelectReporteProps = {
  onFinish: () => void;
};

export default function SelectReporte({ onFinish }: SelectReporteProps) {
  const [selectedReporte, setSelectedReporte] = useState<string | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [myProducts, setMyProducts] = useState<any[]>([]);
  const { getMyProducts } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const userData = await getMyProducts();
        console.log("datadata", userData);
        setMyProducts(userData);
      } catch (error) {
        console.error("Error al obtener los datos del usuario:", error);
      }
    };
    fetchData();
  }, []);

  // const ReporteData = [
  //   {
  //     id: "1",
  //     title: "Wiki Asalto",
  //     noPoliza: "123456789",
  //     estatus: "Activo",
  //     periodo: "01/01/2023 - 31/12/2023",
  //     sumaAsegurada: "$100,000",
  //     remanenteSumaAsegurada: "$50,000",
  //     color: "#3D7E97",
  //     options: [
  //       { id: "1", title: "Opción 1" },
  //       { id: "2", title: "Opción 2" },
  //     ],
  //   },
  //   {
  //     id: "2",
  //     title: "Wiki Robo",
  //     noPoliza: "987654321",
  //     estatus: "Inactivo",
  //     periodo: "01/01/2022 - 31/12/2022",
  //     sumaAsegurada: "$50,000",
  //     remanenteSumaAsegurada: "$20,000",
  //     color: "#8C699D",
  //     options: [
  //       { id: "1", title: "Opción 1" },
  //       { id: "2", title: "Opción 2" },
  //     ],
  //   },
  // ];

  return (
    <StyledLayout>
      <div className="containerMain">
        {!selectedReporte && (
          <>
            <StyledTitle>No te preocupes, todo va a estar bien!</StyledTitle>
            <StyledSubtitle>
              Cuéntanos, ¿qué tipo de siniestro necesitas reportar? Estamos aquí
              para hacerlo rápido y fácil.
            </StyledSubtitle>
          </>
        )}
        <ContentConatiner>
          {selectedReporte === null &&
            Array.isArray(myProducts) &&
            myProducts.map((reporte) => (
              <ReporteCard
                key={reporte.id}
                selected={selectedReporte === reporte.id}
                onClick={() => setSelectedReporte(reporte.id)}
                data={reporte}
                onFinish={onFinish}
              />
            ))}
          {selectedReporte !== null && (
            <ReporteCard
              key={selectedReporte}
              selected={true}
              onClick={() => setSelectedReporte(null)}
              data={
                myProducts.find((reporte) => reporte.id === selectedReporte) ||
                myProducts[0]
              }
              onFinish={onFinish}
            />
          )}
        </ContentConatiner>
      </div>
    </StyledLayout>
  );
}

const StyledLayout = styled.div`
  background-color: #fdfaff;
  width: 100%;
  min-height: 70vh;

  .containerMain {
    display: flex;
    flex-direction: column;
    gap: 21px;
    width: 100%;
    align-items: center;
    justify-content: start;
    max-width: 120rem;
    margin: 0 auto;
    padding: 46px 60px;
  }
`;

const ContentConatiner = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  padding: 10px 0;
`;

const StyledTitle = styled.h2`
  color: var(--Gris-Obscuro, #333);
  text-align: center;

  /* Wiki/Desktop/H2 */
  font-family: Poppins;
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
`;

const StyledSubtitle = styled.h2`
  color: var(--Gris-Obscuro, #333);
  text-align: center;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;
