import { Box } from "@mui/material";
import React from "react";
import styles from "./styles.module.css";
import { handlePhoneClick, handleWhatsAppClick } from "./utils/contactUtils";
import { ContactButton } from "./contactButton/ContactButton";
import { useSnackbar } from "@/hooks/faq/useSnackbar";
import CustomSnackbar from "../form/components/CustomSnackbar";
import { useIsLogged } from "@/app/LoginProvider";

const iconPhone = "/web/img/phone.svg";
const iconWhatsApp = "/web/img/whatsapp.svg";

/**
 * Componente ContainerContacto
 * Renderiza una sección de contacto con botones para WhatsApp y Teléfono.
 */
interface ContainerContactoProps {
  isLogged: boolean;
}
export const ContainerContacto = ({ isLogged }: ContainerContactoProps) => {
  const { user } = useIsLogged();
  const {
    snackbarOpen,
    snackbarMessage,
    snackbarSeverity,
    showSnackbar,
    handleSnackbarClose,
  } = useSnackbar();
  return (
    <Box className={styles.contactContainer}>
      <Box className={styles.textSection}>
        <Box className={styles.contactTitleWrapper}>
          <Box className={styles.contactTitle}>Escríbenos</Box>
        </Box>
        <Box className={styles.contactDescription}>
          <Box className={styles.text}>
            <Box className={styles.contactSubtext}>
              ¿Prefieres hablar con alguien? Utiliza otro de nuestros servicios
            </Box>
          </Box>
        </Box>
      </Box>
      <Box className={styles.buttonGroup}>
        {/* Botón de WhatsApp */}
        <ContactButton
          onClick={() =>
            handleWhatsAppClick(
              isLogged,
              user?.nickname ? user.nickname : user?.firstName
            )
          }
          iconSrc={iconWhatsApp}
          label="WhatsApp"
          iconAlt="WhatsApp Icon"
        />
        {/* Botón de Teléfono */}
        <ContactButton
          onClick={() => handlePhoneClick(showSnackbar, isLogged)}
          iconSrc={iconPhone}
          label="Teléfono"
          iconAlt="Phone Icon"
        />
      </Box>
      <CustomSnackbar
        open={snackbarOpen}
        message={snackbarMessage}
        severity={snackbarSeverity}
        onClose={handleSnackbarClose}
      />
    </Box>
  );
};
