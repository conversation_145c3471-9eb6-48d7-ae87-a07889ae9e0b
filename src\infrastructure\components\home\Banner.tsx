"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Container,
  // Typo<PERSON>,
  IconButton,
} from "@mui/material";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import styles from "../../styles/banner.module.css";
import { bannerItem } from "@/infrastructure/models/home.model";
import { slides } from "@/infrastructure/data/home.data";
// import Cotizar from "@/../public/web/img/banner/btnCalendar.svg";
import Image from "next/image";
import BannerFigma from "./BannerFigma";
// import Destello from "@/../public/web/img/banner/destello.svg";

export const Banner: React.FC = () => {
  const { item }: bannerItem = slides;
  const [currentSlide, setCurrentSlide] = React.useState(0);

  const nextSlide = React.useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % (item.length + 1));
  }, [item.length]);

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + (item.length + 1)) % (item.length + 1)
    );
  };

  React.useEffect(() => {
    const timer = setInterval(nextSlide, 5000);
    return () => clearInterval(timer);
  }, [nextSlide]);

  return (
    <div className="relative w-full max-w-5xl mx-auto px-4 py-6">
      <Card className={styles.securityBanner}>
        <div className={styles.slideContainer}>
          {item.map((slide, index) => (
            <div
              key={index}
              className={styles.slide}
              style={{
                transform: `translateX(${(index - currentSlide) * 100}%)`,
              }}
            >
              <Container className={styles.slideContent}>
                {/* <div className={styles.slideGrid}>
                  <div className={styles.slideText}>
                    <div className={styles.slideLink}>
                      <Image
                        src={Destello}
                        alt="destello"
                        className={styles.sparkles}
                      />
                      <Typography
                        component="a"
                        href={slide.link.url}
                        className={styles.slideLink}
                      >
                        {slide.link.text}. <u>{slide.link.more}</u>
                      </Typography>
                    </div>
                    <Typography variant="h2" className={styles.slideTitle}>
                      {slide.title}
                    </Typography>
                    <Typography
                      variant="body1"
                      className={styles.slideDescription}
                    >
                      {slide.description}
                    </Typography>
                    <div style={{ width: "50%", position: "relative" }}>
                      <Button className={styles.btnCotizarSlider}>
                        <Image
                          className={styles.imgCotizar}
                          src={Cotizar}
                          alt="calendar"
                          sizes="100%"
                        />
                        <span style={{ flex: 1, textAlign: "center" }}>
                          Cotizar Ahora
                        </span>
                        <Image
                          className={styles.imgCotizar}
                          src={Cotizar}
                          alt="calendar"
                          sizes="100vw"
                        />
                      </Button>
                    </div>
                  </div>
                  <div className={styles.mobileImg}></div>
                </div> */}
                <Image
                  src={slide.image}
                  alt="slide"
                  layout="responsive"
                  style={{ width: "100%", height: "100%" }}
                />
              </Container>
            </div>
          ))}
          <div
            key={item.length}
            className={styles.slide}
            style={{
              transform: `translateX(${(item.length - currentSlide) * 100}%)`,
            }}
          >
            <BannerFigma />
          </div>
        </div>

        <div className={styles.dotContainer}>
          {item.map((_, index) => (
            <Button
              key={index}
              className={styles.dot}
              size="large"
              sx={{
                backgroundColor: index === currentSlide ? "#8B5CF6" : "#D1D5DB",
                minWidth: "unset",
                padding: 0,
              }}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
          <Button
            key={item.length}
            className={styles.dot}
            size="large"
            sx={{
              backgroundColor:
                item.length === currentSlide ? "#8B5CF6" : "#D1D5DB",
              minWidth: "unset",
              padding: 0,
            }}
            onClick={() => setCurrentSlide(item.length)}
          />
        </div>

        <IconButton
          onClick={prevSlide}
          className={`${styles.navButton} ${styles.navButtonLeft}`}
        >
          <ChevronLeft />
        </IconButton>

        <IconButton
          onClick={nextSlide}
          className={`${styles.navButton} ${styles.navButtonRight}`}
        >
          <ChevronRight />
        </IconButton>
      </Card>
    </div>
  );
};
