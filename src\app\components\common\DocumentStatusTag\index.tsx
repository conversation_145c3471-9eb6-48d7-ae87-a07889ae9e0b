import React from 'react';
import styled from 'styled-components';
import { DocumentStatus } from '@/types';
import { CheckCircle, Cancel, AccessTime } from '@mui/icons-material';

interface DocumentStatusTagProps {
  status: DocumentStatus;
  onClick?: () => void;
}

const StatusContainer = styled.div<{ status: DocumentStatus; clickable?: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: ${props => props.clickable ? 'pointer' : 'default'};
  transition: all 0.2s ease-in-out;
  
  ${props => {
    switch (props.status) {
      case 'Aprobado':
        return `
          background-color: #d1fae5;
          color: #065f46;
          border: 1px solid #10b981;
        `;
      case 'Rechazado':
        return `
          background-color: #fee2e2;
          color: #991b1b;
          border: 1px solid #ef4444;
        `;
      case 'Pendiente':
        return `
          background-color: #fef3c7;
          color: #92400e;
          border: 1px solid #f59e0b;
        `;
      default:
        return `
          background-color: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
        `;
    }
  }}

  ${props => props.clickable && `
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  `}
`;

const getStatusIcon = (status: DocumentStatus) => {
  switch (status) {
    case 'Aprobado':
      return <CheckCircle sx={{ fontSize: 16 }} />;
    case 'Rechazado':
      return <Cancel sx={{ fontSize: 16 }} />;
    case 'Pendiente':
      return <AccessTime sx={{ fontSize: 16 }} />;
    default:
      return null;
  }
};

const DocumentStatusTag: React.FC<DocumentStatusTagProps> = ({ status, onClick }) => {
  return (
    <StatusContainer
      status={status}
      clickable={!!onClick}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyPress={(e) => {
        if (onClick && (e.key === 'Enter' || e.key === ' ')) {
          onClick();
        }
      }}
    >
      {getStatusIcon(status)}
      {status}
    </StatusContainer>
  );
};

export default DocumentStatusTag;
