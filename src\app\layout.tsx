import type { Metadata } from 'next';
import { poppins } from '../app/fonts/fonts';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';

import './globals.css';
import StyledComponentsRegistry from './StyledProvider';
import Header from '@/infrastructure/components/home/<USER>';
import MainFooter from '@/infrastructure/components/home/<USER>';
import Script from 'next/script';
import { LoginProvider } from './LoginProvider';
import { ProductsProvider } from './ProductsContextProvider';
import Login from './components/modals/login/Login';
import { SnackbarProvider } from '@/context/SnackbarContext';

export const metadata: Metadata = {
  title: 'Wiki',
  description: 'Tu aliado en seguros',
  icons: [
    {
      rel: 'icon',
      type: 'image/svg',
      sizes: '32x32',
      url: '/favicon.svg',
    },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <head>
        {/* Google Tag Manager */}
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-W5GDM6WJ');
            `,
          }}
        />
        {/* End Google Tag Manager */}
      </head>
      <body className={`${poppins.variable}`} suppressHydrationWarning>
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-W5GDM6WJ"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          ></iframe>
        </noscript>
        {/* End Google Tag Manager (noscript) */}
        <StyledComponentsRegistry>
          <AppRouterCacheProvider options={{ enableCssLayer: true, key: 'app-router-cache' }}>
            <SnackbarProvider>
              <ProductsProvider>
                <LoginProvider>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      minHeight: '100vh',
                    }}
                  >
                    <Header />
                    <main style={{ flex: 1 }}>
                      <StyledComponentsRegistry>{children} </StyledComponentsRegistry>
                    </main>
                    <Login />

                    <Script src="https://delfi.euda.com.ar/api/scripts/fab-button?client=672a417351bc5c85ea9b4477&iframeWidth=400&iframeHeight=600" />

                    <footer className="w-full bg-[#9878B3] px-4 py-6">
                      <div className="max-w-7xl mx-auto">
                        <MainFooter />
                      </div>
                    </footer>
                  </div>
                </LoginProvider>
              </ProductsProvider>
            </SnackbarProvider>
          </AppRouterCacheProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
