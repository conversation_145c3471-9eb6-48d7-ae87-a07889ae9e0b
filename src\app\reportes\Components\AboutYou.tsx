import {
  FilterContainer,
  InputsContainer2,
  StyledInput,
} from "@/Styles/General.styles";
import { Checkbox, FormControlLabel } from "@mui/material";
import styled from "styled-components";

const AboutYou = () => {
  return (
    <StyledLayout>
      <div className="containerMain">
        <div className="headerContainer">
          <h1>Ahora un poco sobre ti</h1>
          <p>
            Necesitaremos algo de tu información personal para poder seguir con
            el reporte y poder abonar tu indemnización.
          </p>
        </div>

        <InputsContainer2>
          <FilterContainer name="Nombres (no los puedes cambiar)">
            <StyledInput
              placeholder="Introduce el Nombre del Proyecto"
              id="projectName"
              name="name"
              disabled
            />
          </FilterContainer>
          <FilterContainer name="Apellido Paterno (no lo puedes cambiar)">
            <StyledInput
              placeholder="Introduce el Nombre del Proyecto"
              id="projectName"
              name="name"
              disabled
            />
          </FilterContainer>
          <FilterContainer name="Apellido Materno (no lo puedes cambiar)">
            <StyledInput
              placeholder="Introduce el Nombre del Proyecto"
              id="projectName"
              name="name"
              disabled
            />
          </FilterContainer>
          <FilterContainer name="Fecha de Nacimiento (no lo puedes cambiar)">
            <StyledInput
              placeholder="Introduce el Nombre del Proyecto"
              id="projectName"
              name="name"
              disabled
            />
          </FilterContainer>
          <FilterContainer name="Email (no lo puedes cambiar)">
            <StyledInput
              placeholder="Introduce el Nombre del Proyecto"
              id="projectName"
              name="name"
              disabled
            />
          </FilterContainer>
          <FilterContainer name="Género (no lo puedes cambiar)">
            <div style={{ display: "flex", gap: "10px", flexDirection: "row" }}>
              <FormControlLabel
                control={
                  <Checkbox
                    value="femenino"
                    checked={true}
                    disabled
                    sx={{
                      color: "#10265f",
                      "&.Mui-checked": { color: "#10265f" },
                    }}
                  />
                }
                label="Femenino"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    value="masculino"
                    disabled
                    sx={{
                      color: "#10265f",
                      "&.Mui-checked": { color: "#10265f" },
                    }}
                  />
                }
                label="Masculino"
              />
            </div>
          </FilterContainer>
          <FilterContainer name="Calle">
            <StyledInput placeholder="Calle" id="projectName" name="name" />
          </FilterContainer>
          <FilterContainer name="Número Exterior">
            <StyledInput
              placeholder="No. Exterior"
              id="projectName"
              name="name"
            />
          </FilterContainer>
          <FilterContainer name="Número Interior">
            <StyledInput
              placeholder="No. Interior"
              id="projectName"
              name="name"
            />
          </FilterContainer>
          <FilterContainer name="Código Postal">
            <StyledInput
              placeholder="Código Postal"
              id="projectName"
              name="name"
            />
          </FilterContainer>
          <FilterContainer name="Colonia">
            <StyledInput placeholder="Colonia" id="projectName" name="name" />
          </FilterContainer>
          <FilterContainer name="Municipio">
            <StyledInput placeholder="Municipio" id="projectName" name="name" />
          </FilterContainer>
          <FilterContainer name="CURP">
            <StyledInput placeholder="CURP" id="projectName" name="name" />
          </FilterContainer>
          <FilterContainer name="RFC">
            <StyledInput placeholder="RFC" id="projectName" name="name" />
          </FilterContainer>
          <FilterContainer name="CLABE">
            <StyledInput placeholder="CLABE" id="projectName" name="name" />
          </FilterContainer>
        </InputsContainer2>
      </div>
    </StyledLayout>
  );
};

export default AboutYou;

const StyledLayout = styled.div`
  background-color: #fdfaff;
  width: 100%;
  min-height: 70vh;

  .containerMain {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
    align-items: flex-start;
    justify-content: start;
    max-width: 120rem;
    margin: 0 auto;
    padding: 46px 60px;
    .headerContainer {
      display: flex;
      flex-direction: column;
      width: 100%;
    }
    h1 {
      color: var(--Gris-Obscuro, #333);
      font-family: Poppins;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    h2 {
      color: var(--Gris-Obscuro, #333);

      /* Wiki/Common/Body */
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
`;
