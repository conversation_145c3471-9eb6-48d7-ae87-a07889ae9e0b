"use client";
import { Typography } from "@mui/material";
import { product } from "@/infrastructure/models/products.model";
import Image from "next/image";
import styled from "styled-components";
import { useRouter } from "next/navigation";

export const ProductCard: React.FC<{ product: product }> = ({ product }) => {
  const router = useRouter();

  const navigate = (product: product) => {
    router.push(product.path);
  };

  return (
    <StyledProductCard
      className="scroll-animate"
      color={product.color}
      onClick={() => navigate(product)}
    >
      <div className="descriptionContainer">
        <Image
          className="img"
          width={211}
          height={211}
          alt={product.name}
          src={product.image}
        />
      </div>
      <div className="endSection">
        <Typography className="title">{product.name.toLowerCase()}</Typography>
        <Typography className="description">{product.description}</Typography>
      </div>
    </StyledProductCard>
  );
};

const StyledProductCard = styled.div<{ color: string }>`
  width: 100%;
  max-width: 270px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  min-width: 340px;
  max-width: 340px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
  border-radius: 16px;
  transition: all 0.3s ease;
  padding: 30px;
  width: 100%;
  max-width: 370px;
  height: 100%;
  top: 0;
  /* position: absolute; */

  /* overflow: hidden; */
  /* justify-content: space-between; */

  .endSection {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    justify-content: center;
    .description {
      color: #7d7d7d;
      text-align: center;
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .title {
      color: #333;
      text-align: center;
      font-family: Poppins;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-transform: capitalize;
    }
  }

  @media screen and (min-width: 769px) {
    transition: all 0.5s ease;
    width: 270px;
    height: 305px;
    padding: 18px;
  }

  .descriptionContainer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 12px;
    margin: 0 0 16px;
    align-items: center;

    @media screen and (max-width: 768px) {
      margin: 0;
      flex-direction: column;
    }

    .img {
      display: block;
      aspect-ratio: 1 / 1 !important;
      position: relative !important;
      transition: all 0.3s ease;
      @media screen and (max-width: 768px) {
        margin: 8px 0 0;
      }
    }
  }
  .price {
    font-family: Poppins;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    /* identical to box height */
    text-align: center;
    /* Acento 3 */
    color: #273270;
    margin: 5px 0 0;
    width: 100%;
  }
  &:hover {
    img {
      filter: drop-shadow(0px 0px 30px ${(props) => props.color});
    }
  }
`;
