// Número de teléfono utilizado para WhatsApp y llamadas
const phoneNumber = '559126460';

/**
 * Función para iniciar una conversación de WhatsApp.
 * Abre un enlace de WhatsApp en una nueva pestaña con un mensaje predefinido.
 */
export const handleWhatsAppClick = (isLogged: boolean, name?: string) => {
  const phoneNumberWhats = isLogged ? `${phoneNumber}1` : `${phoneNumber}0`;
  const message = isLogged ? `Hola soy ${name} necesito ayuda con...` : 'Hola wiki, tengo dudas ayuda con...'; // Mensaje predefinido
  const url = `https://wa.me/${phoneNumberWhats}?text=${encodeURIComponent(message)}`;
  window.open(url, '_blank'); // Abre el enlace en una nueva pestaña
};

/**
 * Función para manejar la interacción con el número de teléfono.
 * Si el usuario está en un dispositivo móvil, inicia una llamada.
 * Si el usuario está en un escritorio, copia el número al portapapeles.
 */
export const handlePhoneClick = (showSnackbar: (message: string, severity: 'success' | 'error') => void, isLogged: boolean) => {
  const phoneNumberTel = isLogged ? `${phoneNumber}1` : `${phoneNumber}0`;
  if (/Mobi|Android/i.test(navigator.userAgent)) {
    // Si es un dispositivo móvil, inicia la llamada
    window.location.href = `tel:+${phoneNumber}`;
  } else {
    // Verifica si la API clipboard está disponible
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(`+${phoneNumberTel}`)
        .then(() => {
          showSnackbar(`El número se ha copiado al portapapeles.`, 'success');
        })
        .catch((err) => {
          console.error('Error al copiar al portapapeles:', err);
          showSnackbar(`No se pudo copiar el número al portapapeles. Por favor, cópialo manualmente: ${phoneNumberTel}`, 'error');
        });
    } else {
      // Si la API clipboard no está disponible, muestra el número en un mensaje
      showSnackbar(`Tu navegador no soporta la función de copiar al portapapeles. Por favor, copia este número manualmente: ${phoneNumber}`, 'error');
    }
  }
};
