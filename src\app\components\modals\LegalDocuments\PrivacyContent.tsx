import React from "react";
import { SectionTitle, Content } from "./LegalModal.styles";
import { EmptyState } from "@/app/components/common";

interface PrivacySection {
  id: string | number;
  tituloDeSeccion?: string;
  contenidoDeSeccion: string;
}

interface PrivacyContentProps {
  privacySections: PrivacySection[];
  loading: boolean;
}

const PrivacyContent: React.FC<PrivacyContentProps> = ({ privacySections, loading }) => {
  if (loading) return <p>Cargando...</p>;

  if (!privacySections || privacySections.length === 0) {
    return <EmptyState message="No hay información disponible en este momento." />;
  }

  return (
    <>
      {privacySections.map((section) => (
        <div key={`privacy-${section.id}`}>
          {section.tituloDeSeccion && (
            <SectionTitle>{section.tituloDeSeccion}</SectionTitle>
          )}
          <Content>
            {section.contenidoDeSeccion
              .split("\n\n")
              .map((paragraph, index) => (
                <p
                  key={index}
                  dangerouslySetInnerHTML={{
                    __html: paragraph
                      .replace(/\n/g, "<br/>")
                      .replace(/•/g, "•&nbsp;"),
                  }}
                />
              ))}
          </Content>
        </div>
      ))}
    </>
  );
};

export default PrivacyContent;
