"use client";
import { useProducts } from "@/app/ProductsContextProvider";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Product, Product as ProductType } from "@/app/product/products.types";
import ProductPage from "@/app/product/layoutProduct";
import {
  APICobertura,
  APIInsuredAmount,
} from "@/app/product/apiProducts.types";
import Page404 from "@/app/[...not-found]/page";

const ProductPageData = () => {
  const { id } = useParams();

  const [dataProduct, setDataProduct] = useState<ProductType>();

  const { getProductById, loading } = useProducts();

  // Lógica para obtener los datos del producto según el id
  // Por ejemplo, buscar en un objeto o array local

  const product = id
    ? getProductById(parseFloat(Array.isArray(id) ? id[0] : id))
    : null;

  /*function formatNumber(input: string): string {
    // Reemplazar el punto por una coma temporalmente para evitar conflictos con el parseo
    const normalized = input.replace(/\./g, "").replace(/,/, ".");

    // Convertir el string a número
    const numberValue = parseFloat(normalized);

    // Formatear el número con la convención de EE.UU.
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numberValue);
  }
*/
  useEffect(() => {
    if (product !== null && product !== undefined) {
      const adaptedProduct: Product = {
        title: product.name, // mapeamos "name" a "title"
        color: product.color,
        image: product.secondaryImage, // usamos "secondaryImage" como imagen principal
        secondaryImage: product.secondaryImage, // usamos "secondaryImage" como imagen secundaria
        type: product.type,
        description: product.generalCoverage, // usamos "generalCoverage" para la descripción
        features: product.coberturas.map((feature: APICobertura) => ({
          title: feature.name,
          description: feature.description,
          icon: feature.icon,
        })),
        subtitle: product.subtitle,
        id: product.id.toString(), // aseguramos que el id sea un string
        subtitleDescription: product.additionalDetails, // mapeamos "additionalDetails" a "subtitleDescription"
        CovergaeOptions: product
          ? product.planesProductos.map((insured: APIInsuredAmount) => ({
              id: insured.id,
              deducible: insured.deductible,
              precio: insured.total,
              suma: insured.insuredAmount,
              // No se indica meses sin intereses en la respuesta, se asigna false por defecto
              mesesSinIntereses: false,
              // Mapeamos las coberturas a un arreglo de CoverageFeature:
              features: product.coberturas.map((cobertura: APICobertura) => ({
                concepto: cobertura.name,
                sumaAsegurada: cobertura.percentageInsuredAmount,
                incluido: cobertura.percentageInsuredAmount,
                icon: cobertura.icon ? cobertura.icon.url : undefined,
              })),
            }))
          : [],
      };
      console.log("adaptedProduct", adaptedProduct, product);
      setDataProduct(adaptedProduct);
    }
  }, [product]);

  return (
    <div>
      {loading ? (
        "loading..."
      ) : dataProduct ? (
        <ProductPage product={dataProduct} />
      ) : (
        <Page404 />
      )}
    </div>
  );
};

export default ProductPageData;
