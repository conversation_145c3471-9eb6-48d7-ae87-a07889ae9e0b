// Definiciones de los tipos para el producto adaptado
export interface Product {
  title: string;
  color: string;
  image: string;
  secondaryImage: string;
  type: string;
  description: string;
  features: Feature[];
  subtitle: string;
  subtitleDescription: string;
  CovergaeOptions: CoverageOption[];
}

export interface Feature {
  title: string;
  description: string;
  icon: { nombre: string; url: string }[];
}

export interface CoverageOption {
  id: number;
  deducible: string;
  precio: string;
  suma: string;
  mesesSinIntereses: boolean;
  features: CoverageFeature[];
}

export interface CoverageFeature {
  concepto: string;
  sumaAsegurada: string;
  incluido: string;
  icon: { nombre: string; url: string }[];
}

// Definiciones de tipos para la respuesta de la API
export interface APICobertura {
  id: number;
  name: string;
  icon: { nombre: string; url: string };
  description: string;
  percentageInsuredAmount: string;
}

export interface APIInsuredAmount {
  id: number;
  insuredAmount: string;
  netPremium: string;
  deductible: string;
  iva: string;
  total: string;
}

export interface APIProduct {
  id: number;
  name: string;
  idZurich: number;
  type: string;
  generalCoverage: string;
  subtitle: string;
  status: string;
  additionalDetails: string;
  mainImage: string;
  secondaryImage: string;
  coverageName: string;
  coverageDescription: string;
  color: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  coberturas: APICobertura[];
  planesProductos: APIInsuredAmount[];
}
