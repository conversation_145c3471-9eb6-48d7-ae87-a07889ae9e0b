/* Estilo para el componente Alert */
.customAlert {
  display: flex;
  align-items: center;
  border-radius: 5px;
  font-size: 1rem;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
  padding: 15px 16px !important;
  font-weight: 400;
  gap: 8px;
}

/* Estilo para el estado de éxito */
.customAlert.success {
  color: #72e128;
  background-color: #eefbe5;
}

/* Estilo para el estado de error */
.customAlert.error {
  color: #ff4d49;
  background-color: #fdeae9;
}
