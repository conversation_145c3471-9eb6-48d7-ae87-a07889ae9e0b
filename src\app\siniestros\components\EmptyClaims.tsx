"use client";

import Image from "next/image";
import { Button } from "@mui/material";
import { useRouter } from "next/navigation";

const EmptyClaims = () => {
  const router = useRouter();

  return ( 
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        textAlign: "center",
        padding: "1rem",
        marginTop: "3rem",
        marginBottom: "3rem",
      }}
    >
      <Image
        src="/web/img/no-claims.png"
        alt="Sin siniestros"
        width={300}
        height={300}
        priority
      />
      <p
        style={{
          color: "#757575",
          marginTop: "2rem",
          marginBottom: "2rem",
          fontSize: "1.1rem",
        }}
      >
        No has reportado ningún siniestro
      </p>
      <div style={{ display: "flex", justifyContent: "center", width: "100%" }}>
        <Button
          variant="contained"
          onClick={() => router.push('/siniestros/select')}
          sx={{
            backgroundColor: "#10265F",
            borderRadius: "100px",
            textTransform: "none",
            padding: "10px 35px !important",
            fontSize: "0.9rem",
            fontWeight: 500,
            "&:hover": {
              backgroundColor: "#51519b",
            },
          }}
        >
          Reportar un siniestro
        </Button>
      </div>
    </div>
  );
};

export default EmptyClaims;
