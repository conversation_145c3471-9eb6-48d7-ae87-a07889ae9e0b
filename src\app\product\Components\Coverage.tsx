"use client";
import React, { FunctionComponent } from "react";
import styled from "styled-components";
import { Product } from "../products.types";
import DoublePalm from "/public/elements/doublePalm.svg";
import Image from "next/image";

interface CoverageProps {
  product: Product;
}

const Coverage: FunctionComponent<CoverageProps> = ({ product }) => {
  return (
    <Wrapper className="card-elements" id="info">
      <Title>{product.subtitle}</Title>
      <StyledImage1 src={DoublePalm} alt="doublePalm" />
      <Subtitle>{product.subtitleDescription}</Subtitle>
      <SectionTitle>¿Qué incluye el seguro de {product.title}?</SectionTitle>
      <SectionSubtitle>
        Estas son las coberturas que puedes tener con {product.title}
      </SectionSubtitle>
      <CardsContainer>
        {product.features.map((item, index) => (
          <Card key={item.title + index} className="scroll-animate">
            <CardTitle>{item.title}</CardTitle>
            <CardContent>{item.description}</CardContent>
          </Card>
        ))}
      </CardsContainer>
      <DownloadLink href="#!">Descarga los detalles de la póliza</DownloadLink>
    </Wrapper>
  );
};

export default Coverage;

const StyledImage1 = styled(Image)`
  position: absolute;
  top: 160px;
  min-width: 150px;
  width: 10vw;
  height: auto;
  right: -4vw;
  transform: rotateY(180deg);
  @media (max-width: 1024px) {
    display: none;
  }
`;

const Wrapper = styled.div`
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
`;

const Title = styled.h3`
  color: #5959a3;
  text-align: center;
  font-family: Poppins;
  font-size: 32px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin: 0 0 24px;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const Subtitle = styled.p`
  color: #828282;
  text-align: center;
  font-family: "Poppins";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  max-width: 800px;
  margin: 0 auto 24px;

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
`;

const SectionTitle = styled.h3`
  color: #5959a3;
  text-align: center;
  font-family: Poppins;
  font-size: 38px;
  font-style: normal;
  font-weight: 400;
  margin: 64px 0 0;

  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
`;

const SectionSubtitle = styled.p`
  color: #273270;
  text-align: center;
  font-family: "Poppins";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 137.5% */

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
`;

const CardsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 35px 0 0;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const Card = styled.div`
  display: flex;
  width: 100%;
  max-width: 372px;
  min-height: 187px;
  padding: 24px 32px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  border-radius: 20px;
  border: 1px solid var(--Secondary-Blossom, #af8cc0);
  background: var(--Background-White, #fff);
  box-shadow: 0px 25px 7px 0px rgba(175, 140, 192, 0),
    0px 16px 6px 0px rgba(175, 140, 192, 0.02),
    0px 9px 5px 0px rgba(175, 140, 192, 0.08),
    0px 4px 4px 0px rgba(175, 140, 192, 0.14),
    0px 1px 2px 0px rgba(175, 140, 192, 0.16);

  @media (max-width: 768px) {
    max-width: 100%;
  }
`;

const CardTitle = styled.h4`
  color: #5959a3;
  text-align: center;

  /* Wiki/Common/Small Title */
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const CardContent = styled.ul`
  color: #828282;
  text-align: justify;

  /* Wiki/Common/Small Body */
  font-family: "Poppins";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  @media (max-width: 768px) {
    font-size: 0.85rem;
  }
`;

const DownloadLink = styled.a`
  display: inline-block;
  margin-top: 2rem;
  font-size: 0.9rem;
  color: #6f3dc8;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.3s ease;
  position: relative;

  &:hover {
    color: #8854c6;
  }

  @media (max-width: 768px) {
    font-size: 0.85rem;
  }
  @media (min-width: 769px) {
    right: -50%;
    transform: translateX(-100%);
  }
`;
