import React from 'react';
import * as S from './Table.styles';

interface Column<T> {
  header: string;
  accessor: keyof T;
  render?: (value: T[keyof T], item: T) => React.ReactNode;
}

interface TableProps<T> {
  data: T[];
  columns: Column<T>[];
  emptyMessage?: string;
  onRowClick?: (item: T) => void;
  'aria-label': string;
  headerBgColor?: string;
  headerText?: boolean;
  contentAlignment?: 'center' | 'left' | 'right';
}

export function Table<T extends object>({
  data,
  columns,
  emptyMessage = 'No hay datos disponibles',
  onRowClick,
  'aria-label': ariaLabel,
  headerBgColor = '#5959a3',
  headerText = false,
  contentAlignment = 'center',
}: TableProps<T>) {
  if (data.length === 0) {
    return (
      <S.TableWrapper>
        <div role="alert">{emptyMessage}</div>
      </S.TableWrapper>
    );
  }

  return (
    <S.TableWrapper>
      <S.Table role="table" aria-label={ariaLabel}>
        <S.TableHead $headerBgColor={headerBgColor} $headerText={headerText}>
          {columns.map((column) => (
            <S.TableHeaderCell key={String(column.accessor)} $headerBgColor={headerBgColor} $headerText={headerText} $contentAlignment={contentAlignment}>
              {column.header}
            </S.TableHeaderCell>
          ))}
        </S.TableHead>
        <S.TableBody>
          {data.map((item, index) => (
            <S.TableRow
              key={index}
              onClick={() => onRowClick?.(item)}
              style={{ cursor: onRowClick ? 'pointer' : 'default' }}
              role="row"
            >
              {columns.map((column) => (
                <S.TableCell key={String(column.accessor)} role="cell" $contentAlignment={contentAlignment}>
                  {column.render
                    ? column.render(item[column.accessor], item)
                    : String(item[column.accessor])}
                </S.TableCell>
              ))}
            </S.TableRow>
          ))}
        </S.TableBody>
      </S.Table>
    </S.TableWrapper>
  );
}

export * from './Table.styles';
