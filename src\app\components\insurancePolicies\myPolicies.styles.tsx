import styled from "styled-components";
import HelpIcon from "@mui/icons-material/Help";

export const Container = styled.section`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  flex: 1 0 0;
  gap: 30px;
  align-self: stretch;
  margin-bottom: 40px;
`;

// Título principal
export const Title = styled.p`
  padding: 0px 60px;
  color: #10265f;
  text-align: center;
  font-family: Poppins;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  @media (max-width: 980px) {
    padding: 0px 32px;
    font-size: 30px;
  }
`;

// Tabla contenedora
export const TableWrapper = styled.div`
  display: flex;
  padding: 0px 70px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  overflow-x: auto;

  @media (max-width: 980px) {
    padding: 0 36px;
  }
`;

// Tabla
export const Table = styled.table`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 6px;
  align-self: stretch;
  @media (max-width: 980px) {
    align-self: auto;
  }
`;

// Encabezados de tabla
export const TableHead = styled.thead`
  background-color: #5959a3;
  color: white;
  display: flex;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 10px;
`;

export const TableHeaderCell = styled.th`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  padding: 10px 12px;
  border-radius: 10px;
  background: #5959a3;

  color: #fff;
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;

  @media (max-width: 980px) {
    font-size: 14px;
  }
`;

// Filas y celdas
export const TableBody = styled.tbody`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
`;

export const TableRow = styled.tr`
  display: flex;
  align-items: flex-start;
  align-self: stretch;
  color: #6d6d6d;
  //   &:nth-child(even) {
  //     background-color: #f9f9f9;
  //   }
`;

export const TableCell = styled.td`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0);
  padding: 10px 12px;

  color: #6d6d6d;
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  @media (max-width: 980px) {
    font-size: 14px;
  }
`;

// Iconos de acción
export const ActionIcon = styled.button`
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  color: #10265f;
  width: 18px;
  height: 18px;
  @media (max-width: 980px) {
    width: 16px;
    height: 16px;
  }
`;

// Icono de ayuda
export const HelpIconStyled = styled(HelpIcon)`
  padding: 0px 0.352px 0px 0.355px;
  width: 17px;
  height: 17px;
  color: #ffffff;
  margin-left: 6px;
  @media (max-width: 980px) {
    width: 15px;
    height: 15px;
    margin-left: 4px;
  }
`;
